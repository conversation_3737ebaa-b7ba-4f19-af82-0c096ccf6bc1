# frozen_string_literal: true

module Generators
  class EmployeeDataGenerator
    def self.generate(employee, columns, current_account = nil)
      new(employee, columns, current_account).generate
    end

    def initialize(employee, columns, current_account)
      @employee = employee
      @columns = columns
      @current_account = current_account
    end

    def generate
      current_user = User.kept.find(PaperTrail.request.whodunnit)
      hide_address_except_account_admins = @current_account.saas_json&.dig('ui', 'employees', 'hide_address_except_account_admins')
      show_address_for_this_user = true
      current_user_role = current_user.role&.name&.downcase

      columns.each do |column|
        if hide_address_except_account_admins == true && ['admin', 'account admin'].exclude?(current_user_role)
          is_board_member = (employee.position_name&.split(', ')&.map(&:downcase))&.include?('board member')
          show_address_for_this_user = is_board_member == true ? false : true
        end

        case column
        when 'employee' # TODO: Check and make changes for this header
          # For SSSA account, they wanted to show the name in 'last_name first_name middle_name' format.
          # Ref: https://app.asana.com/0/****************/****************
          # TODO: If the same customization needed in some other accounts, we may have to move this to account JSON.
          data << (@current_account.subdomain == 'sssa' ? employee.last_first_middle_name : employee.name)
        when 'employees.first_name'
          data << employee.first_name
        when 'employees.middle_name'
          data << employee.middle_name
        when 'employees.last_name'
          data << employee.last_name
        when 'employees.suffix'
          data << employee.suffix
        when 'office'
          data << employee.office_name || ''
        when 'department'
          data << department_data
        when 'section'
          data << employee.section_name || ''
        when 'pacf'
          data << employee.pacf_name || ''
        when 'grievance'
          data << employee.grievance_name || ''
        when 'discipline_setting'
          data << employee.discipline_setting_name || ''
        when 'employees.a_number'
          data << employee.a_number || ''
        when 'employees.shield_number'
          data << employee.shield_number || ''
        when 'employees.previous_shield_number'
          data << employee.previous_shield_number || ''
        when 'title'
          data << employee.title_name || ''
        when 'rank'
          data << employee.rank_name || ''
        when 'employment_status'
          data << employee.employment_status_name || ''
        when 'reports.employment_statuses_start_date'
          data << employment_status_start_date || ''
        when 'officer_status'
          data << employee.officer_status_name || ''
        when 'firearm_status'
          data << employee.firearm_status_name || ''
        when 'marital_status'
          data << employee.marital_status&.name || ''
        when 'unit'
          data << employee.unit&.name || ''
        when 'gender'
          data << employee.gender&.name || ''
        when 'position'
          data << employee.position_name || ''
        when 'employees.social_security_number'
          data << ssn_value
        when 'employees.member_since'
          data << member_since || ''
        when 'employees.birthday'
          data << birthday || ''
        when 'employees.age'
          data << employee_age(employee.birthday)
        when 'employees.email'
          data << employee.personal_email || ''
        when 'employees.work_email'
          data << employee.work_email || ''
        when 'employees.home_phone'
          data << employee.home_phone || ''
        when 'employees.cellphone'
          data << employee.cellphone || ''
        when 'employees.work_phone'
          data << employee.work_phone || ''
        when 'employees.staff_member'
          data << (employee.staff_member == true ? "Yes": "No") || ''
        when 'employees.prom_prov'
          data << prom_prov || ''
        when 'employees.prom_perm'
          data << prom_perm || ''
        when 'employees.start_date'
          data << start_date || ''
        when 'employees.member_start_date'
          data << member_start_date || ''
        when 'titles.title_code'
          data << employee.title_code || ''
        when 'affiliation'
          data << employee.affiliation&.name || ''
        when 'tour_of_duty'
          data << employee.tour_of_duty&.name || ''
        when 'platoon'
          data << employee.platoon&.name || ''
        when 'reports.dependent_count'
          data << employee.benefit_coverages.select('distinct(name)').count || 0
        when 'employees.payroll_id'
          data << employee.payroll_id || ''
        when 'employees.ncc_date'
          data << ncc_date || ''
        when 'employees.longevity_date'
          data << longevity_date || ''
        when 'employees.leave_progression_date'
          data << leave_progression_date || ''
        when 'employees.rdo'
          data << employee.rdo || ''
        when 'employees.primary_work_location'
          data << employee.primary_work_location || ''
        when 'employees.t_shirt_size'
          data << employee.t_shirt_size || ''
        when 'employees.app_downloaded'
          data << employee.app_downloaded || ''
        when 'reports.congress_district_id'
          data << get_legislative_detail('congress_member_details')
        when 'reports.assembly_district_id'
          data << get_legislative_detail('assembly_member_details')
        when 'reports.senate_district_id'
          data << get_legislative_detail('senate_member_details')
        when 'reports.council_district_id'
          data << get_legislative_detail('council_member_details')
        when 'employees.street'
          data << (show_address_for_this_user && employee.street || '')
        when 'employees.apartment'
          data << (show_address_for_this_user && employee.apartment || '')
        when 'employees.city'
          data << (show_address_for_this_user && employee.city || '')
        when 'employees.state'
          data << (show_address_for_this_user && employee.state || '')
        when 'employees.zipcode'
          data << (show_address_for_this_user && employee.zipcode || '')
        when 'employees.county'
          data << (show_address_for_this_user && employee.county || '')
        end

        year = Date.today.year
        if (column == "placard_#{year}" || column == "placard_#{year + 1}" || column == "placard_#{year-1}" || column == "placard_#{year-2}") ||
           (column == "placard_#{year - 1} - #{year}" || column == "placard_#{year} - #{year + 1}") &&
          current_account.saas_json.dig('schema', 'employees', 'placard_multiple').present? &&
          current_account.saas_json.dig('schema', 'employees', 'placard_multiple') == true
          if employee.placard_number.present?
            card_number = employee.placard_number.split(',')
            if (placard_numbers = current_account.saas_json.dig('schema', 'employees', 'placard_label')).presence
              data << card_number[0] || '' if column == "placard_#{placard_numbers[0]}"
              data << card_number[1] || '' if column == "placard_#{placard_numbers[1]}"
              data << card_number[2] || '' if (column == "placard_#{placard_numbers[2]}" && placard_numbers.count == 3)
            elsif current_account.saas_json.dig('schema', 'employees', 'placard_number_years_customize') == true
              data << card_number[0] || '' if column == "placard_#{year - 1} - #{year}"
              data << card_number[1] || '' if column == "placard_#{year} - #{year + 1}"
            elsif Date.today >= "01-04-#{year}".to_date
              data << card_number[0] || '' if column == "placard_#{year + 1}"
              data << card_number[1] || '' if column == "placard_#{year}"
              data << card_number[2] || '' if column == "placard_#{year - 1}"
            else
              data << card_number[0] || '' if column == "placard_#{year}"
              data << card_number[1] || '' if column == "placard_#{year - 1}"
              data << card_number[2] || '' if column == "placard_#{year - 2}"
            end
          end
        elsif column == 'employees.placard_number' || column == 'employees.placard_customize'
          data << employee.placard_number || ''
        end
      end

      data
    end

    private

    attr_reader :employee, :columns, :current_account

    def data
      @data ||= []
    end

    def birthday
      @birthday ||= if employee.birthday.present?
                      employee.birthday.strftime('%m-%d-%Y')
                    else
                      ''
                    end
    end

    def start_date
      @start_date ||= if employee.start_date.present?
                        employee.start_date.strftime('%m-%d-%Y')
                      else
                        ''
                      end
    end

    def member_start_date
      @member_start_date ||= if employee.member_start_date.present?
                               employee.member_start_date.strftime('%m-%d-%Y')
                             else
                               ''
                             end
    end

    def member_since
      @member_since ||= if employee.member_since.present?
                          employee.member_since.strftime('%m-%d-%Y')
                        else
                          ''
                        end
    end

    def prom_prov
      @prom_prov ||= if employee.prom_prov.present?
                       employee.prom_prov.strftime('%m-%d-%Y')
                     else
                       ''
                     end
    end

    def prom_perm
      @prom_perm ||= if employee.prom_perm.present?
                       employee.prom_perm.strftime('%m-%d-%Y')
                     else
                       ''
                     end
    end

    def ncc_date
      @ncc_date ||= if employee.ncc_date.present?
                       employee.ncc_date.strftime('%m-%d-%Y')
                     else
                       ''
                     end
    end

    def longevity_date
      @longevity_date ||= if employee.longevity_date.present?
                       employee.longevity_date.strftime('%m-%d-%Y')
                     else
                       ''
                     end
    end

    def leave_progression_date
      @leave_progression_date ||= if employee.leave_progression_date.present?
                       employee.leave_progression_date.strftime('%m-%d-%Y')
                     else
                       ''
                     end
    end

    def ssn_value
      employee_json = @current_account.saas_json['schema']['employees']
      allowed_length = employee_json['social_security_number_format'] if employee_json.present?
      allowed_length ||= '9' # 9 is the default SSN digit.

      if employee.social_security_number.present?
        allowed_length == '9' ? employee.social_security_number : 'XXX - XX - ' + employee.social_security_number.last(4)
      else
        ''
      end
    end

    def get_legislative_detail(column)
      if employee.legislative_address.present?
        employee.legislative_address.legislation_details[column.to_s]['district']
      else
        ''
      end
    end

    def employment_status_start_date
      start_dates = employee.employee_employment_statuses.pluck(:start_date).map { |s_date| s_date&.strftime('%m-%d-%Y') }
      start_dates&.join(', ')
    end

    def department_data
      if current_account.saas_json['schema']['reports']['department_with_section'].present?
        employee.employee_sections.map(&:department).map(&:name).join(", ")
      else
        employee.department_name || ''
      end
    end

    def employee_age(date)
      return '' if date.blank?

      age = Date.today.year - date.year
      age -= 1 if date.strftime('%m%d').to_i > Date.today.strftime('%m%d').to_i
      age
    end
  end
end
