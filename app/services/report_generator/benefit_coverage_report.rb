# frozen_string_literal: true

module ReportGenerator
  class BenefitCoverageReport < ApplicationReport
    private

    def report_type
      Report::ReportTypes::BENEFIT_COVERAGES
    end

    def filename
      @filename ||= "benefit_coverage-report-#{report.id}-#{report_created_at}.#{report_format}"
    end

    def generate_pdf
      return unless report_format == 'pdf'

      ReportGenerator::Pdf::BenefitCoveragePdf.create(employees, filename, filepath, params[:show_coverages], current_account)
    end

    def generate_xls
      return unless report_format == 'xls'

      ReportGenerator::Excel::BenefitCoverageExcel.create(employees, filepath, params[:show_coverages], current_account)
    end
  end
end
