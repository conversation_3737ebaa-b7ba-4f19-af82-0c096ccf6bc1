# frozen_string_literal: true

module ReportGenerator
  module Excel
    class BenefitCoverageExcel < ApplicationExcel
      def self.create(object, filepath, show_coverages, current_account, columns = [])
        new(object, filepath, show_coverages, current_account, columns).create
      end

      def initialize(object, filepath, show_coverages, current_account, columns)
        @object = object
        @filepath = filepath
        @show_coverages = show_coverages
        @current_account = current_account
        @columns = columns
      end

      private

      attr_reader :object, :filepath, :columns, :current_account, :show_coverages

      def create_worksheets
        ReportGenerator::Excel::Worksheets::BenefitCoverageWorksheet.create(workbook, object, show_coverages, current_account)
      end
    end
  end
end
