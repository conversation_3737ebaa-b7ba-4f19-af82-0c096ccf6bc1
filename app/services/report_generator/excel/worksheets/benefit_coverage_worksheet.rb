# frozen_string_literal: true

module ReportGenerator
  module Excel
    module Worksheets
      class BenefitCoverageWorksheet < ApplicationWorksheet
        def self.create(workbook, object, show_coverages, current_account = nil, columns = [])
          new(workbook, object, show_coverages, current_account, columns).create
        end

        def initialize(workbook, object, show_coverages, current_account, columns)
          @workbook = workbook
          @object = object
          @show_coverages = show_coverages
          @columns = columns
          @current_account = current_account
        end

        private

        attr_reader :workbook, :object, :columns, :current_account, :show_coverages

        def report_type
          Report::ReportTypes::BENEFIT_COVERAGES
        end

        def create_rows # rubocop:disable Metrics/MethodLength
          index = 0
          current_account = Account.find_by(subdomain: Apartment::Tenant.current)

          total_dependents_count = 0
          coverages = {}
          object.order_by_name.each do |employee|
            # each_with_index is slower in this case
            data = []
            employee_data = []
            employee_data << employee.name
            employee_data << employee.shield_number || '-'
            data << employee_data

            employee_benefits = employee.employee_benefits

            if employee_benefits.present?
              employee.employee_benefits.each_with_index do |employee_benefit, indx|
                if indx != 0
                  data = []
                  data << [nil, nil]
                end
                employee_benefit_data = []

                if current_account.saas_json.dig('schema', 'benefit_coverages', 'order_by_relationship').present? &&
                  current_account.saas_json.dig('schema', 'benefit_coverages', 'order_by_relationship') == true
                  benefit_coverages = employee.benefit_coverages.kept.order("relationship DESC, birthday ASC")
                else
                  benefit_coverages = employee_benefit.benefit_coverages.kept
                end

                employee_benefit_data << employee_benefit.name
                employee_benefit_data << DateFormatterHelper.format_report_date(employee_benefit.start_date)
                employee_benefit_data << DateFormatterHelper.format_report_date(employee_benefit.end_date)
                total_dependents_count += employee_benefit.benefit_coverages.where(discarded_at: nil).count
                employee_benefit.benefit_coverages.kept.group('LOWER(relationship)').order('LOWER(relationship) desc').count.each do |coverage, value|
                  coverages[coverage] ||= 0
                  coverages[coverage] += value
                end

                data << employee_benefit_data

                if (show_coverages == 'all' || show_coverages == 'true') && benefit_coverages.present?
                  benefit_coverages.each_with_index do |benefit_coverage, idx|
                    data = [nil, nil, nil, nil, nil] if idx != 0
                    benefit_coverage_data = []
                    benefit_coverage_data << benefit_coverage.name
                    benefit_coverage_data << benefit_coverage.relationship&.humanize&.titleize
                    benefit_coverage_data << benefit_coverage.social_security_number
                    benefit_coverage_data << DateFormatterHelper.format_report_date(benefit_coverage.birthday)

                    data << benefit_coverage_data
                    data.flatten!
                    index += 1
                    worksheet.insert_row(index, data)
                  end
                else
                  data.flatten!
                  index += 1
                  worksheet.insert_row(index, data)
                end
              end
            else
              data.flatten!
              index += 1
              worksheet.insert_row(index, data)
            end

          end
          if show_coverages == 'true' || show_coverages == 'all'
          index += 1
          worksheet.insert_row(index, [nil, nil, nil, nil, nil, nil, nil, nil, nil])
          coverages.each do |coverage, value|
            index += 1
            worksheet.insert_row(index, [
              nil, nil, nil, nil, nil,
              nil, nil, nil, nil,
              (coverage.blank? ? 'Total dependents without a Relationship defined :' : "Total #{coverage.titleize} :"),
              value
            ])
          end
          index += 1
          worksheet.insert_row(index, [
            nil, nil, nil, nil, nil,
            nil, nil, nil, nil,
            'Total Dependents :',
            total_dependents_count
          ])
          end
        end
      end
    end
  end
end
