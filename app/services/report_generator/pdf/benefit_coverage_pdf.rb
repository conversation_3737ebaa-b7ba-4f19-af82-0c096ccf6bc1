# frozen_string_literal: true

module ReportGenerator
  module Pdf
    class BenefitCoveragePdf < ApplicationPdf
      def self.create(object, filename, filepath, show_coverages = true, current_account = nil)
        new(object, filename, filepath, show_coverages, current_account).create
      end

      def initialize(object, filename, filepath, show_coverages, current_account)
        @object = object
        @filename = filename
        @filepath = filepath
        @show_coverages = show_coverages
        @current_account = current_account
      end

      private

      attr_reader :show_coverages

      def pdf # rubocop:disable Metrics/MethodLength
        ac.render_to_string(
          encoding: 'UTF-8',
          locals: {
            employees: object,
            show_coverages: show_coverages
          },
          assigns: {
            current_account: current_account
          },
          pdf: filename,
          template: 'templates/benefit_coverages/index_pdf',
          layout: 'pdf.html',
          padding: {
            top: 30
          },
          header: {
            html: {
              template: 'layouts/header',
              locals: {
                title: report_title_translation(Report::ReportTypes::BENEFIT_COVERAGES)
              },
              assigns: {
                current_account: current_account
              }
            },
            spacing: 6
          },
          footer: {
            font_size: 10,
            right: '[page] of [topage]'
          }
        )
      end
    end
  end
end
