# frozen_string_literal: true

module ReportGenerator
  class ApplicationReport
    include SaasTranslationsHelper

    ALLOWED_FORMATS = %w[pdf xls].freeze

    def initialize(params)
      current_subdomain = Apartment::Tenant.current
      # The account will be queried from 'public' schema
      # Ref: Check 'excluded_models' option of Apartment gem(https://github.com/influitive/apartment)
      @current_account = Account.find_by(subdomain: current_subdomain)
      @params = params
    end

    def generate
      report.format = report_format
      report.save

      generate_pdf
      generate_xls
      save_report
    end

    def report
      @report ||= Report.new(report_type: report_type, params: params)
    end

    private

    attr_reader :params
    attr_accessor :current_account

    def report_type
      raise NotImplementedError
    end

    def filename
      raise NotImplementedError
    end

    def filepath
      @filepath ||= "#{Rails.root}/#{filename}"
    end

    def workbook
      @workbook ||= Spreadsheet::Workbook.new
    end

    def report_format
      raise NotImplementedError unless ALLOWED_FORMATS.include?(params[:report_format])

      params[:report_format]
    end

    def report_created_at
      report.created_at.strftime('%Y-%m-%d')
    end

    def employees
      if report_type != 'notification_analytics'
        @employees ||= Generators::EmployeesQueryGenerator.generate(params, current_account)
      else
        @employees ||= NotificationTracker.includes(:employee, :notification).where(notification_id: params[:notification_id]).search_filter(params[:status], params[:type])
      end
    end

    def save_report
      report.file.attach(io: File.open(filepath), filename: filename)
      report.save

      File.delete(filepath)
    end
  end
end
