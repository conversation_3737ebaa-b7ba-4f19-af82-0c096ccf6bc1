module FileGenerator
  class DavisVisionFileGenerator
    def generate(account, benefit)
      Apartment::Tenant.switch!(account)
      current_account = Apartment::Tenant.current

      members = Employee.kept.includes(:contacts).order(:a_number)
      optical_benefit = Benefit.includes(employee_benefits: :benefit_coverages).where(name: benefit).first
      data_array = []
      member_count = 0
      dependent_count = 0

      space_filler_8 = format_text_length('', 8)
      language = format_text_length('ENGLISH', 11)

      # Subscriber Coverage Information
      rider_id = format_text_length('PR00002609', 10)

      # Subscriber PCP Information
      member_pcp_fill = format_text_length('', 300)
      dependent_pcp_fill = format_text_length('', 300)

      # Subscriber HIPAA Information (Customer Individual Rights)
      member_cir_fill = format_text_length('', 256)
      dependent_cir_fill = format_text_length('', 255)

      # Subscriber/Member Category Codes
      member_category_fill = format_text_length('', 839)
      dependent_category_fill = format_text_length('', 1030)

      # Header Row
      header = ['H', 'P', format_text_length('DAVIS VISION', 40), format_text_length('BTOBA', 50), format_date(Date.today), 'F', space_filler_8, ' ', format_text_length('', 1890)]

      data_array << header.join('')

      members.each do |member|
        emp_benefit = member.employee_benefits.where('benefit_id = ? and (end_date is null or end_date >= ?)', optical_benefit.id, Date.today).first
        next unless emp_benefit.present?

        employee_employment_status = member.employee_employment_statuses.kept.where('end_date is NULL OR end_date >= ?', Date.today) if member.present?
        employment_status_name = employee_employment_status.first.employment_status.name.downcase unless employee_employment_status.blank?

        if (employment_status_name.present? && %w(deceased promoted terminated inactive cobrainactive).exclude?(employment_status_name.downcase.delete(" "))) || employee_employment_status.blank?
          # Member Details
          member_count += 1
          member_data = ['S']

          member_ssn = member.social_security_number.delete('-')

          # Constructing Subscriber Identification
          m_subscriber_id = format_text_length(member_ssn, 12)
          m_alternate_id = format_text_length('', 12)
          m_dependent = format_text_length('', 2)
          m_subscriber_ssn = format_text_length(member_ssn, 9)
          m_first_name = format_text_length(format_hyphens(member.first_name), 25)
          m_middle_name = format_text_length(format_hyphens(member.middle_name), 1)
          m_last_name = format_text_length(format_hyphens(member.last_name), 35)
          m_gender = member.gender.present? ? format_gender_code(member.gender) : ' '
          m_dob = format_text_length(format_date(member.birthday), 8)
          m_handicapped = 'N'

          # Constructing Subscriber Demographics
          address_1 = format_text_length(member.street, 55)
          address_2 = format_text_length(member.apartment, 55)
          city = format_text_length(member.city, 30)
          state = format_text_length(member.state, 2)
          zipcode = format_text_length(member.zipcode, 15)
          country = 'USA'
          alternative_address = format_text_length('', 160)
          email_opt_out = member.email_opt_out ? 'Y' : 'N'
          personal_email = member.contacts.where(contact_type: 'email', contact_for: 'personal').first.value
          member_email = format_text_length(personal_email || '', 50)
          personal_phone = member.contacts.where(contact_type: 'phone', contact_for: 'personal').first.value
          member_phone = format_text_length(personal_phone&.delete(' ()-') || '', 10)

          address_data = [address_1, address_2, city, state, zipcode, country]

          # Constructing Subscriber Coverage Information
          plan_type = 'VIS'
          min_benefit_date = Date.parse('1/1/2021')
          benefit_start_date = emp_benefit.start_date.present? && emp_benefit.start_date > min_benefit_date ? emp_benefit.start_date : Date.parse('1/1/2021')
          start_date = format_text_length(format_date(benefit_start_date), 8)
          end_date = format_text_length(format_date(emp_benefit.end_date), 8)
          rider_contract_id = format_text_length(format_rider_contract_id(employment_status_name), 30)
          employment_status = format_text_length(format_employment_status_code(employment_status_name), 3)
          cobra_qualifying_event = format_text_length('', 1)
          original_subscriber_id = format_text_length('', 12)

          # Constructing Subscriber Affordable Care Act
          aptc_indicator = ' '
          premium_paid_through_date = format_text_length('', 8)

          # Injecting Subscriber Identification
          member_data << [m_subscriber_id, space_filler_8, m_alternate_id, format_text_length('', 6), m_dependent, m_subscriber_ssn, language, m_first_name, m_middle_name, m_last_name, m_gender, m_dob, m_handicapped]

          # Injecting Subscriber Demographics
          member_data << [address_data, alternative_address, email_opt_out, member_email, member_phone]

          # Injecting Subscriber Coverage Information
          member_data << [plan_type, start_date, end_date, rider_id, rider_contract_id, employment_status, cobra_qualifying_event, original_subscriber_id, space_filler_8]

          # Injecting Subscriber PCP Information
          member_data << [member_pcp_fill]

          # Injecting Subscriber HIPAA Information (Customer Individual Rights)
          member_data << [member_cir_fill]

          # Injecting Subscriber/Member Category Codes
          member_data << [member_category_fill]

          # Injecting Subscriber Affordable Care Act
          member_data << [aptc_indicator, premium_paid_through_date]

          member_data.flatten!

          data_array << member_data.join('')
          coverages = member.benefit_coverages.where('employee_benefit_id = ? and (expires_at is null or expires_at >= ?)', emp_benefit.id, Date.today)

          coverages.each do |coverage|
            dependent_count += 1

            # Dependent Details
            coverage_data = ['D']
            dependent_first_name = coverage.first_name
            dependent_last_name = coverage.last_name
            dependent_middle_name = dependent_last_name&.split&.count > 2 ? dependent_last_name.split.first : ''

            dependent_ssn = coverage.social_security_number.delete('-')

            # Constructing Dependent Demographics
            d_email_opt_out = format_text_length('', 1)
            d_email = format_text_length('', 50)
            d_phone = coverage.phone.present? ? format_text_length(coverage.phone.delete(' ()-'), 10) : member_phone

            # Constructing Dependent Identification
            d_dependent_id = format_text_length('', 12)
            d_dependent_ssn = format_text_length(dependent_ssn, 9)
            d_first_name = format_text_length(format_hyphens(dependent_first_name), 25)
            d_middle_name = format_text_length(format_hyphens(dependent_middle_name), 1)
            d_last_name = format_text_length(format_hyphens(dependent_last_name), 35)
            d_gender = coverage.gender.present? ? format_gender_code(coverage.gender) : ' '
            d_dob = coverage.birthday.present? ? format_date(coverage.birthday) : ''
            d_handicapped = coverage.relationship.present? && coverage.relationship.downcase.include?('disabled') ? 'Y' : 'N'
            d_student_status = ' '
            d_relationship_code = format_relationship(coverage.relationship&.downcase)

            # Injecting Dependent Identification
            coverage_data << [m_subscriber_id, space_filler_8, d_dependent_id, space_filler_8, d_dependent_ssn, language, d_first_name, d_middle_name, d_last_name, d_gender, d_dob, d_handicapped, d_student_status, d_relationship_code]

            # Injecting Dependent Demographics
            coverage_data << [address_data, d_email_opt_out, d_email, d_phone]

            # Injecting Dependent Coverage Information
            coverage_data << [start_date, end_date, rider_id, rider_contract_id, employment_status]

            # Injecting Dependent PCP Information
            coverage_data << [dependent_pcp_fill]

            # Injecting Dependent HIPAA Information (Customer Individual Rights)
            coverage_data << [dependent_cir_fill]

            # Injecting Dependent/Member Category Codes
            coverage_data << [dependent_category_fill]

            coverage_data.flatten!
            data_array << coverage_data.join('')
          end
        end
      end

      total = member_count + dependent_count
      trailer = ['T', format_text_length(total, 10), format_text_length(member_count, 10), format_text_length(dependent_count, 10), format_text_length('', 1969)]

      data_array << trailer.join('')

      filename = "V260324_601226_10F1331HM_#{Time.now.strftime('%Y%m%d_%H%M%S')}_001.txt"
      filepath ||= "#{Rails.root}/#{filename}"

      File.open(filepath, 'wb:UTF-8') do |file|
        data_array.each do |data|
          file.puts data
        end
      end

      filename # Returning Filename to upload it to FTP server
    end

    private

    def format_text_length(text, length)
      text.to_s.ljust(length, ' ').slice(0, length)
    end

    def format_date(date)
      date.strftime('%Y%m%d') rescue ''
    end

    def format_time(time)
      time.strftime('%H%M')
    end

    def format_gender_code(gender)
      if ["female", "f"].include?(gender.name.downcase)
        'F'
      elsif ["male", "m"].include?(gender.name.downcase)
        'M'
      else
        'U' # unknown
      end
    end

    def format_relationship(relationship)
      case relationship
      when 'spouse'
        '02'
      when 'ex-spouse'
        '03'
      when 'domestic partner'
        '04'
      when 'child', 'disabled_child'
        '05'
      when 'stepchild', 'step_child', 'disabled_step_child'
        '06'
      when 'grandchild'
        '07'
      when 'adopted child'
        '08'
      when 'child of a domestic partner'
        '09'
      else
        '10' # Other relationships
      end
    end

    def format_employment_status_code(name)
      if name.present?
        if name == 'active'
          'ACT'
        elsif name.include?('cobra')
          'COB'
        elsif name == 'retired'
          'RET'
        elsif name == 'long term disability'
          'LTD'
        else
          ''
        end
      else
        ''
      end
    end

    def format_rider_contract_id(status_name = '')
      return '' if status_name.blank?
      if status_name.downcase.include?('active')
        '10F100000331'
      elsif status_name.downcase.include?('cobra')
        '10F100000332'
      elsif status_name.downcase.include?('retired')
        '10F100000333'
      else
        ''
      end
    end

    def format_hyphens(value)
      value.gsub("-", " ").gsub("‐", " ").gsub("–", " ").gsub("—", " ") rescue ''
    end

  end
end
