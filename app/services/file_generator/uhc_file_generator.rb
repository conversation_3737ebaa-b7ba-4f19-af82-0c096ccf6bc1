# frozen_string_literal: true

module FileGenerator
  class UhcFileGenerator
    def generate(account, benefit)
      ## Switching tenants based on the account
      Apartment::Tenant.switch!(account)

      ## Assigning variables outside loop
      data_array = []
      total_no_of_records = 0

      dental_benefit = Benefit.kept.where(name: benefit).first
      employment_status_ids = EmploymentStatus.kept.where("lower(name) in (?)", ['active', 'part-time active', 'cobra', 'retired', 'out of state retiree', 'range instructor']).pluck(:id)
      condition = "employee_benefits.end_date is NULL or employee_benefits.end_date > '#{Date.today}' and employee_employment_statuses.employment_status_id in (#{employment_status_ids.join(', ')})"
      excluded_status = get_associate_status(account, benefit)
      associated_models = [:gender, :contacts, employee_employment_statuses: :employment_status, employee_benefits: [:benefit_coverages]]

      members = Employee.kept.includes(associated_models).order(:a_number).where(employee_benefits: { benefit_id: dental_benefit&.id }).send(:where, condition)

      members.each do |member|
        emp_benefits = member.employee_benefits.where('benefit_id = ? and (end_date is NULL or end_date >= ?)', dental_benefit&.id, Date.today)
        next unless emp_benefits.present?

        employee_employment_status = member.employee_employment_statuses.kept.where('end_date is NULL OR end_date >= ?', Date.today) if member.present?
        employment_status_name = employee_employment_status.first.employment_status&.name unless employee_employment_status.blank?
        next unless (employment_status_name.present? && excluded_status.exclude?(employment_status_name.downcase.delete(' '))) || employee_employment_status.blank?

        ## Assigning variables
        data = []
        coverage_data = []
        total_no_of_records += 1
        coverage_start_pos = 1200
        coverage_end_pos = 5699

        @member_ssn = member.social_security_number&.delete('-') || ''
        @member_ssn = "00#{@member_ssn}" if @member_ssn.present?

        ## Member Record Requirements
        get_member_record_requirements(member, data, true)

        ## Coverage Record Requirements
        coverages = member.benefit_coverages.where('employee_benefit_id in (?) and (expires_at is null or expires_at >= ?)', emp_benefits.ids, Date.today)
        coverages.each do |coverage|
          coverage_data << get_coverage_record_requirements(coverage, employment_status_name)
        end

        ## Adding the remaining positions based on the benefit_coverages count
        coverages_current_pos = coverage_start_pos + coverage_data.flatten.join.length
        space_to_be_added = coverage_end_pos - coverages_current_pos + 1
        data << coverage_data.flatten
        data << format_text_length('', space_to_be_added)

        data_array << data.flatten.join
      end

      ## Adding Header records at last, because it is having the total no of member counts
      data_array.unshift get_header_details(total_no_of_records).join
      uhc_file_generate(data_array)
    end

    def gsf_generate(account, benefit)
      Apartment::Tenant.switch!(account)
      employment_status_ids = EmploymentStatus.kept.where("lower(name) in (?)", ['active', 'part-time active', 'cobra', 'retired', 'out of state retiree', 'range instructor']).pluck(:id)
      condition = "employee_benefits.end_date is NULL or employee_benefits.end_date > '#{Date.today}' and employee_employment_statuses.employment_status_id in (#{employment_status_ids.join(', ')})"

      dental_benefit = Benefit.kept.where(name: benefit).first
      members = Employee.kept.includes(:gender, :contacts, employee_employment_statuses: :employment_status, employee_officer_statuses: :officer_status, employee_benefits: [:benefit_coverages]).order(:a_number).where(employee_benefits: { benefit_id: dental_benefit.id }).send(:where, condition)

      ReportGenerator::Excel::UhcExcel.create(members, '', @current_account, account, benefit)
    end

    def uhc_file_generate(data_array)
      filename = "COBANCF.U.#{Time.now.strftime('%Y%m%d%H%M')}.gsf"
      filepath ||= "#{Rails.root}/#{filename}"

      File.open(filepath, 'wb') do |file|
        data_array.each do |data|
          file.puts data
        end
      end

      filename
    end

    private

    def format_date(date)
      date.strftime('%Y%m%d') rescue ''
    end

    def format_text_length(text, length)
      text = '' if text.blank?
      text.ljust(length, ' ').slice(0, length)
    end

    def get_associate_status(account, benefit)
      %w[deceased pending terminated resigned transferred] if benefit == 'Dental' && account == 'cobanc'
    end

    def get_header_details(total_records)
      header_data = []
      header_data << format_text_length('', 19)
      header_data << format_text_length(total_records.to_s.rjust(8, '0'), 8)
      # header_data << format_text_length('', 28)
      header_data << format_text_length('|', 1)

      header_data
    end

    def get_valid_values_member_record(object, member_or_dependent)
      relationship_code = member_or_dependent == true ? '18' : format_relationship_code(object)
      employment_date = (member_or_dependent == true ? object.start_date : '') || ''
      employment_date = format_date(employment_date) if employment_date.present?
      middle_name = member_or_dependent == true ? object.middle_name : ''
      birthday = object.birthday.present? ? format_date(object.birthday) : ''
      home_phone_number = object.contacts.where(contact_for: 'home', contact_type: 'phone')&.first&.value&.remove!('(', ')', ' ', '-') || ''
      email_address = object.contacts.where(contact_for: 'personal', contact_type: 'email')&.first&.value || ''

      [relationship_code, employment_date, middle_name, birthday, home_phone_number, email_address]
    end

    def get_member_record_requirements(object, data, member_or_dependent)
      relationship_code, employment_date, middle_name, birthday,
        home_phone_number, email_address = get_valid_values_member_record(object, member_or_dependent) ## unpacking the values from the above method

      data << format_text_length('V1.30', 5) ## version_indicator
      data << format_text_length('COBANCF', 8) ## submission_group_id
      data << format_text_length('', 12) ## blank_filler1
      data << format_text_length(relationship_code, 2) ## relationship_code
      data << format_text_length(@member_ssn, 11) ## employee_id
      data << format_text_length('', 4) ## blank_filer2
      data << format_text_length(@member_ssn, 11) ## member_ssn
      data << format_text_length('', 4) ## blank_filer3
      data << format_text_length('', 15) ## former_ee_id
      data << format_text_length('', 11) ## personal_id
      data << format_text_length('', 5) ## blank_filer4
      data << format_text_length(employment_date, 8) ## employment_date
      data << format_text_length('', 12) ## blank_filer5
      data << format_text_length(object.last_name, 20) ## last_name
      data << format_text_length(object.first_name, 12) ## first_name
      data << format_text_length('', 8) ## blank_filer6
      data << format_text_length(middle_name, 1) ## middle_name
      data << format_text_length('', 19) ## blank_filer7
      data << format_text_length(birthday, 8) ## birthday
      data << format_text_length('', 12) ## blank_filer8
      data << format_text_length(format_gender_code(object.gender), 1) ## gender
      data << format_text_length(format_marital_status(object.marital_status), 1) ## marital_status
      data << format_text_length('', 1) ## cob_flag
      data << format_text_length('', 8) ## cob_start_date
      data << format_text_length('', 8) ## cob_stop_date
      data << format_text_length('', 1) ## custody_code
      data << format_text_length('', 3) ## blank_filer9
      data << format_text_length('ENG', 3) ## spoken_language
      data << get_members_address_details(object) ## members_address
      data << format_text_length(home_phone_number, 10) ## home_phone_number
      data << format_text_length('', 408) ## blank_filler10
      data << format_text_length(email_address, 100) ## email_address
      data << format_text_length('', 259) ## blank_filler11
    end

    def get_members_address_details(object)
      address_data = []
      address_data << format_text_length(object.street, 32) # permanent_street_address1
      address_data << format_text_length(object.apartment, 32) # permanent_street_address2
      address_data << format_text_length(object.city, 20) # permanent_city
      address_data << format_text_length(object.state, 2) # permanent_state
      address_data << format_text_length(object.zipcode, 15) # permanent_zipcode
      address_data << format_text_length('US', 2) # permanent_country_code
      address_data << format_text_length('', 1) # blank_filler10
      address_data << format_text_length(object.mailing_address&.street || '', 32) # mailing_street_address1
      address_data << format_text_length(object.mailing_address&.apartment || '', 32) # mailing_street_address2
      address_data << format_text_length(object.mailing_address&.city || '', 20) # mailing_city
      address_data << format_text_length(object.mailing_address&.state || '', 2) # mailing_state
      address_data << format_text_length(object.mailing_address&.zipcode || '', 15) # mailing_zipcode
      address_data << format_text_length('', 2) # mailing_country_code
      address_data << format_text_length('', 1) # blank_filler11

      address_data
    end

    def get_coverage_record_requirements(coverage, status_name)
      coverage_data = []
      start_date = coverage.employee_benefit.start_date || ''
      start_date = format_date(start_date) if start_date.present?
      end_date = coverage.expires_at || ''
      end_date = format_date(end_date) if end_date.present?
      structure_field3, structure_field4 = get_group_plan_reporting_code(status_name)
      coverage_data << format_text_length('DEN', 3) # coverage_type
      coverage_data << format_text_length(start_date, 8) # coverage_start_date
      coverage_data << format_text_length('', 12) # blank_filler
      coverage_data << format_text_length(end_date, 8) # coverage_end_date
      coverage_data << format_text_length('', 12) # blank_filler
      coverage_data << format_text_length('', 8) # coverage_paid_date
      coverage_data << format_text_length('', 12) # blank_filler
      coverage_data << format_text_length('0934413', 7) # coverage_structure_field
      coverage_data << format_text_length('', 3) # blank_filler
      coverage_data << format_text_length('0934413', 7) # coverage_structure_field2
      coverage_data << format_text_length('', 3) # blank_filler
      coverage_data << format_text_length(structure_field3, 4) # coverage_structure_field3
      coverage_data << format_text_length('', 6) # blank_filler
      coverage_data << format_text_length(structure_field4, 4) # coverage_structure_field4
      coverage_data << format_text_length('', 6) # blank_filler
      coverage_data << format_text_length('', 2) # coverage_structure_field5
      coverage_data << format_text_length('', 8) # blank_filler

      ## Splitting method to an another method to reduce no of lines
      split_method_coverage_record_requirements(coverage, coverage_data)
      coverage_data
    end

    def split_method_coverage_record_requirements(_coverage, coverage_data)
      coverage_data << format_text_length('', 10) # coverage_structure_field6
      coverage_data << format_text_length('', 10) # coverage_structure_field7
      coverage_data << format_text_length('', 8) # coverage_structure_field8
      coverage_data << format_text_length('', 7) # coverage_supplemental_life_amount
      coverage_data << format_text_length('', 3) # coverage_supplemental_life_benefit_factor
      coverage_data << format_text_length('', 7) # coverage_supplemental_ad_d_amount
      coverage_data << format_text_length('', 3) # coverage_supplemental_ad_d_benefit_factor
      coverage_data << format_text_length('', 2) # coverage_evidence_insurability_for_life
      coverage_data << format_text_length('', 3) # coverage1_members_covered
      coverage_data << format_text_length('', 2) # coverage1_cobra_indicator
      coverage_data << format_text_length('', 8) # coverage1_elig_util1
      coverage_data << format_text_length('', 8) # coverage1_elig_util2
      coverage_data << format_text_length('', 8) # coverage1_elig_util3
      coverage_data << format_text_length('', 20) # coverage1_elig_long_util_1
      coverage_data << format_text_length('', 7) # coverage1_life_flat_amount
      coverage_data << format_text_length('', 3) # coverage1_life_benefit_factor
      coverage_data << format_text_length('', 1) # coverage1_life_coverage_status
      coverage_data << format_text_length('', 1) # coverage1_rider_dep_flag
      coverage_data << format_text_length('', 1) # coverage1_rider_critical_illness
      coverage_data << format_text_length('', 75) # blank_filler
    end

    def format_relationship_code(object)
      relationship = object.relationship.downcase.gsub(' ', '_')
      case relationship
      when relationship == 'spouse'
        '01'
      when relationship == 'child'
        '19'
      when relationship == 'step_child'
        '09'
      else
        ''
      end
    end

    def format_gender_code(gender)
      return '' if gender.blank?

      if gender.name.downcase == 'female'
        'F'
      elsif gender.name.downcase == 'male'
        'M'
      else
        'U' # unknown
      end
    end

    def format_marital_status(marital_status)
      return '' if marital_status.blank?

      marital_status_hash = { single: 'I', divorced: 'D', married: 'M', separated: 'S', widowed: 'W',
                              domestic_partner: 'B', unknown: 'U' }
      marital_status_name = marital_status.name.downcase.gsub(' ', '_')

      marital_status_hash[marital_status_name.to_sym]
    end

    def get_group_plan_reporting_code(status_name)
      return ['', ''] if status_name.blank?

      status_name = status_name.downcase.delete(' ')
      if %w[active rangeinstructor].include?(status_name)
        plan_variation_code = '0001'
        reporting_code = '0001'
      elsif status_name == 'cobra'
        plan_variation_code = '0002'
        reporting_code = '0002'
      elsif status_name == 'part-timeactive'
        plan_variation_code = '0007'
        reporting_code = '0007'
      elsif %w[outofstateretiree retired].include?(status_name)
        plan_variation_code = '0003'
        reporting_code = '0003'
      end
      [plan_variation_code, reporting_code]
    end
  end
end
