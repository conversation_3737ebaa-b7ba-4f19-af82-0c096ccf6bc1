# frozen_string_literal: true

require 'net/http'
require 'json'
require 'openssl'

class LegislativeDetailService
  GEOCODIO_BASE_URL = 'https://api.geocod.io/v1.9/'

  def initialize(employee)
    @employee = employee
    @geocodio_api_key = ENV['GEOCODIO_API_KEY']
    @logger = Logger.new(STDOUT)
  end

  def fetch
    geocode_data
    details = build_legislative_details
    create_legislative_address(details, @employee)
  end

  private

  def build_legislative_details
    {
      county_details: county_details,
      congress_member_details: congress_member_details,
      senate_member_details: member_details('senate'),
      assembly_member_details: member_details('house'),
      council_member_details: empty_official('district'),
      comptroller_member_details: empty_official,
      attorney_member_details: empty_official,
      executive_member_details: empty_official
    }
  end

  def county_details
    name = fetch_data(geocode_data, ['results', 0, 'address_components', 'county']) || ''
    { county_name: name }
  end

  def congress_member_details
    congress_district_path = ['results', 0, 'fields', 'congressional_districts', 0]
    member_data = fetch_data(geocode_data, congress_district_path + ['current_legislators'])
    district_number = fetch_data(geocode_data, congress_district_path + ['district_number'])
    district_name = format_district_name(district_number, 'congress')
    representative = member_data&.find { |leg| leg['type'] == 'representative' }
    return empty_official('district') unless representative

    {
      "name": [
        fetch_data(representative, %w[bio first_name]),
        fetch_data(representative, %w[bio last_name])
      ].join(' '),
      "website": fetch_data(representative, %w[contact url]),
      "district": district_name
    }
  end

  def member_details(type = nil)
    member_path = ['results', 0, 'fields', 'state_legislative_districts', type, 0]

    member_data = fetch_data(geocode_data, member_path + ['current_legislators', 0])
    district_number = fetch_data(geocode_data, member_path + ['district_number'])
    district_name = format_district_name(district_number, type)

    return empty_official('district') unless member_data

    {
      "name": [
        fetch_data(member_data, %w[bio first_name]),
        fetch_data(member_data, %w[bio last_name])
      ].join(' '),
      "website": fetch_data(member_data, %w[contact url]),
      "district": district_name
    }
  end

  def geocode_data
    @geocode_data ||= begin
                        address = [@employee.street, @employee.city, @employee.state, @employee.zipcode].compact.join(', ')
                        encoded = URI.encode_www_form_component(address)
                        url = "#{GEOCODIO_BASE_URL}geocode?q=#{encoded}&api_key=#{@geocodio_api_key}&fields=cd,stateleg,county"
                        fetch_json(url, 'geocode')
                      end
  end

  def fetch_json(url, service_name)
    uri = URI(url)
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true
    http.verify_mode = Rails.env.development? ? OpenSSL::SSL::VERIFY_NONE : OpenSSL::SSL::VERIFY_PEER

    response = http.get(uri.request_uri)
    response_json = JSON.parse(response.body)
    response_message(response.code, response_json, service_name)

    Rails.logger.error(response.body) unless response.code == '200'
    raise StandardError, 'rate-limit-exceeded' if response.code == '429'

    response_json
  rescue => e
    raise StandardError, 'rate limit exceeded' if e.message == 'rate-limit-exceeded'

    Rails.logger.error("Fetch failed: #{e.message}")
    nil
  end

  def response_message(code, message, service_name)
    @response_message ||= {}
    @response_message[service_name] = message
    @response_code ||= code
  end

  def fetch_data(data, path)
    path.reduce(data) do |current, step|
      case current
      when Hash
        current[step]
      when Array
        step.is_a?(Integer) ? current[step] : nil
      else
        nil
      end
    end
  end

  def create_legislative_address(details, employee)

    record = LegislativeAddress.where(
      'lower(street) = ? and lower(city) = ? and lower(state) = ? and zipcode = ?',
      employee.street&.downcase,
      employee.city&.downcase,
      employee.state&.downcase,
      employee.zipcode
    ).order(updated_at: :desc).first_or_initialize

    existing_details = (record.legislation_details || {}).deep_symbolize_keys
    details = details.deep_symbolize_keys

    # Merge existing and new details, keeping non-blank existing values where new ones are blank
    merged_details = deep_merge_preserve(existing_details, details)

    record.assign_attributes(
      street: employee.street&.downcase,
      city: employee.city&.downcase,
      state: employee.state&.downcase,
      zipcode: employee.zipcode,
      legislation_details: merged_details,
      response_code: @response_code || '200',
      response_details: @response_message || 'ok'
    )

    record.save!
    record
  end

  def deep_merge_preserve(existing_details, new_hash)
    return new_hash unless existing_details.is_a?(Hash) && new_hash.is_a?(Hash)

    existing_details.merge(new_hash) do |_, old_val, new_val|
      if old_val.is_a?(Hash) && new_val.is_a?(Hash)
        deep_merge_preserve(old_val, new_val)
      else
        new_val.present? ? new_val : old_val
      end
    end
  end

  def empty_official(*keys)
    keys = %w[name website] + keys
    keys.index_with { '' }
  end

  def format_district_name(district, type)
    return '' if district.blank?

    ordinal = district.to_i.ordinalize
    if type == 'congress'
      "#{state_full_name}'s #{ordinal} Congressional District"
    elsif type == 'house'
      "#{state_full_name} Assembly District #{district}"
    else
      "#{state_full_name} State #{type.capitalize} District #{district}"
    end
  end

  def state_full_name
    { 'al' => 'Alabama', 'ak' => 'Alaska', 'az' => 'Arizona', 'ar' => 'Arkansas', 'ca' => 'California',
      'co' => 'Colorado', 'ct' => 'Connecticut', 'de' => 'Delaware', 'fl' => 'Florida', 'ga' => 'Georgia',
      'hi' => 'Hawaii', 'id' => 'Idaho', 'il' => 'Illinois', 'in' => 'Indiana', 'ia' => 'Iowa', 'ks' => 'Kansas',
      'ky' => 'Kentucky', 'la' => 'Louisiana', 'me' => 'Maine', 'md' => 'Maryland', 'ma' => 'Massachusetts',
      'mi' => 'Michigan', 'mn' => 'Minnesota', 'ms' => 'Mississippi', 'mo' => 'Missouri', 'mt' => 'Montana',
      'ne' => 'Nebraska', 'nv' => 'Nevada', 'nh' => 'New Hampshire', 'nj' => 'New Jersey', 'nm' => 'New Mexico',
      'ny' => 'New York', 'nc' => 'North Carolina', 'nd' => 'North Dakota', 'oh' => 'Ohio', 'ok' => 'Oklahoma',
      'or' => 'Oregon', 'pa' => 'Pennsylvania', 'ri' => 'Rhode Island', 'sc' => 'South Carolina', 'sd' => 'South Dakota',
      'tn' => 'Tennessee', 'tx' => 'Texas', 'ut' => 'Utah', 'vt' => 'Vermont', 'va' => 'Virginia', 'wa' => 'Washington',
      'wv' => 'West Virginia', 'wi' => 'Wisconsin', 'wy' => 'Wyoming' }[@employee.state&.downcase] || @employee.state
  end
end
