class EmployeeDisciplineStep < ApplicationRecord
  delegate :name, to: :discipline_status, allow_nil: true

  enum step: { step_1: 0, step_2: 1, step_3: 2, arbritration: 3, oath: 4, inquiry: 5, arbitration_2: 6 }

  # ================== Associations ====================================================================================
  belongs_to :employee_discipline_setting
  belongs_to :discipline_status, optional: true
  has_many_attached :files

  # ================== Validations =====================================================================================
  validates :files, blob: { content_type: ACCEPTABLE_CONTENT_TYPES, size_range: 1..100.megabytes }
  validates :step, uniqueness: { conditions: -> { where(discarded_at: nil) }, scope: [:employee_discipline_setting_id],
                                 message: 'Same step already exists' }
  # To not to allow updating 'step' in update
  validates :step, inclusion: { in: ->(i) { [i.step_was] }, message: 'Step can\'t be updated' }, on: :update
  validate :validate_step_name
  validate :validate_total_steps, on: :create

  def validate_total_steps
    return if errors.any?

    total_steps = employee_discipline_setting.employee_discipline_steps.count
    return if total_steps <= discipline_settings_steps.length

    errors.add(:step, 'Exceeded allowed steps')
  end

  def validate_step_name
    return if errors.any?

    return if discipline_settings_steps.include?(step)

    errors.add(:step, 'Invalid step')
  end

  def discipline_settings_steps
    @discipline_setting_tabs ||= current_account.saas_json.dig('ui', 'employees', 'discipline_settings', 'tabs') - ['discipline_settings']
  end

  def arbitrator
    Employee.unscoped.find_by(id: arbitrator_id)
  end
end
