# frozen_string_literal: true

class Report < ApplicationRecord
  module ReportTypes
    BENEFITS = 'benefits'
    BENEFIT_COVERAGES = 'benefit_coverages'
    PACFS = 'pacfs'
    EMPLOYEE_DELEGATE_ASSIGNMENT = 'employee_delegate_assignment'
    LODI = 'lodi'
    SICK_BANK = 'sick_bank'
    SINGLE_EMPLOYEE = 'single_employee'
    UNION_MEETINGS = 'union_meetings'
    DISCIPLINES = 'disciplines'
    GRIEVANCES = 'grievances'
    JANUS = 'janus'
    LIFE_INSURANCE = 'life_insurances'
    GVS = 'gvs'
    ASO = 'aso'
    VISION_SCREENING = 'vision_screening'
    WORKERS_COMP = 'workers_comp'
    NOTIFICATION_ANALYTICS = 'notification_analytics'
    BENEFIT_COVERAGES_EXPIRATION = 'benefit_coverage_expiration'
    BENEFICIARY = 'beneficiary'
    UHC = 'uhc'
    MEMBER_COUNT = 'member_count'
    GRIEVANCE_TEMPLATE = 'grievance_template'
    CLASS_ACTION_GRIEVANCE = "class_action_grievance"
  end

  REPORT_TYPES = [
    ReportTypes::SINGLE_EMPLOYEE,
    ReportTypes::BENEFITS,
    ReportTypes::BENEFIT_COVERAGES,
    ReportTypes::BENEFIT_COVERAGES_EXPIRATION,
    ReportTypes::BENEFICIARY,
    ReportTypes::PACFS,
    ReportTypes::SICK_BANK,
    ReportTypes::LODI,
    ReportTypes::UNION_MEETINGS,
    ReportTypes::EMPLOYEE_DELEGATE_ASSIGNMENT,
    ReportTypes::DISCIPLINES,
    ReportTypes::GRIEVANCES,
    ReportTypes::JANUS,
    ReportTypes::LIFE_INSURANCE,
    ReportTypes::GVS,
    ReportTypes::VISION_SCREENING,
    ReportTypes::WORKERS_COMP,
    ReportTypes::NOTIFICATION_ANALYTICS,
    ReportTypes::UHC,
    ReportTypes::MEMBER_COUNT,
    ReportTypes::GRIEVANCE_TEMPLATE,
    ReportTypes::CLASS_ACTION_GRIEVANCE
  ].freeze

  MAILING_LABEL_TYPES = %w[CustomAvery5160 CustomAvery5366 CustomPostCard CustomEnvelope10 CustomAvery5163 CustomAvery5161 CustomAvery5735].freeze

  serialize :params, JSON

  # ============================= Associations =========================================================================
  has_one_attached :file

  # ============================= Validations ==========================================================================
  validates :format, presence: true
  validates :report_type, inclusion: {in: REPORT_TYPES}
end
