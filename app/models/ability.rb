# frozen_string_literal: true

class Ability
  include CanCan::Ability

  def initialize(user, params: {})

    is_user = user.class == User
    is_employee = user.class == Employee

    # To check Super Account's Rights instead of actual account's rights
    current_tenant = nil
    if is_user && user.allowed_accounts.present?
      current_tenant = Apartment::Tenant.current
      Apartment::Tenant.switch!('nats')
    end

    rights = if is_user
               user.role.rights.pluck(:name)
             elsif is_employee
               Right.where(name: Right::MEMBER_RIGHTS).pluck(:name)
             end

    if is_user
      can :profile, User
      can :profile_update, User
    end

    if is_employee
      can :profile, Employee
    end

    can :read, Account

    # --------------------------------------- Settings Permissions ------------------------------------------------------

    if rights.include?('write_paf')
      can :manage, Pacf
    elsif rights.include?('read_paf')
      can :read, Pacf
    end

    if rights.include?('write_rank')
      can :manage, Rank
    elsif rights.include?('read_rank')
      can :read, Rank
    end

    if rights.include?('write_meeting_type')
      can :manage, MeetingType
    elsif rights.include?('read_meeting_type')
      can :read, MeetingType
    end

    if rights.include?('write_officer_status')
      can :manage, OfficerStatus
    elsif rights.include?('read_officer_status')
      can :read, OfficerStatus
    end

    if rights.include?('write_grievance')
      can :manage, Grievance
    elsif rights.include?('read_grievance')
      can :read, Grievance
    end

    if rights.include?('write_grievance_status')
      can :manage, GrievanceStatus
    elsif rights.include?('read_grievance_status')
      can :read, GrievanceStatus
    end

    if rights.include?('write_discipline_setting')
      can :manage, DisciplineSetting
    elsif rights.include?('read_discipline_setting')
      can :read, DisciplineSetting
    end

    if rights.include?('write_discipline_charge')
      can :manage, DisciplineCharge
    elsif rights.include?('read_discipline_charge')
      can :read, DisciplineCharge
    end

    if rights.include?('write_discipline_status')
      can :manage, DisciplineStatus
    elsif rights.include?('read_discipline_status')
      can :read, DisciplineStatus
    end

    if rights.include?('write_department')
      can :manage, Department
    elsif rights.include?('read_department')
      can :read, Department
    end

    if rights.include?('write_delegate_series')
      can :manage, DelegateSeries
    elsif rights.include?('read_delegate_series')
      can :read, DelegateSeries
    end

    # Analytics configuration permissions
    if rights.include?('write_analytics_configuration')
      can :manage, AnalyticsConfiguration
    elsif rights.include?('read_analytics_configuration')
      can :read, AnalyticsConfiguration
    end

    # if rights.include?('write_user_profile')
    #   can :manage, Leave
    #   can :manage, AnalyticsConfiguration
    # elsif rights.include?('read_user_profile')
    #   can :read, AnalyticsConfiguration
    #   can :read, Leave
    # end

    if rights.include?('write_section')
      can :manage, Section
      can :read, Department
    elsif rights.include?('read_section')
      can :read, Section
    end

    if rights.include?('write_title')
      can :manage, Title
      can :read, Section
      can :read, Department
    elsif rights.include?('read_title')
      can :read, Title
    end

    if rights.include?('write_gender')
      can :manage, Gender
    elsif rights.include?('read_gender')
      can :read, Gender
    end

    if rights.include?('write_unit')
      can :manage, Unit
    elsif rights.include?('read_unit')
      can :read, Unit
    end

    if rights.include?('write_position')
      can :manage, Position
    elsif rights.include?('read_position')
      can :read, Position
    end

    if rights.include?('write_firearm_status')
      can :manage, FirearmStatus
    elsif rights.include?('read_firearm_status')
      can :read, FirearmStatus
    end

    if rights.include?('write_marital_status')
      can :manage, MaritalStatus
    elsif rights.include?('read_marital_status')
      can :read, MaritalStatus
    end

    if rights.include?('write_payment_type')
      can :manage, PaymentType
    elsif rights.include?('read_payment_type')
      can :read, PaymentType
    end

    if rights.include?('write_employment_status')
      can :manage, EmploymentStatus
    elsif rights.include?('read_employment_status')
      can :read, EmploymentStatus
    end

    if rights.include?('write_office')
      can :manage, Office
    elsif rights.include?('read_office')
      can :read, Office
    end

    if rights.include?('write_facility')
      can :manage, Facility
    elsif rights.include?('read_facility')
      can :read, Facility
    end

    if rights.include?('write_benefit')
      can :manage, Benefit
    elsif rights.include?('read_benefit')
      can :read, Benefit
    end

    if rights.include?('write_affiliation')
      can :manage, Affiliation
    elsif rights.include?('read_affiliation')
      can :read, Affiliation
    end

    if rights.include?('write_tour_of_duty')
      can :manage, TourOfDuty
    elsif rights.include?('read_tour_of_duty')
      can :read, TourOfDuty
    end

    if rights.include?('write_platoon')
      can :manage, Platoon
    elsif rights.include?('read_platoon')
      can :read, Platoon
    end

    if rights.include?('write_note')
      can :manage, PolyNote
    elsif rights.include?('read_note')
      can :read, PolyNote
    end

    if rights.include?('write_reminder')
      if user.role.name.downcase == 'admin'
        can :manage, Reminder
        can :manage, ReminderTracker
        can :read, User
      else
        can :manage, Reminder, admin_only: false
        can :manage, ReminderTracker, reminder: { admin_only: false }
        can :read, User do |user_data|
          user_data.role&.name&.downcase != 'admin'
        end
      end
    elsif rights.include?('read_reminder') || rights.include?('read_reminder_user')
      if user.role.name.downcase == 'admin'
        can :read, Reminder
        can :manage, ReminderTracker
        can :read, User
      else
        can :read, Reminder, admin_only: false
        can :manage, ReminderTracker, reminder: { admin_only: false }
        can :read, User do |user_data|
          user_data.role&.name&.downcase != 'admin'
        end
      end
    end

    # -------------------------------------- User Permissions ----------------------------------------------------------

    if rights.include?('write_user')
      can :manage, User
    elsif rights.include?('read_user')
      can :read, User
    end

    # -------------------------------------- Member list Permissions ---------------------------------------------------

    if rights.include?('write_employee_discipline_setting')
      can :manage, EmployeeDisciplineSetting
      can :manage, EmployeeDisciplineStep
      can :read, DisciplineSetting
      can :read, DisciplineCharge
      can :read, DisciplineStatus
    elsif rights.include?('read_employee_discipline_setting')
      can :read, EmployeeDisciplineSetting
      can :read, EmployeeDisciplineStep
      can :read, DisciplineSetting
      can :read, DisciplineCharge
      can :read, DisciplineStatus
    end

    if rights.include?('write_employee_grievance')
      can :manage, EmployeeGrievance
      can :manage, EmployeeGrievanceStep
      can :read, Grievance
      can :read, GrievanceStatus
      can :contact_person_dropdown_data, Employee
      can :employee_dropdown_data, Employee
    elsif rights.include?('read_employee_grievance')
      can :read, EmployeeGrievance
      can :read, EmployeeGrievanceStep
      can :read, Grievance
      can :read, GrievanceStatus
      can :contact_person_dropdown_data, Employee
      can :employee_dropdown_data, Employee
    end

    if rights.include?('write_pesh')
      can :manage, Pesh
    elsif rights.include?('read_pesh')
      can :read, Pesh
    end

    if rights.include?('write_assault')
      can :manage, Assault
    elsif rights.include?('read_assault')
      can :read, Assault
    end

    if rights.include?('write_witness')
      can :manage, Witness
    elsif rights.include?('read_witness')
      can :read, Witness
    end
    if rights.include?('write_employee_paf')
      can :manage, EmployeePacf
      can :read, Pacf
    elsif rights.include?('read_employee_paf')
      can :read, EmployeePacf
    end

    if rights.include?('write_employee')
      can :manage, Employee, category: 'employee'
      can :manage, EmployeeDepartment
      can :manage, EmployeeSection
      can :manage, EmployeeTitle
      can :manage, Contact
      can :manage, EmployeeEmploymentStatus
      can :manage, EmployeeOfficerStatus
      can :manage, EmployeeFacility
      can :manage, EmployeePosition
      can :manage, EmployeeOffice
      can :manage, EmployeeRank
      can :manage, DelegateAssignment
      can %i[create update], User
      can :title_code_update, Employee
      can :read, Gender
      can :read, MaritalStatus
      can :read, Unit
      can :read, EmploymentStatus
      can :read, OfficerStatus
      can :read, Department
      can :read, Section
      can :read, Title
      can :read, Position
      can :read, Rank
      can :read, Office
      can :read, Facility
      can :read, MeetingType
      can :read, Affiliation
      can :read, TourOfDuty
      can :read, Platoon
      can :read, DelegateSeries
    elsif rights.include?('read_employee')
      can :delegate_employees, Employee
      can :login_credentials, Employee
      can :staff_member_employee, Employee
      can :employee_status_detail, Employee
      can :exact_search, Employee
      can :read, Employee
      can :read, EmployeeDepartment
      can :read, EmployeeSection
      can :read, EmployeeTitle
      can :read, Contact
      can :read, EmployeeEmploymentStatus
      can :read, EmployeeOfficerStatus
      can :read, EmployeeFacility
      can :read, EmployeePosition
      can :read, EmployeeOffice
      can :read, EmployeeRank
      can :read, DelegateAssignment
      can :read, DelegateSeries
      can :read, Office
      can :read, PaymentType
      can :read, EmploymentStatus
    end

    if rights.include?('read_legislative_address')
      can :read, LegislativeAddress
      can :employee_legislative_address, LegislativeAddress
    end

    if rights.include?('write_employee_meeting_type')
      can :manage, EmployeeMeetingType
      can :read, MeetingType
    elsif rights.include?('read_employee_meeting_type')
      can :read, EmployeeMeetingType
    end

    if rights.include?('write_employee_firearm_status')
      can :manage, EmployeeFirearmStatus
      can :manage, FirearmRangeScore
      can :read, FirearmStatus
    elsif rights.include?('read_employee_firearm_status')
      can :read, EmployeeFirearmStatus
      can :read, FirearmStatus
      can :read, FirearmRangeScore
    end

    if rights.include?('write_employee_benefit')
      can :manage, EmployeeBenefit
      can :manage, BenefitCoverage
      can :manage, BenefitDisbursement
      can :manage, Beneficiary
      can :manage, Disability
      can :read, Benefit
      can :read, PaymentType
      can :read, Gender
      can :manage, PolyNote
      can :read, EmploymentStatus
      can :manage, EmployeeEmploymentStatus
    elsif rights.include?('read_employee_benefit')
      can :read, EmployeeBenefit
      can :read, BenefitCoverage
      can :read, BenefitDisbursement
      can :read, Beneficiary
      can :read, Disability
      can :read, Gender
      can :read, PolyNote
    end

    if rights.include?('write_benefit_coverage')
      can :manage, BenefitCoverage
    elsif rights.include?('read_benefit_coverage')
      can :read, BenefitCoverage
      can :read, Gender
    end

    if rights.include?('write_benefit_disbursement')
      can :manage, BenefitDisbursement
      can :read, PaymentType
    elsif rights.include?('read_benefit_disbursement')
      can :read, BenefitDisbursement
    end

    if rights.include?('write_beneficiary')
      can :manage, Beneficiary
    elsif rights.include?('read_beneficiary')
      can :read, Beneficiary
      can :read, Gender
    end

    if rights.include?('write_disability')
      can :manage, Disability
    elsif rights.include?('read_disability')
      can :read, Disability
    end

    if rights.include?('write_life_insurance')
      can :manage, Dependent
      can :manage, LifeInsurance
    elsif rights.include?('read_life_insurance')
      can :read, Dependent
      can :read, LifeInsurance
    end

    if rights.include?('write_employee_award')
      can :manage, Award
    elsif rights.include?('read_employee_award')
      can :read, Award
    end

    if rights.include?('write_employee_analytics')
      can :manage, Leave
      can :manage, LodiRequestTab
      can :read, DeniedReason
      can :manage, Lodi
      can :manage, AnalyticsConfiguration
    elsif rights.include?('read_employee_analytics')
      can :read, Leave
      can :analytics, Leave
      can :read, LodiRequestTab
      can :read, Lodi
      can :read, DeniedReason
      can :read, AnalyticsConfiguration
    end

    if rights.include?('read_user_employee_analytics')
      can :read, Leave
      can :analytics, Leave
      can :read, LodiRequestTab
      can :read, Lodi
      can :read, DeniedReason
      can :read, AnalyticsConfiguration
    end

    if rights.include?('write_employee_upload')
      can :manage, Upload
    elsif rights.include?('read_employee_upload')
      can :read, Upload
    end

    if rights.include?('read_form')
      can :read, Form
    end

    if rights.include?('write_hearing')
      can :manage, Hearing
    elsif rights.include?('read_hearing')
      can :read, Hearing
    end

    # ----------------------------------------- Report Permissions -----------------------------------------------------
    # TODO: Modify permissions based on tabs for read and write
    can :manage, :report if rights.include?("report_#{params[:report_type]}")

    if (rights & Right::REPORT_RIGHTS).present?
      can :read, Gender
      can :read, Office
      can :read, Facility
      can %i[read employee_dropdown_data address_fields_data], Employee
      can :read, Rank
      can :read, Department
      can :read, Title
      can :read, OfficerStatus
      can :read, FirearmStatus
      can :read, Position
      can :read, Contact
      can :read, EmploymentStatus
      can :read, Section
      can :read, Unit
      can :read, MaritalStatus
      can :read, LifeInsurance
      can :read, LegislativeAddress
    end

    if rights.include?('report_benefits') || rights.include?('read_report_benefits')
      can :read, Benefit
      can :read, PaymentType
    end

    can :read, PaymentType if rights.include?('report_pacfs') || rights.include?('read_report_pacfs')

    can :read, MeetingType if rights.include?('report_union_meetings') || rights.include?('read_report_union_meetings')
    can :read, DisciplineSetting if rights.include?('report_disciplines') || rights.include?('read_report_disciplines')
    can :read, DisciplineCharge if rights.include?('report_disciplines') || rights.include?('read_report_disciplines')
    can :read, DisciplineStatus if rights.include?('report_disciplines') || rights.include?('read_report_disciplines')
    can :read, Benefit if rights.include?('report_benefit_coverages') || rights.include?('read_report_benefit_coverages')
    can :read, Grievance if rights.include?('report_grievances') || rights.include?('read_report_grievances')

    # ----------------------------------------- Notification Permissions -----------------------------------------------

    if rights.include?('write_notification')
      can :manage, Notification
      can :index, NotificationTracker
      can :analytics, NotificationTracker
      can :bounce_and_complaints, NotificationTracker
      can :read, Affiliation
      can :read, AnalyticsConfiguration
      can :read, Benefit
      can :read, Department
      can :read, DisciplineSetting
      can :read, EmploymentStatus
      can :read, FirearmStatus
      can :read, Gender
      can :read, Grievance
      can :read, MaritalStatus
      can :read, MeetingType
      can :read, OfficerStatus
      can :read, Office
      can :read, Facility
      can :read, Pacf
      can :read, PaymentType
      can :read, Platoon
      can :read, Position
      can :read, Rank
      can :read, Section
      can :read, Title
      can :read, TourOfDuty
      can :read, Unit
      can :read, Contact
      can :read, LegislativeAddress
      can :read, DelegateSeries
      can %i[read employee_dropdown_data address_fields_data], Employee
      can :manage, EmailTemplate
      can :manage, EmailPreviewFileUpload
    elsif rights.include?('read_notification')
      can :read, Notification
      can :read, NotificationTracker
      can :analytics, NotificationTracker
      can :bounce_and_complaints, NotificationTracker
      can :push_notification_show, Notification
      can :push_notification_index, Notification
      can :read, EmailTemplate
    end

    if rights.include?('write_device')
      can :manage, Device
    elsif rights.include?('read_device')
      can :read, Device
    end

    # To reset the schema for Super Account
    if current_tenant.present?
      Apartment::Tenant.switch!(current_tenant)
    end

    # ----------------------------------------- Change Request Permissions -----------------------------------------------
    if rights.include?('write_change_request')
      can :manage, ChangeRequest
      can :manage, ChangeRequestUpload
      can :update_password, Employee
    elsif rights.include?('read_change_request')
      can :read, ChangeRequest
      can :read, ChangeRequestUpload
    end

    # ----------------------------------------- Contact Persons Permissions -----------------------------------------------
    if rights.include?('write_contact_person')
      can :manage, Employee, category: 'contact_person'
      can :read, Unit
      can :read, Department
    elsif rights.include?('read_contact_person')
      can :read, Employee
      can :read, Unit
      can :read, Department
    end
    # ----------------------------------------- User Audit Rights -----------------------------------------------
    # rubocop:disable Style/IfUnlessModifier, Style/GuardClause
    if rights.include?('read_user_audit')
      can :read, UserAudit
    end

    # ----------------------------------------- Maillog Rights -----------------------------------------------
    if rights.include?('write_maillog')
      can :manage, Maillog
    elsif rights.include?('read_maillog')
      can :read, Maillog
    end

    # ----------------------------------- View Employee Write Rights ------------------------------------------
    if rights.include?('write_placard_only')
      can :update_allowed_fields, Employee
      can :notes_section, Employee
    end

    # rubocop:enable Style/IfUnlessModifier, Style/GuardClause
  end
end
