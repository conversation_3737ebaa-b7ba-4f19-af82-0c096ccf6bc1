# frozen_string_literal: true

class Account < ApplicationRecord
  RESTRICTED_SUBDOMAINS = %w[www public fuse admin account assets api demo].freeze

  # ============================= Assciations ==========================================================================
  has_one_attached :logo

  # ============================= Validations ==========================================================================
  validates :name, presence: true, uniqueness: { case_sensitive: false }
  validates :subdomain, presence: true,
                        uniqueness: { case_sensitive: false },
                        format: { with: /\A[[:alnum:]][[:alnum:]\-]{0,61}[[:alnum:]]\Z/i,
                                  message: 'contains invalid characters (only letters and numbers can be used)' },
                        exclusion: { in: RESTRICTED_SUBDOMAINS, message: 'restricted' }
  validates :logo, blob: { content_type: %w[image/png image/jpeg] }

  # ============================= Callbacks ============================================================================
  before_validation :downcase_subdomain
  after_create_commit :create_tenant_and_seed_db
  after_update_commit :add_and_update_rights

  private

  def downcase_subdomain
    self.subdomain = subdomain.try(:downcase)
  end

  # Create schema in databased and seed db - https://github.com/influitive/apartment#creating-new-tenants
  def create_tenant_and_seed_db
    Apartment::Tenant.create(subdomain)
    create_rights
    add_and_update_rights
  end

  def create_rights
    Right.create_rights(subdomain) if previous_changes['saas_json'].present?
  end
  
  def self.json_update(domain)
    Account.find_by(subdomain: "#{domain}").update(saas_json: JSON.parse(File.read("account_json/#{domain}_account_saas_json.json")))
  end

  def add_and_update_rights
    rights_hash = {}
    Apartment::Tenant.switch!(subdomain)
    get_rights = Right.kept.all.pluck(:name)
    rights = (get_rights & Right::EMPLOYEE_WRITE_RIGHTS) + Right::IGNORED_MODELS - ['Role']
    rights = get_rights_constant(rights)
    rights += UserAudit.models_without_rights.select { |model| current_account.saas_json.dig('schema', model.underscore).present? }
    rights_hash['models'] = rights.flatten.reject(&:blank?)
    update_column(:user_audit_models, rights_hash)
  end

  def get_rights_constant(rights)
    files = Dir[Rails.root + 'app/models/*.rb']
    models = files.map { |m| File.basename(m, '.rb').camelize }

    uniq_rights, constant_rights = [], []
    rights.each do |right|
      uniq_rights << right.gsub('read_', '').gsub('write_', '')
    end
    uniq_rights = uniq_rights.uniq
    uniq_rights.each do |uniq_right|
      if models.include?(uniq_right.classify)
        constant_rights << uniq_right.classify
      elsif models.include?(uniq_right.gsub('paf', 'pacf').classify)
        constant_rights << uniq_right.gsub('paf', 'pacf').classify
      elsif models.include?(uniq_right.gsub('employee_', '').classify)
        constant_rights << uniq_right.gsub('employee_', '').classify
      end
    end
    constant_rights
  end

  def excluded_model_for_user_audit
    %w[Role Assault Witness]
  end
end
