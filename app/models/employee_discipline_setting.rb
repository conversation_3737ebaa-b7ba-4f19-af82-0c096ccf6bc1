# frozen_string_literal: true

class EmployeeDisciplineSetting < ApplicationRecord
  delegate :name, to: :discipline_setting, allow_nil: true
  delegate :name, to: :discipline_charge, allow_nil: true
  include PgSearch::Model
  pg_search_scope :search_for_status, associated_against: {
    discipline_charge: [:id]
  }

  enum discipline_type: { major: 0, minor: 1 }
  enum duty_status: { on_duty: 0, off_duty: 1 }
  enum status: { pending: 0, settled: 1, withdrawn: 2, decided: 3 }

  # ================== Associations ====================================================================================
  belongs_to :employee
  belongs_to :discipline_setting, optional: true
  belongs_to :discipline_charge, optional: true
  belongs_to :discipline_status, optional: true
  has_many :employee_discipline_steps, -> { where(employee_discipline_steps: { discarded_at: nil }) }
  has_many :discipline_statuses, through: :employee_discipline_steps
  has_many_attached :files

  belongs_to :office, optional: true

  # ================== Validations =====================================================================================
  validates :files, blob: { content_type: ACCEPTABLE_CONTENT_TYPES, size_range: 1..100.megabytes }

  # ============================= Callbacks ============================================================================
  before_save :reset_abandonment_hearing, unless: -> { ta_implemented }
  after_discard do
    employee_discipline_steps.update_all(discarded_at: Time.current)
  end

  # ================== Instance Methods ================================================================================
  def discipline_setting_name
    discipline_setting.name
  end

  def discipline_charge_name
    discipline_charge.name
  end

  def reset_abandonment_hearing
    self.abandonment_hearing = false
  end

  def arbitrator
    Employee.unscoped.find_by(id: arbitrator_id)
  end

  def pa_attorney
    Employee.unscoped.find_by(id: pa_attorney_id)
  end

end
