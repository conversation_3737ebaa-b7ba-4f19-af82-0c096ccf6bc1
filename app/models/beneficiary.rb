# frozen_string_literal: true

class Beneficiary < ApplicationRecord
  BENEFICIARY_TYPE = %w[Primary Secondary Contingent].freeze

  # ============================= Associations =========================================================================
  has_one_attached :file
  belongs_to :employee
  belongs_to :gender, optional: true

  # ============================= Validations ==========================================================================
  validates :beneficiary_type, inclusion: { in: BENEFICIARY_TYPE }, if: -> { beneficiary_type_changed? }
  validates :file, blob: { content_type: ACCEPTABLE_CONTENT_TYPES, size_range: 1..100.megabytes }
  validates :percentage, numericality: { greater_than: 0 }, if: -> { percentage_changed? }, allow_blank: true
  validate :percentage_less_than_or_equal_100, if: -> { percentage_changed? }
  validates :name, format: { with: /\A[a-zA-Z\d\s\u00C0-\u017F'*&()_,.%\#\`\/-]+\z/ }

  # ============================= Methods ==============================================================================
  def percentage_less_than_or_equal_100
    return if errors.any?

    hash = employee.beneficiaries.kept.reject { |b| b.id == id }
    hash << self
    hash = hash.group_by(&:beneficiary_type).map { |type_of_benefit, records| { type_of_benefit: type_of_benefit, percent: (records.map { |b| b.percentage.try(:round, 2) }).compact.sum } }
    hash.each do |record|
      if record[:percent] > 100.0
        errors.add('Beneficiary', " % for #{record[:type_of_benefit]} exceeding a max limit of 100.")
      end
    end
  end
end
