# frozen_string_literal: true

module Api
  class EmployeeDisciplineStepSerializer < NotesSerializer
    attributes :id, :date, :recommended_notes, :is_settled, :is_pending, :step, :win, :loss,
               :employee_discipline_setting_id, :hearing, :discipline_status_id, :pba_member, :pa_member,
               :arbitrator_id

    attribute :files do |object|
      FileBuilder.process_multi_files(object)
    end

    attribute :arbitrator do |object|
      object.arbitrator&.full_name
    end
  end
end
