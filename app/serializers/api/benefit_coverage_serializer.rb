# frozen_string_literal: true

module Api
  class BenefitCoverageSerializer < ApplicationSerializer
    attributes :address, :birthday, :employee_benefit_id, :expires_at, :id, :name, :relationship,
               :social_security_number, :phone, :dependent, :effective_date, :gender_id, :unexpire, :first_name, :last_name, :suffix, :serviced_expiration, :add_dependent_to_all_benefits, :semester, :school_status, :student

    attribute :benefit_name do |object|
      object.employee_benefit.name if object.employee_benefit.present?
    end

    attribute :files do |object|
      FileBuilder.process_multi_files(object)
    end

    attribute :gender_name do |object|
      Gender.kept.find(object.gender_id).name unless object.gender_id.nil?
    end

    attribute :age do |object|
      date = object.birthday if object.birthday.present?
      now = Time.now

      now.year - date.to_time.year - (date.to_time.change(:year => now.year) > now ? 1 : 0) if date
    end

    attribute :benefit_coverage_full_name do |object|
      coverage_name = [object.first_name, object.suffix, object.last_name].join(" ")
      coverage_name.blank? ? object.name : coverage_name
    end
  end
end
