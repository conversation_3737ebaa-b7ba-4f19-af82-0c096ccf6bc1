# frozen_string_literal: true

module Api
  class EmployeeGrievanceStepSerializer < NotesSerializer
    attributes :id, :recommended_notes, :is_settled, :is_pending, :olr, :arbitration,
               :win, :loss, :step, :employee_grievance_id, :grievance_status_id, :decision, :show_button_on

    attribute :date do |object|
      object.date&.to_date
    end

    attribute :files do |object|
      FileBuilder.process_multi_files(object)
    end

    attribute :button_visible_in do |object|
      today = Date.tomorrow
      show_date = object.show_button_on
      if show_date.blank? || show_date <= today
        '0 working days'
      else
        "#{(today...show_date).select {|d| (1..5).include?(d.wday) }.size} working days"
      end
    end

    attribute :is_button_visible do |object|
      show_date = object.show_button_on
      show_date.present? && show_date <= Date.today
    end

    has_many :hearings
  end
end
