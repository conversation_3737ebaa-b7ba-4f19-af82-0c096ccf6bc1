# frozen_string_literal: true

module Api
  class LegislativeAddressSerializer < ApplicationSerializer
    attribute :legislation_details, if: proc { |_object, params| params[:action_name] != 'index' }

    ### We need to remove this logic once the changes is done in the mobile side. We are adding it here for the fail safe.

    attribute :assembly_member_name, if: proc { |_object, params| params[:action_name] != 'index' } do |object|
      object.legislation_details['assembly_member_details']['name'] if object.legislation_details.present?
    end

    attribute :congress_member_name, if: proc { |_object, params| params[:action_name] != 'index' } do |object|
      object.legislation_details['congress_member_details']['name'] if object.legislation_details.present?
    end

    attribute :senator_name, if: proc { |_object, params| params[:action_name] != 'index' } do |object|
      object.legislation_details['senate_member_details']['name'] if object.legislation_details.present?
    end

    attribute :assembly_web_url, if: proc { |_object, params| params[:action_name] != 'index' } do |object|
      object.legislation_details['assembly_member_details']['website'] if object.legislation_details.present?
    end

    attribute :congress_web_url, if: proc { |_object, params| params[:action_name] != 'index' } do |object|
      object.legislation_details['congress_member_details']['website'] if object.legislation_details.present?
    end

    attribute :senate_web_url, if: proc { |_object, params| params[:action_name] != 'index' } do |object|
      object.legislation_details['senate_member_details']['website'] if object.legislation_details.present?
    end

    attribute :congress_district_name, if: proc { |_object, params| params[:action_name] != 'index' } do |object|
      object.legislation_details['congress_member_details']['district'] if object.legislation_details.present?
    end

    attribute :assembly_district_name, if: proc { |_object, params| params[:action_name] != 'index' } do |object|
      object.legislation_details['assembly_member_details']['district'] if object.legislation_details.present?
    end

    attribute :senator_district_name, if: proc { |_object, params| params[:action_name] != 'index' } do |object|
      object.legislation_details['senate_member_details']['district'] if object.legislation_details.present?
    end

    #### --------------------------------------------------------------------------------------------------------####

    attribute :assembly_district_name, if: proc { |_object, params| params[:action_name] == 'index' } do |object|
      object.legislation_details['assembly_member_details']['district'] if object.legislation_details.present?
    end

    attribute :senate_district_name, if: proc { |_object, params| params[:action_name] == 'index' } do |object|
      object.legislation_details['senate_member_details']['district'] if object.legislation_details.present?
    end

    attribute :congress_district_name, if: proc { |_object, params| params[:action_name] == 'index' } do |object|
      object.legislation_details['congress_member_details']['district'] if object.legislation_details.present?
    end

    attribute :council_district_name, if: proc { |_object, params| params[:action_name] == 'index' } do |object|
      object.legislation_details['council_member_details']['district'] if object.legislation_details.present?
    end

  end
end
