# frozen_string_literal: true

module Api
  class BeneficiarySerializer < ApplicationSerializer
    attributes :address, :beneficiary_type, :id, :name, :percentage, :relationship, :phone, :birthday, :gender_id, :social_security_number

    attribute :file do |object|
      FileBuilder.process_file(object)
    end


    attribute :gender_name do |object|
      Gender.kept.find(object.gender_id).name unless object.gender_id.nil?
    end
  end
end
