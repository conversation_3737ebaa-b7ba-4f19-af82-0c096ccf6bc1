# frozen_string_literal: true

module Api
  class UserSerializer < ApplicationSerializer
    attributes :first_name, :last_name, :username, :birthday, :id, :role_id, :notification, :email,
               :apartment, :street, :city, :state, :zipcode, :allowed_accounts, :user_audit_logging, :restrict_login_out_of_office, :restrict_type, :allow_login_from_time, :allow_login_to_time

    has_many :user_contacts, if: proc { |_object, params| %w[profile profile_update].include?(params[:action_name]) }
    belongs_to :role, if: proc { |_object, params| params[:action_name] == 'index' }

    attribute :name do |object|
      object.first_name.to_s + ' ' + object.last_name.to_s
    end

    attribute :role_name do |object|
      object.role.name
    end

    attribute :avatar do |object|
      avatar = object.avatar.attached? ? object.avatar : nil

      if avatar.present?
        subdomain = ENV['MANDATORY_SUBDOMAIN'].present? ? "#{Apartment::Tenant.current}.#{ENV['MANDATORY_SUBDOMAIN']}" : Apartment::Tenant.current # rubocop:disable Layout/LineLength
        Rails.application.routes.url_helpers.rails_representation_url(avatar.variant(resize: '80x80'), subdomain: subdomain)
      end
    end

    attribute :super_account do |object|
      account = Account.find_by(subdomain: Apartment::Tenant.current)
      account.saas_json.dig('super_account')
    end

    attribute :saas_json do |object|
      account = Account.find_by(subdomain: Apartment::Tenant.current)
      if account.saas_json.dig('super_account')
        account.saas_json
      else
        {}
      end
    end
  end
end
