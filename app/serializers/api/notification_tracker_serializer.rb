# frozen_string_literal: true

module Api
  class NotificationTrackerSerializer < ApplicationSerializer
    attributes :notification_id, :employee_id, :sent, :delivered, :rejected, :opened, :clicked, :bounced,
               :email, :notification_type, :sms, :sms_sent, :sms_delivered, :sms_failed, :message_id, :push, :push_sent, :sms_undelivered, :sms_error_code

    # attribute :employee_name do |object|
    #   object.employee.name if object.employee.present?
    # end
    attribute :email_current_status do |object, params|
      if params[:account].saas_json.dig('schema', 'notifications', 'sendgrid_templates') == true && object.email?
        if object.bounced? || object.rejected?
          'Failed'
        elsif object.clicked?
          'Clicked'
        elsif object.opened?
          'Opened'
        elsif object.delivered?
          'Delivered'
        elsif object.sent?
          'Sent'
        else
          '-'
        end
      else
        '-'
      end
    end

    attribute :employee_name do |object|
      if object.notification.subdomain.present?
        current_account = Apartment::Tenant.current
        Apartment::Tenant.switch!(object.notification.subdomain)
        employee = Employee.find(object.employee_id)
        employee_name = employee.name
        Apartment::Tenant.switch!(current_account)
      else
        employee_name = object.employee.name if object.employee.present?
      end

      employee_name
    end
  end
end
