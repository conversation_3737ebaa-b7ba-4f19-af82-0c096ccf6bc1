# frozen_string_literal: true

module Api
  class EmployeeDisciplineSettingSerializer < NotesSerializer
    attributes :id, :employee_id, :discipline_setting_id, :description, :date, :discipline_charge_id, :charge,
               :dan_number, :recommended_penalty, :was_employee_pds, :case_and_abeyance, :ta_implemented,
               :abandonment_hearing, :filed_olr, :discipline_type, :docket_number, :duty_status, :office_id,
               :arbitrator_id, :pa_attorney_id, :status


    has_many :employee_discipline_steps
    belongs_to :discipline_status, optional: true

    attribute :files do |object|
      FileBuilder.process_multi_files(object)
    end

    attribute :discipline_setting_name do |object|
      object.discipline_setting&.name
    end

    attribute :discipline_charge_name do |object|
      object.discipline_charge&.name
    end

    attribute :is_settled do |object|
      unless object.employee_discipline_steps.nil?
        true if object.employee_discipline_steps.pluck(:is_settled).include?(true)
      end
    end

    attribute :arbitrator do |object|
      object.arbitrator&.full_name
    end

    attribute :pa_attorney do |object|
      object.pa_attorney&.full_name
    end

    attribute :discipline_status_name do |object|
      object.discipline_status&.name
    end

    attribute :office do |object|
      object.office&.name
    end
  end
end
