# frozen_string_literal: true

module Api
  class EmployeeSerializer < NotesSerializer
    attributes :a_number, :apartment, :birthday, :city, :first_name, :id, :last_name, :marital_status_id,
               :middle_name, :name, :notes, :officer_status_name, :placard_number, :start_date, :shield_number, :previous_shield_number,
               :slug, :social_security_number, :state, :street, :veteran_status, :zipcode, :county, :gender_id, :unit_id, :prom_prov,
               :prom_perm, :title_code, :janus_card, :staff_member, :member_since, :do_not_mail, :janus_card_opt_out_date,
               :title_name, :office_name, :rank_name, :employment_status_name, :personal_phone, :email_opt_out, :sms_opt_out, :rdo, :payroll_id,
               :longevity_date, :leave_progression_date, :ncc_date, :primary_work_location, :affiliation_id,
               :tour_of_duty_id, :platoon_id, :responder_911, :t_shirt_size, :marital_status_date, :username, :member_start_date, :user_profile_id,
               :suffix, :maiden_name, :category, :enable_mobile_access, :department_id, :prescription, :register_vote, :same_as_mailing_address,
               :previous_shield_number, :department_name, :app_downloaded, :section_name, :position_name

    # Contact info is not needed for index action
    has_many :contacts, if: proc { |_object, params| !%w[index exact_search].include?(params[:action_name]) }
    has_one :mailing_address, if: proc { |_object, params| !%w[index exact_search].include?(params[:action_name]) }
    belongs_to :gender, if: proc { |_object, params| params[:action_name] == 'show' }
    belongs_to :unit, if: proc { |_object, params| params[:action_name] == 'show' }
    belongs_to :marital_status, if: proc { |_object, params| params[:action_name] == 'show' }
    belongs_to :affiliation, if: proc { |_object, params| params[:action_name] == 'show' }
    belongs_to :tour_of_duty, if: proc { |_object, params| params[:action_name] == 'show' }
    belongs_to :platoon, if: proc { |_object, params| params[:action_name] == 'show' }

    attribute :address, &:full_address

    attribute :personal_phone do |object|
      contact = object.contacts.find_by(contact_for: 'personal', contact_type: 'phone')
      contact.value if contact.present?
    end

    attribute :personal_email do |object|
      contact = object.contacts.find_by(contact_for: 'personal', contact_type: 'email')
      contact.value if contact.present?
    end

    attribute :gender_name do |object|
      Gender.kept.find(object.gender_id).name unless object.gender_id.nil?
    end

    attribute :unit_name do |object|
      Unit.kept.find(object.unit_id).name unless object.unit_id.nil?
    end

    attribute :marital_status_name do |object|
      MaritalStatus.kept.find(object.marital_status_id).name unless object.marital_status_id.nil?
    end

    attribute :affiliation_name do |object|
      Affiliation.kept.find(object.affiliation_id).name unless object.affiliation_id.nil?
    end

    attribute :tour_of_duty_name do |object|
      TourOfDuty.kept.find(object.tour_of_duty_id).name unless object.tour_of_duty_id.nil?
    end

    attribute :platoon_name do |object|
      Platoon.kept.find(object.platoon_id).name unless object.platoon_id.nil?
    end

    attribute :mailing_address, if: proc { |_object, params| !%w[index exact_search].include?(params[:action_name]) } do |object|
      if object.same_as_mailing_address == true
        object&.full_address
      else
        object.mailing_address&.full_address
      end
    end

    attribute :mailing_address_id do |object|
      object.mailing_address&.id
    end

    attribute :mailing_address_apartment do |object|
      if object.same_as_mailing_address == true
        object.apartment
      else
        object.mailing_address&.apartment
      end
    end

    attribute :mailing_address_street do |object|
      if object.same_as_mailing_address == true
        object.street
      else
        object.mailing_address&.street
      end
    end

    attribute :mailing_address_city do |object|
      if object.same_as_mailing_address == true
        object.city
      else
        object.mailing_address&.city
      end
    end

    attribute :mailing_address_state do |object|
      if object.same_as_mailing_address == true
        object.state
      else
        object.mailing_address&.state
      end
    end

    attribute :mailing_address_zipcode do |object|
      if object.same_as_mailing_address == true
        object.zipcode
      else
        object.mailing_address&.zipcode&.to_s&.rjust(5, '0')
      end
    end

    # 'Avatar' is not needed for index action
    attribute :avatar, if: proc { |_object, params| !%w[index exact_search].include?(params[:action_name]) } do |object|
      avatar = object.avatar.attached? ? object.avatar : nil

      if avatar.present?
        subdomain = ENV['MANDATORY_SUBDOMAIN'].present? ? "#{Apartment::Tenant.current}.#{ENV['MANDATORY_SUBDOMAIN']}" : Apartment::Tenant.current # rubocop:disable Layout/LineLength
        Rails.application.routes.url_helpers.rails_representation_url(avatar.variant(resize: '80x80'), subdomain: subdomain)
      end
    end

    attribute :avatar_preview, if: proc { |_object, params| !%w[index exact_search].include?(params[:action_name]) } do |object|
      avatar = object.avatar.attached? ? object.avatar : nil

      if avatar.present?
        subdomain = ENV['MANDATORY_SUBDOMAIN'].present? ? "#{Apartment::Tenant.current}.#{ENV['MANDATORY_SUBDOMAIN']}" : Apartment::Tenant.current # rubocop:disable Layout/LineLength
        Rails.application.routes.url_helpers.rails_representation_url(avatar.variant(resize: '300x300'), subdomain: subdomain)
      end
    end
  end
end
