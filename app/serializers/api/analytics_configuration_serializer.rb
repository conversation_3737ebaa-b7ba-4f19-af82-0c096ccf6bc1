# frozen_string_literal: true

module Api
  class AnalyticsConfigurationSerializer < NotesSerializer
    attributes :id, :days_earned, :notes, :days_used, :analytics_type, :employee_id, :duration_from, :duration_to, :slug

    attribute :year_range do |object|
      if object.duration_from && object.duration_to
        date = [object.duration_from, object.duration_to].minmax
        "#{date[0].strftime('%b %Y')} - #{date[1].strftime('%b %Y')}"
      end
    end

    attribute :days_balance do |object|
      if object.days_earned && object.days_used
        object.days_earned >= object.days_used ? object.days_earned - object.days_used : 0
      end
    end

    attribute :employee_name do |object|
      object.employee.name if object.employee.present?
    end
  end
end
