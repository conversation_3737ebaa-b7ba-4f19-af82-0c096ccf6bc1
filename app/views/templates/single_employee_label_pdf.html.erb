<% employees.order_by_name.each_with_index do |employee, i| %>
  <% style = 'width: 75mm; height: 27mm; padding: 6mm 3mm 3mm; box-sizing: border-box; display: flex; flex-direction: column; justify-content: center; font-size: 4mm; line-height: 1.2; overflow: hidden; page-break-inside: avoid;' %>

  <% if employee.apartment.present? || employee.employee_address.to_s.length > 36 %>
    <% style += ' padding-top: 3mm;' %>
  <% end %>

  <div style="<%= style + ( i == employees.size - 1 ? '' : 'page-break-after: always;') %>">
    <div>
      <%= employee.name.upcase %>
    </div>

    <div>
      <%= employee.street %>
    </div>
    <% if employee.apartment.present? %>
      <div>
        <%= employee.apartment %>
      </div>
    <% end %>
    <div>
      <%= employee.employee_address %>
    </div>
  </div>
<% end %>
