<% index = 0
   total_dependents_count = 0
   coverages = {}
%>
<table class="table-striped table-employee-benefits">
  <tbody>
  <% employees.order_by_name.each do |employee| %>
    <% index += 1 %>
    <tr>
      <td>
        <ul>
          <li class="block-employee-details">
            <div class="w-5 float-left">
              <%= index %>
            </div>
            <div class="w-30 float-left">
              <span>
                <%= "Member Name: " %>
              </span>
              <strong class="text-blue">
                <%= employee.name %>
              </strong>
            </div>
            <div class="w-30 float-left">
              <span>
                <%= "#{ translation_for('employees.shield_number') }: " %>
              </span>
              <strong>
                <%= employee.shield_number || '-' %>
              </strong>
            </div>
          </li>

          <% employee.employee_benefits.each do |employee_benefit| %>
            <%total_dependents_count += employee_benefit.benefit_coverages.where(discarded_at: nil).count %>
            <% employee_benefit.benefit_coverages.kept.group('LOWER(relationship)').order('LOWER(relationship) desc').count.each do |coverage, value|
              coverages[coverage] ||= 0
              coverages[coverage] += value
            end %>
            <%= (render partial: '/templates/benefit_coverages/benefit_pdf',
                        locals: { employee: employee, employee_benefit: employee_benefit, show_coverages: show_coverages,
                                  current_account: current_account }) %>
          <% end %>
        </ul>
      </td>
    </tr>
  <% end; if show_coverages == 'true' || show_coverages == 'all'
  %>
    <% coverages.each do |coverage, value| %>
      <tr>
        <td class="block-total-summary">
          <ul>
            <li>
              <div class="w-20 float-left">&nbsp;
              </div>
              <div class="w-20 float-left">&nbsp;
              </div>
              <div class="w-20 float-left">&nbsp;
              </div>
              <div class="w-30  float-left text-right">
                <% if coverage.blank? %>
                  <h3 class="line-height-2"><%= "Total dependents without a Relationship defined :" %></h3>
                <% else %>
                  <h3 class="line-height-2"><%= "Total #{coverage.titleize} :" %></h3>
                <% end %>
              </div>
              <div class="w-5 float-left text-right">
                <h3 class="line-height-3"><%= value %></h3>
              </div>
            </li>
          </ul>
        </td>
      </tr>
    <% end %>
    <br>
    <tr>
      <td class="block-total-summary">
        <ul>
          <li>
            <div class="w-20 float-left">&nbsp;
            </div>
            <div class="w-20 float-left">&nbsp;
            </div>
            <div class="w-20 float-left">&nbsp;
            </div>
            <div class="w-30  float-left text-right">
              <h3 class="line-height-2"><%= "Total Dependents :" %></h3>
            </div>
            <div class="w-5 float-left text-right">
              <h3 class="line-height-3"><%=total_dependents_count%></h3>
            </div>
          </li>
        </ul>
      </td>
    </tr>
  <% end %>
  </tbody>
</table>
