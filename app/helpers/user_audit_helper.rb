# frozen_string_literal: true

module UserAuditHelper
  # rubocop:disable Metrics/AbcSize, Metrics/PerceivedComplexity
  def changeset_for_contacts(changeset, object)
    modified_changeset = {}
    serialized_object = serialize_object(object)
    contact_type = if changeset['contact_for'].present? && changeset['contact_type'].present?
                     "#{changeset['contact_for'].second.titleize} #{changeset['contact_type'].second.titleize}"
                   else
                     contact = Contact.where(id: serialized_object['id']).first
                     "#{contact.contact_for.titleize} #{contact.contact_type.titleize}"
                   end
    first_value = changeset['value']&.first || ''
    modified_value = changeset['value']&.last || ''
    modified_changeset[contact_type.to_s] = [first_value, modified_value]
    modified_changeset['Contact Name'] = [changeset['contact_name'].first, changeset['contact_name'].last] if changeset['contact_name']&.any?(&:present?)
    modified_changeset['Contact Relationship'] = [changeset['contact_relationship'].first, changeset['contact_relationship'].last] if changeset['contact_relationship']&.any?(&:present?)
    modified_changeset
  end

  def changeset_for_others(changeset, account, object, item_schema_name, discarded_time, attachments_names)
    item_type = object.item_type
    model_name = UserAudit.models_without_rights.include?(item_type) ? item_type.underscore : item_type.underscore.pluralize
    changeset = sort_based_on_json(changeset, model_name, account)
    modified_changeset = {}
    excluded_values = %w[id employee_id payroll_id middle_name user_profile_id assault_id lodi_id employee_grievance_step_id arbitrator_id pa_attorney_id]
    changeset.each do |key, value|
      if key.include?('_id') && excluded_values.exclude?(key)
        model = key == 'delegate_employee_id' ? key.gsub('_id', '').gsub('delegate_', '') : key.gsub('_id', '')
        if value.present?
          before_change_value = model.classify.constantize.where(id: value.first).first&.name if value.first.present?
          after_change_value = model.classify.constantize.where(id: value.last).first&.name if value.last.present?
        end
        translation_model = model.gsub('employee_', '')
        item_schema_name = translation_for(translation_model, account)
        item_schema_name = 'Grievance Type' if item_schema_name == 'Grievance'
        modified_changeset[item_schema_name.to_s] = [before_change_value, after_change_value]
      elsif %w[id employee_id encrypted_password created_at updated_at slug reset_password_token employee_grievance_step_id].exclude?(key)
        item_translation_name = translation_for("#{model_name}.#{key}", account)
        if key == 'discarded_at' || (discarded_time.present? && discarded_time < object.created_at)
          modified_changeset["Deleted #{item_schema_name} at"] = ['', discarded_time || object.created_at]
          next if key == 'discarded_at'
        end
        item_schema_name = if item_translation_name.present?
                             item_translation_name
                           elsif item_translation_name.blank?
                             key.classify.titleize
                           end

        modified_changeset[item_schema_name] = if %w[arbitrator_id pa_attorney_id].exclude?(key)
                                                 value
                                               else
                                                 value.map { |id| Employee.unscoped.find_by(id: id)&.full_name }
                                               end
      end
    end
    if attachments_names.present?
      attachment_name_hash = eval(attachments_names)
      if attachment_name_hash.count == 1
        attachment_key = attachment_name_hash.keys&.first
        attachment_key = translation_for("#{model_name}.#{attachment_key}", account)
        value = attachment_name_hash.values.first.map { |v| (v&.length == 1 && v&.first.blank?) ? nil : v }
        if value && value[0]&.flatten != value[1]&.flatten
          modified_changeset[attachment_key.to_s.to_sym] = value
        end
      else
        attachment_name_hash.each do |key, value|
          values = value.map { |v| (v&.length == 1 && v.first.blank?) ? nil : v }
          attachment_key = translation_for("#{model_name}.#{key}", account)
          if values && values[0]&.flatten != values[1]&.flatten
            modified_changeset[attachment_key.to_s.to_sym] = values
          end
        end
      end
    end
    modified_changeset.to_h
  end

  def check_dependent_values(object, account)
    dependent_hash = {}
    serialized_object = serialize_object(object, true)
    model = object.item_type.classify.constantize
    model_value = model.where(id: serialized_object['id'].is_a?(Integer) ? serialized_object['id'] : serialized_object['id'].last).first if serialized_object.present?
    return if model_value.blank?

    if %w[BenefitCoverage BenefitDisbursement].include?(object.item_type) && model_value.present?
      benefit_value = model_value.employee_benefit
      if benefit_value.present?
        dependent_hash['Benefit Name'] = benefit_value&.name
        dependent_hash['Dependent Name'] = [model_value.first_name, model_value.last_name].join(' ') || model_value.name if object.item_type == 'BenefitCoverage'
      end
    elsif %w[EmployeeGrievanceStep EmployeeDisciplineStep].include?(object.item_type) && model_value.present?
      underscore_value = object.item_type.underscore
      model_json_value = translation_for(underscore_value.gsub('employee_', '').gsub('_step', ''), account)
      if underscore_value == 'employee_grievance_step'
        value = translation_for("#{underscore_value.gsub('_step', '').pluralize}.number", account)
        dependent_hash["#{model_json_value} Step No"] = model_value&.step&.titleize
        dependent_hash[value] = model_value.send(underscore_value.gsub('_step', '')).number
      else
        value = translation_for("#{underscore_value.gsub('_step', '')}.dan_number", account)
        dependent_hash["#{model_json_value} Step No"] = model_value&.step&.titleize
        dependent_hash[value] = model_value.dan_number
      end
    end
    dependent_hash
  end

  def serialize_object(object, object_changes = nil)
    object_yaml = if object.object.present?
                    object.object
                  elsif object_changes
                    object.object_changes
                  end
    PaperTrail.serializer.load(object_yaml) if object_yaml.present?
  end

  def sort_based_on_json(changeset, model_name, account)
    sorted_changeset = {}
    association_ids = []
    other_keys_not_present_in_json = { 'leaves': ['leave_type'] }
    json_value_keys = account.saas_json.dig('schema', model_name).keys
    changeset.each_key { |key| association_ids << key if key.include?('_id') }
    common_keys = json_value_keys & changeset.keys
    common_keys += association_ids
    other_keys_not_present_in_json.each {|key, value| common_keys << value if model_name.to_sym == key && value.any? { |v| changeset.keys.include?(v) } }
    common_keys.flatten.each do |json_key|
      if model_name == 'leaves' && json_key == 'leave_type' && account.saas_json.dig('schema', 'common_terms').keys.include?(changeset[json_key]&.last)
        sorted_changeset[json_key] = ['', account.saas_json.dig('schema', 'common_terms', changeset[json_key]&.last)]
        next
      end
      sorted_changeset[json_key] = changeset[json_key]
    end
    sorted_changeset.blank? ? changeset : sorted_changeset
  end

  # rubocop:enable Metrics/AbcSize, Metrics/PerceivedComplexity
end
