# frozen_string_literal: true

module Api
  module Settings
    class AnalyticsConfigurationsController < Api::BaseController
      before_action :set_analytics_configuration, only: %w[update]

      def index
        analytics_configurations = AnalyticsConfiguration.kept.includes(:employee)
                                       .where("(analytics_type = 'sick' AND extract(year from duration_from) = ?) OR (analytics_type != 'sick' AND extract(year from duration_from) = ?)",
                                              AnalyticsConfiguration.analytic_current_year_start('sick').to_date.year,
                                              AnalyticsConfiguration.analytic_current_year_start('!sick').to_date.year)
        pagy, analytics_configurations = params[:search_text].present? ? pagy(analytics_configurations.search_by_analytics(params[:search_text])) : pagy(analytics_configurations)
        render_success(data: analytics_configurations, options: {meta: pagy_headers_hash(pagy)})
      end

      def create
        if resource_params[:analytics_type].present? && resource_params[:days_earned].present? && (!params[:analytics_configuration][:employee_ids].blank?)
          CreateAnalyticsConfigurationJob.perform_later(resource_params.merge!(employee_ids: employee), current_user.class.to_s, current_user.id)
          render json: {}
        else
          render json: {errors: "Field values can't be blank"}, status: 422
        end
      end

      def update
        @analytics_configuration.update(update_params)
        render_json(data: @analytics_configuration)
      end

      private

      def analytics_configurations_json
        {
            user_id: current_user.id,
            analytics: Api::AnalyticsConfigurationSerializer.new(@analytics_configurations.includes(:employee), meta: pagy_headers_hash(@pagy)),
            employees: Api::EmployeeObjectSerializer.new(pagy(Employee.kept.where(staff_member: true)).last)
        }
      end

      def update_params
        params.require(:analytics_configuration).permit(:days_earned, :notes)
      end

      def resource_params
        params.require(:analytics_configuration).permit(:analytics_type, :days_earned, :notes)
      end

      def employee
        if params[:analytics_configuration][:employee_ids].any?('0')
          Employee.kept.where(staff_member: true).ids
        else
          params[:analytics_configuration][:employee_ids]
        end
      end

      def set_analytics_configuration
        @analytics_configuration = AnalyticsConfiguration.kept.find(params[:id])
      end
    end
  end
end
