# frozen_string_literal: true

module Api
  module Settings
    class PositionsController < Api::BaseController
      include Api::ApiRescues
      before_action :set_position, only: %w[update destroy]

      def index
        positions = Position.kept
        account = Account.find_by(subdomain: Apartment::Tenant.current)
        sort_by_alphanumeric = account.saas_json.dig('schema', 'positions', 'order_alphanumerically') == true
        pagy, positions = if params[:search_text].present?
                            pagy(positions.search_by_name(params[:search_text]), items: params[:per_page])
                          elsif sort_by_alphanumeric
                            pagy(positions.order(
                                   "CASE WHEN name ~ '^[^A-Za-z]+' THEN 0 ELSE 1 END ASC, REGEXP_REPLACE(name, '[^A-Za-z]+', '', 'g') ASC, " \
                                   "COALESCE(NULLIF(REGEXP_REPLACE(name, '\\D+', '', 'g'), ''), '0')::int ASC"
                                 ), items: params[:per_page])
                          else
                            pagy(positions.order('name ASC'), items: params[:per_page])
                          end
        render_success(data: positions, options: { meta: pagy_headers_hash(pagy) })
      end

      def create
        position = Position.new(resource_params)
        position.save
        render_json(data: position)
      end

      def update
        @position.update(resource_params)
        render_json(data: @position)
      end

      def destroy
        @position.discard
        render_json(data: @position)
      end

      private

      def resource_params
        params.require(:position).permit(:name, :description)
      end

      def set_position
        @position = Position.kept.friendly.find(params[:id])
      end
    end
  end
end
