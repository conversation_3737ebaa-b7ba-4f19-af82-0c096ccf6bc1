# frozen_string_literal: true

module Api
  class UsersController < Api::BaseController
    before_action :set_user, only: %w[edit update destroy]

    def index
      users = User.kept.includes(:role, avatar_attachment: :blob)
      users = users.joins(:role).where('LOWER(roles.name) != ?', 'admin') if params[:admin].present? && params[:admin] == 'false' && current_user.role.name.downcase != 'admin'

      pagy, users = params[:search_text].present? ? pagy(users.search_by_user(params[:search_text])) : pagy(users)
      render_success(data: users, options: { meta: pagy_headers_hash(pagy), include: [:role] })
    end

    # Ref: https://github.com/Netflix/fast_jsonapi/issues/172
    def profile
      render_json(data: @current_user, options: { include: [:user_contacts] })
    end

    def create
      user = User.new(resource_params)
      user.restrict_login = true if params[:user][:restrict_login_out_of_office].present?
      user.save
      render_json(data: user)
    end

    def update
      @user.restrict_login = true if params[:user][:restrict_login_out_of_office].present?
      @user.update(resource_params)
      render_json(data: @user)
    end

    def destroy
      @user.discard
      render_json(data: @user)
    end

    def profile_update
      @current_user.update(profile_params)
      remove_avatar
      render_json(data: @current_user, options: { include: [:user_contacts] })
    end

    private

    def resource_params
      whitelisted_params = %i[username email first_name last_name notification password role_id password_confirmation user_audit_logging restrict_login_out_of_office
                            restrict_type allow_login_from_time allow_login_to_time]
      whitelisted_params += [allowed_accounts: []]
      # It's optional to update the password while updating the user. So we will permit password related params
      # only when any of both of 'password' or 'password confirmation' is present
      if action_name == 'update' && params[:user][:password].blank? && params[:user][:password_confirmation].blank?
        whitelisted_params -= %i[password_confirmation password]
      end
      params.require(:user).permit(whitelisted_params)
    end

    def profile_params
      params.require(:user).permit(:avatar, :username, :first_name, :last_name, :birthday, :apartment, :street,
                                   :city, :state, :zipcode, :user_audit_logging,
                                   user_contacts_attributes: %i[contact_for contact_type contact_name id value])
    end

    def set_user
      @user = User.kept.find(params[:id])
    end

    def remove_avatar
      return if params[:user][:remove_avatar] != 'true' || !@current_user.avatar.attached?

      @current_user.avatar.purge
    end
  end
end
