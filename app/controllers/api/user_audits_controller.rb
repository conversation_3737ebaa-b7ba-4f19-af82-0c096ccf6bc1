# frozen_string_literal: true

module Api
  class UserAuditsController < Api::BaseController
    before_action :set_user_audit, only: %i[show]

    def index
      # Getting UserAudit rights based on the roles.
      rights = current_account.user_audit_models['models']

      # Excluding some values from the UserAudit.
      no_logging_users = User.where(user_audit_logging: false).pluck(:id)

      # Combining all the above logics.
      user_audit = UserAudit.includes(:user, :employee).kept.joins('INNER JOIN users ON CAST(users.id AS integer) = CAST(versions.whodunnit AS integer)').left_outer_joins(:employee).
        where('whodunnit is NOT NULL and object_changes is NOT NULL').where.not(whodunnit: no_logging_users).where(item_type: rights)
      # Searching logic.
      user_audit = search_columns(params[:search_text], user_audit, rights, current_account) if params[:search_text].present?

      user_audit = search_date(params[:search_date_from], params[:search_date_to], user_audit) if params[:search_date_from].present? && params[:search_date_to].present?

      pagy, user_audits = pagy(user_audit, items: 25)
      render_success(data: user_audits, options: { meta: pagy_headers_hash(pagy), params: { action_name: 'index' } })
    end

    def show
      render_success(data: @user_audit, options: { params: { action_name: 'show' } })
    end

    private

    def set_user_audit
      @user_audit = UserAudit.includes(:user).find(params[:id])
    end

    def query_build
      "SELECT id FROM versions WHERE item_type = 'Contact' AND object_changes LIKE '%\nvalue:\n-\n- ''%'
      OR object_changes LIKE '%\nvalue:\n- \n- ''%' OR object_changes LIKE '%\nvalue:\n- ''\n- ''%'"
    end

    def current_account
      @current_account ||= Account.find_by(subdomain: Apartment::Tenant.current)
    end

    def search_columns(search_text, user_audit, rights, current_account)
      regex = /(created|updated|deleted)/i
      sliced_value = search_text.downcase.slice!(regex)
      if sliced_value.present?
        sliced_value = sliced_value[0..-2].downcase
        remaining_value = search_text.gsub(regex, '')
        remaining_value[0] = '' if remaining_value[0] == ' '
        search_text = remaining_value.present? ? remaining_value : search_text
      end

      item_key = if search_text.downcase.include?('contact')
                   'contact'
                 else
                   search_title(rights, current_account, search_text)
                 end
      user_audit = user_audit.where('lower(versions.event) = ?', sliced_value) if sliced_value.present?

      suffixes = %w[Mr Mr. Ms Ms. Jr Jr. I II III IV V]
      search_text_split = search_text.split
      suffix = suffixes.include?(search_text_split.last) ? search_text_split.last : nil
      text = "lower(users.username) LIKE '%#{search_text.downcase}%' OR lower(versions.item_type) = '#{item_key}'"

      text += search_by_name_query(search_text, search_text_split)

      text += " OR lower(employees.suffix) LIKE '%#{suffix.downcase}%'" if suffix

      user_audit.where(text)
    end

    def build_condition(first_name, middle_name, last_name)
      condition = []
      condition << "lower(employees.first_name) LIKE '%#{first_name.downcase}%'" if first_name
      condition << "lower(employees.middle_name) LIKE '%#{middle_name.downcase}%'" if middle_name
      condition << "lower(employees.last_name) LIKE '%#{last_name.downcase}%'" if last_name
      "(#{condition.join(' AND ')})"
    end

    def search_by_name_query(search_text, search_text_split)
      conditions = case search_text_split.count
                   when 4
                     [
                       build_condition(search_text_split[0..1].join(' '), search_text_split[2], search_text_split[3]),
                       build_condition(search_text_split[0..1].join(' '), nil, search_text_split[2..3].join(' ')),
                       build_condition(search_text_split[0], search_text_split[1], search_text_split[2..3].join(' '))
                     ]
                   when 3
                     [
                       build_condition(search_text_split[0], search_text_split[1], search_text_split[2]),
                       build_condition(search_text_split[0..1].join(' '), nil, search_text_split[2]),
                       build_condition(search_text_split[0], nil, search_text_split[1..2].join(' '))
                     ]
                   when 2
                     [build_condition(search_text_split[0], nil, search_text_split[1])]
                   else
                     ["lower(employees.first_name) LIKE '%#{search_text.downcase}%'", "lower(employees.last_name) LIKE '%#{search_text.downcase}%'"]
                   end
      ' OR ' + conditions.join(' OR ')
    end

    def search_title(rights, current_account, search_text)
      item_hash = {}
      current_account = current_account.saas_json.dig('schema')
      rights = rights.map { |x| x.downcase }
      current_account.each { |key, value| item_hash[key] = (current_account[key]['key_name'])&.delete(' ')&.downcase if current_account[key]['key_name'].present? }
      if (item_type = item_hash.select { |key, value| key if value&.include?(search_text.delete(' ').downcase) }).present?
        item_key = item_type.keys.first.singularize.titleize.delete(' ').downcase
        unless rights.include?(item_key.downcase)
          if rights.include?("employee#{item_key}")
            item_key = "employee#{item_key}"
          end
        end
      end
      item_key
    end

    def search_date(from_date, to_date, user_audit)
      if from_date.present? && to_date.present?
        text = "DATE(versions.created_at) >= '#{from_date}' and DATE(versions.created_at) <= '#{to_date}'"
      elsif from_date.present? && to_date.blank?
        text = "DATE(versions.created_at) >= '#{from_date}'"
      elsif from_date.blank? && to_date.present?
        text = "DATE(versions.created_at) <= '#{to_date}'"
      end

      user_audit.where(text)
    end
  end
end