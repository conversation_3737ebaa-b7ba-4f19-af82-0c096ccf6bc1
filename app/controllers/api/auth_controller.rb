# frozen_string_literal: true

module Api
  class AuthController < Api::BaseController
    skip_before_action :authenticate_and_set_user, only: :create
    skip_load_and_authorize_resource
    before_action :find_user, only: [:create]

    def create
      if @current_user.valid_password?(params[:password])
        if (@current_user.role.present? && @current_user.role.name == 'No Access') || (out_of_office = check_login_out_of_office) == true
          if out_of_office == true
            render_error('Login Restricted')
          else
            render_error("You don't have access to the system")
          end
        else
          create_token_and_set_cookie(out_of_office == false ? true: nil )
          @current_user.update_tracked_fields!(request)
          render_success(data: @current_user, message: 'Signed in successfully')
        end
      else
        render_error('Invalid login credentials')
      end
    end

    def refresh_jwt_token
      out_of_office = check_login_out_of_office
      create_token_and_set_cookie(out_of_office == false ? true: nil )
      # Blacklist the current token from future use.
      # When two refresh token API occurs at the same time, Unauthorised issue produced because of the blacklisted_tokens.
      # @current_user.blacklisted_tokens.create(token: request.cookies['fusesystems_session'], expire_at: @decoded_token[:exp])
      render_success(message: 'Token refreshed successfully')
    end

    def destroy
      # Blacklist the current token from future use.
      @current_user.blacklisted_tokens.create(token: request.cookies['fusesystems_session'], expire_at: @decoded_token[:exp])

      render_success(message: 'Logged out successfully!')
    end

    # The main purpose of this API is not to allow the user to view the requested page if the session got expired.
    # 'Where this API needed?'
    # (*) When a user opens a new browser tab - FE will hit this API and based on the response, they will redirect to
    # login page or the corresponding page which the user requested.
    #
    # (*) When refreshing browser - we have 30 mins as session time and say, if the logged in user refreshes
    # the page after 30 mins, FE should not show them the page UI and they have to redirect the user to the login page.
    def authenticate
      render_success(data: @current_user)
    end

    def find_user
      @current_user = User.kept.find_by(email: params[:email].downcase.strip) if params[:email].present?
      return if @current_user

      render_error('Invalid login credentials')
    end

    def check_login_out_of_office
      restrict_login = @current_user.restrict_login_out_of_office
      return nil if restrict_login.blank? || restrict_login == false

      restrict_type = @current_user.restrict_type
      from_time, to_time = time_in_est(@current_user.allow_login_from_time, @current_user.allow_login_to_time)
      current_est_time = Time.now.in_time_zone('Eastern Time (US & Canada)')
      if restrict_type.to_s == 'allow_login_in_weekends' && ((from_time..to_time) === current_est_time) == false
        return true
      elsif restrict_type.to_s == 'login_only_in_weekdays' && [6, 7].exclude?(current_est_time.day) && ((from_time..to_time) === current_est_time) == false
        return true
      end
      return false
    end
  end
end
