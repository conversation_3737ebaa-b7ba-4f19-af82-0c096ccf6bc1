# frozen_string_literal: true

module Api
  module Employees
    class EmployeeDisciplineStepsController < Api::BaseController
      include Api::ApiRescues
      before_action :set_employee_discipline_step, only: :update

      def create
        employee_discipline_step = EmployeeDisciplineStep.new(resource_params)
        employee_discipline_step.save
        render_json(data: employee_discipline_step)
      end

      def update
        @employee_discipline_step.update(resource_params)
        render_json(data: @employee_discipline_step)
      end

      private

      def resource_params
        resource_param = params.require(:employee_discipline_step).permit(:date, :recommended_notes, :is_settled, :is_pending, :step, :win, :loss,
                                                                          :employee_discipline_setting_id, :discipline_status_id, :hearing,
                                                                          :pba_member, :pa_member, :arbitrator_id, files: [])
        resource_param[:files] = [] if params[:employee_discipline_step][:remove_all_files] == 'true'
        resource_param
      end

      def set_employee_discipline_step
        @employee_discipline_step = EmployeeDisciplineStep.kept.find(params[:id])
      end
    end
  end
end
