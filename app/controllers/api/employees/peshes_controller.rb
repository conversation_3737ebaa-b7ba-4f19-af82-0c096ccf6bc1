module Api
  module Employees
    class PeshesController < Api::BaseController
      include Api::ApiRescues
      before_action :set_pesh, only: %w[update destroy]

      def index
        peshes = Pesh.includes(:files_attachments).kept.where(employee_id: params[:employee_id])
        pagy, peshes = pagy(peshes)
        render_success(data: peshes, options: { change_request: 'pesh', meta: pagy_headers_hash(pagy) })
      end

      def show
        render_json(data: @pesh)
      end

      def create
        pesh = Pesh.new(resource_params)
        pesh.save
        render_json(data: pesh)
      end

      def update
        @pesh.update(resource_params)
        render_json(data: @pesh)
      end

      def destroy
        @pesh.discard
        render_json(data: @pesh)
      end

      private

      def set_pesh
        @pesh = Pesh.kept.find(params[:id])
      end

      def resource_params
        resource_param = params.require(:pesh).permit(:complaint, :date, :remarks, :office_id, :employee_id, files: [])
        resource_param[:files] = [] if params[:pesh][:remove_all_files] == 'true'
        resource_param
      end
    end
  end
end