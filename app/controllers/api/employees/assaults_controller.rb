# frozen_string_literal: true

module Api
  module Employees
    class AssaultsController < Api::BaseController
      include Api::ApiRescues
      before_action :set_assault, only: [:show, :update, :destroy]

      def index
        assaults = Assault.kept.where(employee_id: params[:employee_id])
        pagy, assaults = pagy(assaults)
        render_success(data: assaults, options: { meta: pagy_headers_hash(pagy), change_request: 'assault', include: [:witnesses] })
      end

      def show
        render_json(data: @assault, options: { include: [:witnesses], change_request: 'assault' })
      end

      def create
        assault = Assault.new(resource_params)
        assault.save
        render_json(data: assault, options: { include: [:witnesses] })
      end

      def update
        @assault.update(resource_params)
        render_json(data: @assault, options: { include: [:witnesses] })
      end

      def destroy
        @assault.discard
        render_json(data: @assault)
      end

      private

      def set_assault
        @assault = Assault.find(params[:id])
      end

      def resource_params
        params.require(:assault).permit(:location, :time, :date, :physical, :verbal, :description, :incident_reported_to, :incident_report,
                                        :lodi_pack, :delegate, :employee_id, witnesses_attributes: [ :name, :phone, :address, :id])
      end
    end
  end
end