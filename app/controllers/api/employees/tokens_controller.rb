# frozen_string_literal: true

module Api
  module Employees
    class TokensController < Doorkeeper::TokensController
      include Api::ApiRescues
      include Api::ResponseFormatter

      before_action :doorkeeper_authorize!, only: [:logout]
      after_action :remove_device, only: [:logout]

      def login
        employee = Employee.find_for_database_authentication(username: params[:username])

        case
        when employee.nil? || !employee.valid_password?(params[:password])
          render_error('Invalid login credentials')
        when employee&.inactive_message == :unconfirmed
          render_error('You have to confirm your username before continuing.')
        when employee.present? && employee.enable_mobile_access == false
          render_error("You don't have access to the application")
        when !employee.active_for_authentication?
          create
        else
          create
          employee.update_columns(app_downloaded: true) if employee.app_downloaded == false
        end
      end

      def refresh
        create
      end

      def logout
        # Follow doorkeeper-5.1.0 revoke method, different from the latest code on the repo on 6 Sept 2019

        params[:token] = access_token
        revoke
      end

      private

      def access_token
        pattern = /^Bearer /
        header = request.headers['Authorization']
        header.gsub(pattern, '') if header && header.match(pattern)
      end

      def remove_device
        device = Device.where(employee_id: doorkeeper_token.resource_owner_id, os_type: params[:device][:os_type], device_token: params[:device][:device_token]).first
        device.destroy if device.present?
      end
    end
  end
end