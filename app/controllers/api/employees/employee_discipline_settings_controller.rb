# frozen_string_literal: true

module Api
  module Employees
    class EmployeeDisciplineSettingsController < Api::BaseController
      include Api::ApiRescues
      before_action :set_employee_discipline_settings, only: %w[update destroy]

      # rubocop:disable Layout/LineLength
      def index
        return render_error('Employee id is missing') if params[:employee_id].blank?

        employee_discipline_settings = if params[:search_for_status].present?
                                         EmployeeDisciplineSetting.where(employee_id: params[:employee_id]).search_for_status(params[:search_for_status])
                                       else
                                         EmployeeDisciplineSetting.includes(:discipline_setting, :discipline_charge, employee_discipline_steps: [:files_attachments], files_attachments: :blob)
                                                                  .kept.where(employee_id: params[:employee_id]).order('id DESC')
                                       end
        pagy, employee_discipline_settings = pagy(employee_discipline_settings, items: params[:per_page])
        render_success(data: employee_discipline_settings, options: { change_request: %w[employee_discipline_setting employee_discipline_step],
                                                                      meta: pagy_headers_hash(pagy), include: [:employee_discipline_steps] })
      end

      # rubocop:enable Layout/LineLength

      def create
        employee_discipline_setting = EmployeeDisciplineSetting.new(resource_params)
        employee_discipline_setting.save
        render_json(data: employee_discipline_setting)
      end

      def update
        @employee_discipline_setting.update(resource_params)
        render_json(data: @employee_discipline_setting)
      end

      def destroy
        @employee_discipline_setting.discard
        @employee_discipline_setting.files.purge_later
        render_json(data: @employee_discipline_setting)
      end

      private

      def resource_params
        resource_param = params.require(:employee_discipline_setting)
                               .permit(:employee_id, :description, :discipline_setting_id, :discipline_charge_id, :charge,
                                       :date, :dan_number, :recommended_penalty, :was_employee_pds, :case_and_abeyance,
                                       :ta_implemented, :abandonment_hearing, :filed_olr, files: [])
        resource_param[:files] = [] if params[:employee_discipline_setting][:remove_all_files] == 'true'
        resource_param
      end

      def set_employee_discipline_settings
        @employee_discipline_setting = EmployeeDisciplineSetting
                                       .kept.includes(:discipline_setting, files_attachments: :blob).find(params[:id])
      end
    end
  end
end
