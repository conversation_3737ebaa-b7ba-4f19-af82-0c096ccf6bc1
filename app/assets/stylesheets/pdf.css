body {
    font-weight: normal;
    font-family: 'Ubuntu', sans-serif;
    font-size: 14px;
}

hr {
    border-bottom: 3px solid #040F30;
}

table {
    border-collapse: collapse;
    position: relative;
    width: 100%;
}

table thead {
    display: table-header-group;
}

table thead th {
    font-size: .75rem;
    text-transform: uppercase;
    font-weight: bold;
    letter-spacing: 1px;
    border: none;
}

table th, table td {
    border-top: 1px solid #E5E5E5;
    padding: 0.75rem;
    text-align: left;
    vertical-align: top;
}

table tfoot {
    display: table-row-group;
}

table tr {
    page-break-inside: avoid;
}

table.header {
    margin-top: 0;
}

table.header tbody td .block-logo {
    text-align: left;
}

table.header tbody td .block-logo img {
    max-height: 70px;
    max-width: 300px;
    object-fit: contain;
    object-position: left;
}

table.header tbody td .block-title {
    font-size: 18px;
    font-weight: bold;
    text-align: right;
}

table.header th, table.header td {
    border-top: 0;
    padding: 0;
}

.font-weight-400 {
    font-weight: 400;
}

.no-border {
    border: 0;
}

.nobreak {
    position: relative;
    page-break-inside: avoid;
}

.nobreak:before {
    clear: both;
}

.float-left {
    float: left;
}

.text-blue {
    color: #0B71B2;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.w-5 {
    width: 5%;
}

.w-13 {
    width: 13%;
}

.w-16 {
    width: 16%;
}

.w-19 {
    width: 19%;
}

.w-15 {
    width: 15%;
}

.w-20 {
    width: 20%;
}

.w-30 {
    width: 30%;
}

.w-40 {
    width: 40%;
}

.line-height-2 {
    line-height: 2;
}

.line-height-3 {
    line-height: 2;
    padding-left: 7px;
}

/*** Page Specific Styles ***/
table.table-regular {
    padding-left: 0;
    padding-right: 0;
}

table.table-regular tr {
    list-style: none;
    padding: 0;
    margin: 0;
    font-size: 14px;
    color: #86837E;
    font-weight: normal;
    font-family: sans-serif;
    text-align: left;
    line-height: 18px;
}

table.table-regular tr th {
    border-bottom: 2px solid #E5E5E5;
    padding: 8px;
    overflow: auto;
    zoom: 1;
    font-size: 10px;
    text-transform: uppercase;
    color: #0B0D0E;
    letter-spacing: .5px;
    font-weight: bold;
}

table.table-regular tr td {
    padding-left: 0;
    padding-right: 0;
    background: #FFFFFF;
    border-bottom: 1px solid #E5E5E5;
    padding: 8px;
    overflow: auto;
    zoom: 1;
}

table.table-regular tr td h1 {
    font-size: 14px;
    color: #0B71B2;
    font-weight: bold;
    font-family: sans-serif;
    text-align: left;
    padding: 0;
    margin: 0;
}

table.table-employee-benefits th, table.table-employee-benefits td {
    border-top: 0;
}

table.table-employee-benefits ul {
    list-style: none;
    padding: 0;
    margin: 0;
    font-size: 14px;
    color: #0B0D0E;
    font-weight: normal;
    font-family: sans-serif;
    text-align: left;
    line-height: 18px;
}

table.table-employee-benefits ul li.block-employee-details {
    color: #86837E;
    border-bottom: 2px solid #E5E5E5;
    padding: 8px;
    overflow: auto;
    zoom: 1;
}

table.table-employee-benefits ul li.block-benefit-details {
    color: #86837E;
    padding: 8px;
    overflow: auto;
    zoom: 1;
    border-bottom: 1px solid #E5E5E5;
}

table.table-employee-benefits ul li.block-disbursements-heading,
table.table-employee-benefits ul li.block-benefit-coverages-heading {
    border-bottom: 2px solid #E5E5E5;
    padding: 8px;
    overflow: auto;
    zoom: 1;
    font-size: 10px;
    text-transform: uppercase;
    color: #0B0D0E;
    letter-spacing: .5px;
    font-weight: bold;
}

table.table-employee-benefits ul li.block-disbursement,
table.table-employee-benefits ul li.block-benefit-coverages {
    color: #86837E;
    border-bottom: 1px solid #E5E5E5;
    padding: 8px;
    overflow: auto;
    zoom: 1;
}

table.table-employee-benefits ul li.block-total {
    border-bottom: 1px solid #E5E5E5;
    padding: 8px;
    overflow: auto;
    zoom: 1;
}

table.table-employee-benefits ul li.block-employee-total {
    background: #F5F8FA;
    padding: 8px;
    overflow: auto;
    zoom: 1;
}

table.table-employee-benefits .block-total-summary {
    padding-bottom: 0;
    padding-top: 0;
}

table.table-employee-benefits .block-total-summary h3 {
    color: #0B71B2;
    padding-bottom: 0;
    padding-top: 0;
    margin-bottom: 0;
    margin-top: 0;
}

table .block-total-summary h3 {
    color: #0B71B2;
}

.grievance-overall-download ul {
    list-style-type: none;
    padding-left: 0;
    margin-top: 10px;
}

.grievance-overall-download ul li {
    background-color: #f7fafc;
    margin-bottom: 5px;
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 5px;
}

.grievance-overall-download ul li strong {
    color: #2b6cb0;
    font-weight: 600;
}

.grievance-overall-download h1 {
    text-align: center;
    margin-bottom: 30px;
    font-size: 28px;
    color: #2a4365;
    border-bottom: 2px solid #cbd5e0;
    padding-bottom: 10px;
}

h2 .grievance-overall-download h2 {
    margin-top: 30px;
    font-size: 22px;
    color: #2d3748;
    border-left: 5px solid #4299e1;
    padding-left: 10px;
}

.grievance-overall-download h3 {
    font-size: 18px;
    color: #4a5568;
    margin: 20px 0 10px 0;
}