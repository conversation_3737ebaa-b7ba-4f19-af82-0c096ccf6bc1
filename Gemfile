# frozen_string_literal: true

source 'https://rubygems.org'
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

ruby '2.6.6'

# Bundle edge Rails instead: gem 'rails', github: 'rails/rails'
gem 'rails', '~> 6.0.2', '>= 6.0.2.1'
# Use postgresql as the database for Active Record
gem 'pg', '>= 0.18', '< 2.0'
# Use Puma as the app server
gem 'puma', '5.2.0'
gem 'nio4r', '2.5.4'
# Build JSON APIs with ease. Read more: https://github.com/rails/jbuilder
# gem 'jbuilder', '~> 2.7'
# Use Active Model has_secure_password
# gem 'bcrypt', '~> 3.1.7'

# In-Memory Database
gem 'hiredis'
gem 'redis'

# API related gems
gem 'fast_jsonapi'

# Security related gems
gem 'cancancan'
gem 'devise'
gem 'jwt', '~> 2.2', '>= 2.2.1' # For token based authentication

# Data related gems
gem 'activestorage-validator'
gem 'discard'
gem 'friendly_id'
gem 'pagy'
gem 'paper_trail'
gem 'pg_search'
gem 'strong_migrations'

# Enable Multi-Tenancy
gem 'apartment', github: 'influitive/apartment', branch: 'development'

# Assets and template related gems
gem 'aws-sdk-s3', require: false
gem 'aws-sdk-ses'
gem 'aws-sdk-sns', '~> 1.1'
gem 'mini_racer'
gem 'plivo', '>= 4.3.0'
gem 'prawn-labels', path: 'vendor/bundle/prawn-labels'
gem 'spreadsheet'
# gem 'uglifier'
# gem 'webpacker'
gem 'wicked_pdf'
gem 'wkhtmltopdf-binary'

# Others
gem 'appsignal', '3.3.11'
gem 'foreman'
gem 'image_processing'
gem 'rake-progressbar'
gem 'sidekiq'

# Use Active Storage variant
# gem 'image_processing', '~> 1.2'

# Reduces boot times through caching; required in config/boot.rb
gem 'bootsnap', '>= 1.4.2', require: false

# Use Rack CORS for handling Cross-Origin Resource Sharing (CORS), making cross-origin AJAX possible
gem 'rack-cors'

group ENV['APP_ENV'] == 'development' ? :production : :development do
  gem 'bullet'
  gem 'slack-notifier'
end

group :development, :test do
  # Call 'byebug' anywhere in the code to stop execution and get a debugger console
  gem 'byebug', platforms: %i[mri mingw x64_mingw]

  gem 'capybara'
  gem 'capybara-screenshot'
  gem 'coveralls', require: false
  gem 'database_cleaner'
  gem 'dotenv-rails'
  gem 'fabrication'
  gem 'faker'
  gem 'guard-rspec'
  gem 'json-schema'
  gem 'overcommit'
  gem 'poltergeist'
  gem 'priscilla'
  gem 'rails-controller-testing'
  gem 'rspec-rails'
  gem 'rspec-retry'
  gem 'selenium-webdriver', require: !%w[poltergeist poltergeist_errors_ok webkit].include?(ENV['DRIVER'])
  gem 'shoulda-kept-respond-with-content-type'
  gem 'shoulda-matchers'
  gem 'simplecov'
  gem 'spring-commands-rspec'
  gem 'term-ansicolor'
  gem 'webdrivers'
  gem 'webmock'
end

group :development do
  gem 'listen', '>= 3.0.5', '< 3.2'
  # Spring speeds up development by keeping your application running in the background. Read more: https://github.com/rails/spring
  gem 'spring'
  gem 'spring-watcher-listen', '~> 2.0.0'

  gem 'rails-erd'

  gem 'awesome_print'
  gem 'better_errors'
  gem 'hirb'
  gem 'i18n-tasks'
  gem 'meta_request'
  gem 'rainbow'

  gem 'pry'
  gem 'pry-byebug'
  gem 'pry-doc'
  gem 'pry-rails'
  gem 'pry-rescue'
  gem 'pry-stack_explorer'

  gem 'brakeman'
  gem 'capistrano'
  gem 'capistrano-ext'
  gem 'capistrano-rails'
  gem 'fasterer'
  gem 'flamegraph'
  gem 'memory_profiler'
  gem 'oink'
  gem 'rack-mini-profiler', require: false
  gem 'reek'
  gem 'rubocop'
  gem 'ruby-lint'
  gem 'ruby-prof'
  gem 'stackprof'

  gem 'guard-rake'
  gem 'letter_opener'
  gem 'web-console'
end

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem 'tzinfo-data', platforms: %i[mingw mswin x64_mingw jruby]

# Doorkeeper is an oAuth2 provider
gem 'doorkeeper', '~> 5.2.1'
gem 'doorkeeper-jwt'

# Job scheduler
gem 'rufus-scheduler', '~> 3.2'

# SFTP Connection
gem 'net-sftp'

gem "bundle-audit", "~> 0.1.0"

gem "activerecord-import", "~> 1.5"

gem 'pghero', '~> 2.8', '>= 2.8.3'

gem "pg_query", ">= 2"

gem 'ice_cube', '~> 0.16.4'

gem 'sendgrid-ruby'