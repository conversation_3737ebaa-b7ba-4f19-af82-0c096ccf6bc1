# frozen_string_literal: true

require 'csv'
desc 'import data'

# rubocop:disable Metrics/BlockLength, Style/RescueStandardError
task :send_member_credential_mailer, %i[account employment_statuses file_path] => :environment do |_t, args|
  # Argument employment_statuses can be passed one or many as like below.
  # bundle exec rake 'send_member_credential_mailer[account, active]'
  #                             or
  # bundle exec rake 'send_member_credential_mailer[account, active retired terminated]'

  account = args[:account]
  @errors = {}
  file_path = args[:file_path] || ''
  Apartment::Tenant.switch!(account)
  employment_statuses = args[:employment_statuses].split
  employment_status_query = 'employee_employment_statuses.employment_status_id in (?) and (employee_employment_statuses.end_date is NULL or employee_employment_statuses.end_date > ?)'
  if file_path.present?
    employees = find_employees_by_name(file_path, employment_status_query)
  else
    employees = Employee.kept.includes(:employee_employment_statuses).where(employment_status_query,
                                                                            EmploymentStatus.kept.where('lower(name) in (?)', employment_statuses).pluck(:id), Date.today).references(:employee_employment_statuses)
  end
  mail_sent_members = []
  employees.each do |employee|
    @row_number = employee.full_name

    personal_email = employee.contacts.where(contact_type: 'email', contact_for: 'work').first
    if personal_email&.value.blank?
      member_feed_errors('Mail is blank')
      next
    elsif employee.email_opt_out == true
      member_feed_errors('Email Opt Out box has been checked')
      next
    elsif employee.enable_mobile_access == false
      member_feed_errors('Mobile Access is not enabled for this Member')
      next
    end

    password = Devise.friendly_token(8)
    username = (employee.username.present? ? employee.username : personal_email.value)
    employee.assign_attributes(username: username, password: password, password_confirmation: password)
    employee.save!(validate: false)

    mail_sent_members << employee.full_name if MemberCredentialsMailer.details(username, password, personal_email.value, account).deliver_later
  rescue => e
    p @row_number, e.message
    member_feed_errors(e.message)
  end

  generate_csv_errors("#{account}_send_member_credential_mailer_errors", ['Errors'], @errors)
  generate_csv_errors("#{account}_mail_succeed_members", ['Member Full Name'], mail_sent_members)
end
# rubocop:enable Metrics/BlockLength, Style/RescueStandardError

def find_employees_by_name(file_path, employment_status_query)
  employees_array = []
  employment_status_id = EmploymentStatus.kept.where('lower(name) = ?', 'active').pluck(:id)
  CSV.foreach(file_path, headers: true) do |row|
    @row_number = row['Member Name']
    if row['First Name'].present?
      f_name = row['First Name'] || ''
      l_name = row['Last Name'] || ''
      m_name = row['Middle Name'] || ''
    else
      name_array = @row_number.split
      f_name = name_array.first
      l_name = name_array.last
      m_name = name_array.second if name_array.count == 3
    end

    if m_name.present?
      employees = Employee.kept.where('lower(first_name) = ? AND lower(last_name) = ? AND lower(middle_name) = ?', f_name.downcase, l_name.downcase, m_name.downcase)
      employees = Employee.kept.where('lower(first_name) = ? AND lower(last_name) = ?', "%#{f_name.downcase} #{m_name.downcase}%", l_name.downcase) if employees.empty?
      employees = Employee.kept.where('lower(first_name) = ? AND lower(last_name) = ?', f_name.downcase, "%#{m_name.downcase} #{l_name.downcase}%") if employees.empty?
    else
      employees = Employee.kept.where('lower(first_name) = ? AND lower(last_name) = ?', f_name.downcase, l_name.downcase)
    end

    employees = employees.joins(:employee_employment_statuses).where(employment_status_query, employment_status_id, Date.today)

    valid_employee = check_employees_count(employees)
    employees_array << valid_employee if valid_employee
  end
  employees_array
end

def check_employees_count(employees)
  if employees.blank?
    member_feed_errors('Invalid Employee or Employee was missing')
    nil
  elsif employees.count > 1
    member_feed_errors('More than One Employee found')
    nil
  else
    employees.first
  end
end

def member_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end

def generate_csv_errors(file_name, headers, objects)
  CSV.open("#{Rails.root}/#{file_name}_#{Time.now.strftime('%d-%m-%Y %H:%M:%S')}.csv", 'w') do |csv|
    csv << headers

    objects.each do |object|
      csv << [object].flatten
    end
  end
end

# bundle exec rake 'send_member_credential_mailer[sccoa, active, SCCOA Start Date 51324.csv]'
# bundle exec rake 'send_member_credential_mailer[nyccoba, active, Nyccoba_member_credentials_11_07_2025.csv]'
