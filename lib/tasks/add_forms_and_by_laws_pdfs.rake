# frozen_string_literal: true

desc 'Adding the forms and bylaws PDFS'
task :add_forms_and_bylaws_pdfs, %i[account file_path form_or_by_law] => [:environment] do |_t, args|
  file_path = "#{Rails.root}/#{args[:file_path]}"
  account = args[:account]
  form_or_by_law = args[:form_or_by_law]

  Apartment::Tenant.switch!(account)

  file_type = if form_or_by_law == 'form'
                1
              elsif form_or_by_law == 'by_law'
                2
              end

  Dir.foreach(file_path) do |filename|
    next unless filename.include?('.pdf')

    begin
      form = Form.where(description: filename, file_type: file_type).first_or_create
      form.file.attach(io: File.open("#{file_path}/#{filename}"), filename: filename)
      form.save
    rescue NoMethodError, NameError => e
      puts e, '', '', filename
    end
  end
end

task :add_number_or_links_with_heading, %i[account file_path attachment_present file_dir_path] => [:environment] do |_t, args|
  account = args[:account] ## domains
  attachment_present = args[:attachment_present] ## true if attachments present
  file = File.read(args[:file_path]) ## CSV file path. S3 URL for CSV eg: s3://fuse-production-assets/NameAndNumbersWithHeadings.csv
  file_directory_path = "#{Rails.root}/#{args[:file_dir_path]}" unless args[:file_dir_path] == 'false' ## if attachments present, Directory Path. OtherWise false. Eg: forms in SCCOA.
  csv_file ||= CSV.parse(file, headers: true)
  @errors = {}

  Apartment::Tenant.switch!(account)
  csv_file.each do |row|
    @row_number = row['Name'] || row['Number'] || row['Link']
    name = row['Name']
    heading = row['Heading']
    link = row['Link']
    number = row['Number']
    address = row['Address']
    file_name = row['File Name']
    file_type = row['File Type']
    form = Form.where(name: name, file_type: file_type, heading: heading, link: link, number: number, address: address).first_or_create

    next unless attachment_present == 'true' && file_name.present?

    form.file.attach(io: File.open("#{file_directory_path}/#{file_name}"), filename: file_name)
    form.save

  rescue StandardError => e
    p @row_number, e.message
    local2507_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{Date.today}_add_number_or_links_with_heading_errors.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']

    @errors.each do |error|
      csv << error
    end
  end
end

# bundle exec rake 'add_forms_and_bylaws_pdfs[local2507, forms, form]'
# bundle exec rake 'add_forms_and_bylaws_pdfs[local2507, by_laws, by_law]'
# bundle exec rake 'add_number_or_links_with_heading[local2507, NameAndNumbersWithHeadings.csv, local2507, useful_phone_numbers, true , forms]' ## if attachments present.
# bundle exec rake 'add_number_or_links_with_heading[local2507, NameAndNumbersWithHeadings.csv, local2507, useful_phone_numbers, false , false]' ## if attachments blank.
# bundle exec rake 'add_number_or_links_with_heading[local2507, NameAndNumbersWithHeadings.csv, true , forms]'
