# frozen_string_literal: true

require 'csv'

desc 'import data'
task :import_data_all_in_one, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  csv_file.each do |row|
      old_employee = nil
      pass_number = row['Pass Number']

      pass_number = pass_number.rjust(6,'0') if pass_number.present?

      old_employee = Employee.where(a_number: pass_number).first if pass_number.present?

      next if old_employee.present?

      @row_number = row['COUNT']

      # Default values
      section = nil
      title = nil

      gender = check_gender(row['SEX'])
      unit = check_unit(row['Unit'])
      department = check_department(row['Department'])
      section = check_section(row['Section'], department.id) if department
      title = check_title(row['Title'], department.id, section.id) if section
      dob = parse_date(row['DOB'], 'DOB')
      ssn = parse_ssn(row['SSN'])
      ta_start_date = parse_date(row['TA Start Date'], 'TA Start Date')
      prom_prov = parse_date(row['Prom Prov'], 'Prom Prov')
      prom_perv = parse_date(row['Prom Perm'], 'Prom Perm')
      member_since = parse_date(row['Member Since'], 'Member Since')
      cell_phone = parse_phone(row['Personal Cell'])
      personal_email = row['Personal  E-Mail']
      work_email = row['Work E-Mail']
      zip_code = row['Zip Code']
      zip_code = zip_code.rjust(5,'0') if zip_code.present?

      employee = Employee.new

      employee.first_name = row['First Name']
      employee.last_name = row['Last Name']
      employee.middle_name = row['MI'] || ''
      employee.street = row['Address'] || ''
      employee.apartment = row['Apt'] || ''
      employee.city = row['City'] || ''
      employee.state = row['State'] || ''
      employee.zipcode = zip_code || ''
      employee.a_number = pass_number || ''
      employee.shield_number = row['BSCID'] || ''
      employee.title_code = row['Title Code']
      employee.janus_card = row['Janus Card'].present?
      employee.birthday = dob
      employee.social_security_number = ssn || ''
      employee.start_date = ta_start_date
      employee.member_since = member_since
      employee.prom_prov = prom_prov
      employee.prom_perm = prom_perv
      employee.unit = unit if unit
      employee.gender_id = gender.id if gender

      if employee.save
        employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE, value: cell_phone).save! if cell_phone
        employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL, value: personal_email).save! if personal_email
        employee.contacts.new(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL, value: work_email).save! if work_email
        employee.employee_departments.new(department_id: department.id).save(validate: false) if department
        employee.employee_sections.new(department_id: department.id, section_id: section.id).save(validate: false) if section
        employee.employee_titles.new(department_id: department.id, section_id: section.id, title_id: title.id).save(validate: false) if title
      else
        @errors[@row_number] = employee.errors.full_messages
      end

    rescue => e
      p @row_number, e.message
      feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_members_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end

def check_gender(gender_type)
  return nil unless gender_type.present?

  gender = Gender.where(name: gender_type).first_or_create

  gender

rescue => e
  feed_errors('GENDER ' + gender.errors.full_messages)
end

def check_unit(unit_name)
  return nil unless unit_name.present?

  unit = Unit.where(name: unit_name).first_or_create

  unit

rescue => e
  feed_errors('UNIT ' + unit.errors.full_messages)
end

def check_department(department_name)
  return nil unless department_name.present?

  department = Department.where(name: department_name).first_or_create

  department

rescue => e
  feed_errors('DEPARTMENT ' + department.errors.full_messages)
end

def check_section(section_name, department_id)
  return nil unless section_name.present?

  section = Section.where(department_id: department_id, name: section_name).first_or_create

  section

rescue => e
  feed_errors('SECTION ' + section.errors.full_messages)
end

def check_title(title_name, department_id, section_id)
  return nil unless title_name.present?

  title = Title.where(department_id: department_id, section_id: section_id, name: title_name).first_or_create

  title

rescue => e
  feed_errors('TITLE ' + title.errors.full_messages)
end

def check_paf(paf)
  if paf.present?
    paf = Pacf.where(name: 'Donating').first_or_create
  else
    paf = Pacf.where(name: 'Not Donating').first_or_create
  end

  paf

rescue => e
  feed_errors('PAF ' + paf.errors.full_messages)
end

def check_paf_opt_out(paf)
  return nil unless paf.present?

  paf_opt_out = Pacf.where(name: 'Opt-Out').first_or_create

  paf_opt_out

rescue => e
  feed_errors('PAF Opt Out ' + paf_opt_out.errors.full_messages)
end

def parse_amount(amount, type)
  return 0.00 unless amount.present?

  parsed_amount = amount.remove('US', ' ', '$', ' ').to_f

  parsed_amount

rescue => e
  feed_errors('AMOUNT - ' + type + ' ' + e.message)
end

def parse_date(date, type)
  return nil unless date.present?

  parsed_date = nil

  begin
    date_array = date.split('/')
    day = date_array[1].to_i
    month = date_array[0].to_i
    year = date_array[2].to_i

    if year.to_s.length == 2
      if year > 20 && year <= 99
        year = ('19' + year.to_s).to_i
      else
        year = ('20' + year.to_s).to_i
      end
    elsif year.to_s.length == 1
      year = ('200' + year.to_s).to_i
    elsif year.to_s.length > 4
      raise 'Invalid Year'
    end

    parsed_date = Date.new(year, month, day)
  rescue
    parsed_date = Date.parse(date)
  end

  parsed_date

rescue => e
  feed_errors('DATE - ' + type + ' ' + e.message)
end

def parse_phone(phone)
  return nil unless phone.present?

  phone.remove!('(', ')', ' ', '-')

  phone_number = '(' + phone.first(3) + ') ' + phone[3..5] + ' - ' + phone.last(4)

  phone_number

rescue => e
  feed_errors('PHONE ' + e.message)
end

def parse_ssn(ssn)
  return nil unless ssn.present?

  ssn.remove!('-', ' ')

  ssn_number = ssn.first(3) + ' - ' + ssn[3..4] + ' - ' + ssn.last(4)

  ssn_number
rescue => e
  feed_errors('SSN ' + e.message)
  raise
end

def feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end
