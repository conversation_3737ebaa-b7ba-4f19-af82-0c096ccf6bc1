# frozen_string_literal: true

require 'csv'

desc 'import data'

# rails "sccea_update_court_location[Names and Work Locations.csv]"
task :sccea_update_court_location, [:file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!('sccea')

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  # First Name,Middle Name,Last Name,Work Locations
  csv_file.each do |row|

    first_name = row['First Name']
    last_name = row['Last Name']
    middle_name = row['Middle Name']
    court_location = check_office(row['Work Locations'])

    @current_account = Account.find_by(subdomain: Apartment::Tenant.current)

    @row_number = first_name + ' ' + last_name

    employees = Employee.where('first_name ilike ? and last_name ilike ?', first_name, last_name)

    if employees.present?
      if employees.count > 1
        employees = employees.where('middle_name ilike ?', middle_name) if middle_name.present?

        if employees.present?
          employee = employees.first
        else
          feed_errors('Member not found')
        end
      else
        employee = employees.first
      end
    else
      feed_errors('Member not found')
    end

    if employee.present?
      employee.employee_offices.new(office_id: court_location.id).save(validate: false) if court_location.present?
    end

  end

  file_name = args[:file_path].split('.').first

  CSV.open("#{Rails.root}/#{Apartment::Tenant.current}_#{Date.today}_#{file_name}_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end

def check_office(office_name)
  return nil unless office_name.present?

  office = Office.where(name: office_name).first_or_create

  office

rescue => e
  feed_errors('OFFICE ' + office.errors.full_messages)
end

def feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = message
  end
end
