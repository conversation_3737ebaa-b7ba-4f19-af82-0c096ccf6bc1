# frozen_string_literal: true

desc 'Update Coverage Effective Dates'
task :update_coverage_effective_dates, [:account] => [:environment] do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  BenefitCoverage.kept.includes(:employee).where(effective_date: nil).each do |benefit_coverage|
    employee = benefit_coverage.employee
    if benefit_coverage.relationship.downcase == 'spouse'

      if employee.marital_status_date.present?
        benefit_coverage.update(effective_date: employee.marital_status_date, address: employee.full_address)
      elsif employee.start_date.present?
        benefit_coverage.update(effective_date: employee.start_date, address: employee.full_address)
      end
    elsif %w[child disabled_child step_child disabled_step_child].include?(benefit_coverage.relationship.downcase)
      benefit_coverage.update(effective_date: benefit_coverage.birthday, address: employee.full_address) if benefit_coverage.birthday.present?
    end
  end
end
