# frozen_string_literal: true

require 'csv'

desc 'import employee data to versions'
task :employee_id_update_to_versions, [:account] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  @errors = {}

  UserAudit.where("whodunnit IS NOT NULL and employee_id IS NULL").find_each do |version|
    if version.item_type == 'Employee'
      employee_id = version.item&.id
    elsif version.item_type.constantize._reflect_on_association(:employee).present?
      relation = version.item_type.constantize&._reflect_on_association(:employee)&.name&.to_s
      employee_id = version.item&.send(relation)&.id
    elsif %w[EmployeeGrievanceStep EmployeeDisciplineStep].include?(version.item_type)
      employee_id = version.item&.send("#{version.item_type.underscore.gsub('_step', '')}")&.employee&.id
    end

    version.update_columns(employee_id: employee_id)

  rescue StandardError => e
    member_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_employee_id_update_to_versions_errors.csv", 'w') do |csv|
    p csv << ['Row Number', 'Errors']

    @errors.each do |error|
      p csv << error
    end
  end
end

desc 'This rake task is for updating the null values to a empty string in a model for avoiding unusual updates'
task :update_null_values_to_empty_string, [:account] => [:environment] do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  @errors = {}
  update_columns_arr = []
  current_account = Account.find_by(subdomain: Apartment::Tenant.current)
  employee_json_keys = current_account.saas_json.dig('schema', 'employees').keys

  Contact.kept.where(contact_name: nil, contact_relationship: nil).update_all(contact_name: '', contact_relationship: '')

  employee_json_keys.each do |key|
    next unless Employee.column_for_attribute(key).type.to_s == 'string'

    update_columns_arr << key
  end

  update_columns_arr.each do |column|
    update_hash = {}
    @row_number = column
    update_hash[column.to_sym] = ''

    Employee.where("#{column} IS NULL").update_all(update_hash)
  rescue StandardError => e
    member_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_update_null_values_to_empty_string_errors.csv", 'w') do |csv|
    p csv << ['Row Number', 'Errors']

    @errors.each do |error|
      p csv << error
    end
  end
end

def member_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = message
  end
end
