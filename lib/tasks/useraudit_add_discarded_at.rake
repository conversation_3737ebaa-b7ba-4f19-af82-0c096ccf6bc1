# frozen_string_literal: true

require 'yaml'
require 'csv'

task :add_discarded_at_to_user_audit, [:account] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  @errors = {}
  batch_size = 1000

  current_account_contacts = (Account.find_by(subdomain: args[:account]).saas_json.dig('ui', 'employees', 'profile', 'contacts') ||
    Account.find_by(subdomain: args[:account]).saas_json.dig('ui', 'utlo_permitted_components_or_fields', 'profile', 'contacts'))
  all_contact_values = current_account_contacts.flat_map do |contact_hash|
    contact_hash.values.select { |v| v.is_a?(Array) }.flatten
  end

  query = "SELECT * FROM versions WHERE item_type = 'Contact' AND object_changes LIKE '%\nvalue:\n-\n- ''%'
          OR object_changes LIKE '%\nvalue:\n- \n- ''%' OR object_changes LIKE '%\nvalue:\n- ''\n- ''%'"
  user_audits = UserAudit.find_by_sql(query).uniq

  # here I don't use find_each because find_each is an ActiveRecord::Relation method, not an Array method
  # we get user_audits(variable) is an Array (because we used find_by_sql which returns an Array).
  user_audits.each_slice(batch_size) do |batch|
    batch.each do |user_audit|
      contact_obj = YAML.safe_load(user_audit.object_changes, permitted_classes: [Time, Date, Symbol], aliases: true)
      contact_for = contact_obj.dig('contact_for')&.last&.downcase
      contact_type = contact_obj.dig('contact_type')&.last&.downcase
      contact_key = "#{contact_for}_#{contact_type}"
      user_audit.update_columns(discarded_at: Time.current) unless all_contact_values.include?(contact_key)

    rescue StandardError => e
      @errors[user_audit.id] = e.message
    end
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_errors.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']
    @errors.each do |error|
      csv << error
    end
  end
end

task :update_council_district_members, [:account] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  council_member_data = [
    { name: 'Christopher Marte', website: 'https://council.nyc.gov/district-1/'},
    { name: 'Carlina Rivera', website: 'https://council.nyc.gov/district-2/' },
    { name: 'Erik Bottcher', website: 'https://council.nyc.gov/district-3/'},
    { name: 'Keith Powers', website: 'https://council.nyc.gov/district-4/'},
    { name: 'Julie Menin', website: 'https://council.nyc.gov/district-5/'},
    { name: 'Gale A. Brewer', website: 'https://council.nyc.gov/district-6/'},
    { name: 'Shaun Abreu', website: 'https://council.nyc.gov/district-7/'},
    { name: 'Diana Ayala', website: 'https://council.nyc.gov/district-8/'},
    { name: 'Yusef Salaam', website: 'https://council.nyc.gov/district-9/'},
    { name: 'Carmen De La Rosa', website: 'https://council.nyc.gov/district-10/'},
    { name: 'Eric Dinowitz', website: 'https://council.nyc.gov/district-11/'},
    { name: 'Kevin C. Riley', website: 'https://council.nyc.gov/district-12/'},
    { name: 'Kristy Marmorato', website: 'https://council.nyc.gov/district-13/'},
    { name: 'Pierina Ana Sanchez', website: 'https://council.nyc.gov/district-14/'},
    { name: 'Oswald Feliz', website: 'https://council.nyc.gov/district-15/'},
    { name: 'Althea Stevens', website: 'https://council.nyc.gov/district-16/'},
    { name: 'Rafael Salamanca Jr.', website: 'https://council.nyc.gov/district-17/'},
    { name: 'Amanda Farías', website: 'https://council.nyc.gov/district-18/'},
    { name: 'Vickie Paladino', website: 'https://council.nyc.gov/district-19/'},
    { name: 'Sandra Ung', website: 'https://council.nyc.gov/district-20/'},
    { name: 'Francisco Moya', website: 'https://council.nyc.gov/district-21/'},
    { name: 'Tiffany Cabán', website: 'https://council.nyc.gov/district-22/'},
    { name: 'Linda Lee', website: 'https://council.nyc.gov/district-23/'},
    { name: 'James F. Gennaro', website: 'https://council.nyc.gov/district-24/'},
    { name: 'Shekar Krishnan', website: 'https://council.nyc.gov/district-25/'},
    { name: 'Julie Won', website: 'https://council.nyc.gov/district-26/'},
    { name: 'Dr. Nantasha Williams', website: 'https://council.nyc.gov/district-27/'},
    { name: 'Adrienne E. Adams', website: 'https://council.nyc.gov/district-28/'},
    { name: 'Lynn Schulman', website: 'https://council.nyc.gov/district-29/'},
    { name: 'Robert F. Holden', website: 'https://council.nyc.gov/district-30/'},
    { name: 'Selvena N. Brooks-Powers', website: 'https://council.nyc.gov/district-31/'},
    { name: 'Joann Ariola', website: 'https://council.nyc.gov/district-32/'},
    { name: 'Lincoln Restler', website: 'https://council.nyc.gov/district-33/'},
    { name: 'Jennifer Gutiérrez', website: 'https://council.nyc.gov/district-34/'},
    { name: 'Crystal Hudson', website: 'https://council.nyc.gov/district-35/'},
    { name: 'Chi Ossé', website: 'https://council.nyc.gov/district-36/'},
    { name: 'Sandy Nurse', website: 'https://council.nyc.gov/district-37/'},
    { name: 'Alexa Avilés', website: 'https://council.nyc.gov/district-38/'},
    { name: 'Shahana Hanif', website: 'https://council.nyc.gov/district-39/'},
    { name: 'Rita Joseph', website: 'https://council.nyc.gov/district-40/'},
    { name: 'Darlene Mealy', website: 'https://council.nyc.gov/district-41/'},
    { name: 'Chris Banks', website: 'https://council.nyc.gov/district-42/'},
    { name: 'Susan Zhuang', website: 'https://council.nyc.gov/district-43/'},
    { name: 'Simcha Felder', website: 'https://council.nyc.gov/district-44/'},
    { name: 'Farah N. Louis', website: 'https://council.nyc.gov/district-45/'},
    { name: 'Mercedes Narcisse', website: 'https://council.nyc.gov/district-46/'},
    { name: 'Justin Brannan', website: 'https://council.nyc.gov/district-47/'},
    { name: 'Inna Vernikov', website: 'https://council.nyc.gov/district-48/'},
    { name: 'Kamillah Hanks', website: 'https://council.nyc.gov/district-49/'},
    { name: 'David Carr', website: 'https://council.nyc.gov/district-50/'},
    { name: 'Frank Morano', website: 'https://council.nyc.gov/district-51/'},
  ]
  @errors = {}

  LegislativeAddress.where('lower(state) = ? or lower(state) = ?', 'ny', 'new york').find_each(batch_size: 100) do |legislative_address|
    # Check if the legislative_details is nil
    if legislative_address.legislation_details.nil?
      @errors[legislative_address.id] = "Legislative details is nil for this address"
      next
    end
    # Check if the council_member_details is nil
    if legislative_address.legislation_details['council_member_details'].nil?
      @errors[legislative_address.id] = "Council member details is nil for this address"
      next
    end
    council_member_details = legislative_address.legislation_details['council_member_details']
    council_district = council_member_details['district']
    next if council_district.blank?
    district_number = council_district.gsub(/[^0-9,.]/, "").to_i
    if district_number < 1 || district_number > 51
      @errors[legislative_address.id] = "Invalid district number: #{district_number}"
      next
    end
    district_number -= 1
    next if council_member_details['name']&.downcase == council_member_data[district_number][:name].downcase
    legislative_address.legislation_details['council_member_details']['name'] = council_member_data[district_number][:name]
    legislative_address.legislation_details['council_member_details']['website'] = council_member_data[district_number][:website]
    legislative_address.save!

  rescue => e
      @errors[legislative_address.id] = e.message
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_errors.csv", 'w') do |csv|
    csv << ['Legislative Detail', 'Errors']
    @errors.each do |error|
      csv << error
    end
  end
end

# bundle exec rake 'add_discarded_at_to_user_audit[btoba]'
# bundle exec rake 'update_council_district_members[btoba]'
