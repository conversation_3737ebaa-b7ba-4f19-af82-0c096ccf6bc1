# frozen_string_literal: true

require 'csv'

desc 'import data'
task :cobanc_update_emp_details, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  csv_file.each do |row|
    @first_name = row['First Name']
    @last_name = row['Last Name']
    @mi = row['MI'] || ''
    epd = row['Employment Status Start Date']
    employment_status = EmploymentStatus.where(name: 'Retired').first
    @row_number = @first_name + @last_name

    employment_start_date = parse_employment_start_date(epd, 'Employment Start Date')

    p "#{epd} : #{employment_start_date}"

    employees = Employee.where('first_name ilike ? and last_name ilike ? and middle_name ilike ?', @first_name, @last_name, @mi)

    if employees.present?
      if employees.count > 1
        feed_errors('Multiple occurrences for name')
      else

        employee = employees.first
        employee_employment = employee.employee_employment_statuses.where(employment_status_id: employment_status.id)

        next unless  employee_employment.present?

        employee_employment.update_all(start_date: employment_start_date)

      end
    else
      feed_errors("Employee doesn't exist")
    end

  rescue => e
    p @first_name, @last_name, @mi, e.message
    feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_members_details_import_errors.csv", 'w') do |csv|
    csv << ["First Name", "Last Name", "Middle Name", "Error"]

    @errors.each do |error|
      csv << error
    end
  end

end


def parse_employment_start_date(date, type)
  return nil unless date.present?

  parsed_date = nil

  begin
    date_array = date.split('/')
    month = date_array[0].to_i
    day = date_array[1].to_i
    year = date_array[2].to_i

    if year.to_s.length == 2
      if year > 20 && year <= 99
        year = ('19' + year.to_s).to_i
      else
        year = ('20' + year.to_s).to_i
      end
    elsif year.to_s.length == 1
      year = ('200' + year.to_s).to_i
    elsif year.to_s.length > 4
      raise 'Invalid Year'
    else
      year = year.to_i
    end

    parsed_date = Date.new(year, month, day)
  rescue
    parsed_date = Date.parse(date)
  end

  parsed_date

rescue => e
  feed_errors('DATE - ' + type + " #{date} " + e.message)
end

def feed_errors(message)
  @errors[@first_name] = [@last_name, @mi, message]
end
