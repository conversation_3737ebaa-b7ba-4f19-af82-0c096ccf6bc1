# frozen_string_literal: true

desc 'Add lodi denied reasons'
task :add_lodi_denied_reasons, [:account] =>[:environment] do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  DeniedReason.create(name: "The incident did not occur while you were in the performance of duties as a Correction Officer")
  DeniedReason.create(name: "Insufficient evidence that you sustained an injury.")
  DeniedReason.create(name: "Insufficient evidence that the injury you claim was sustained in the performance of your duties")
  DeniedReason.create(name: "Insufficient evidence of a causal connection between the injury/injuries claimed and Incident described.")
  DeniedReason.create(name: "Insufficient documentation. The following was missing and/or incomplete.")
  DeniedReason.create(insufficient_reason: "Injury to Officer")
  DeniedReason.create(insufficient_reason: "Witness Reports")
  DeniedReason.create(insufficient_reason: "Detailed Medical Report")
  DeniedReason.create(insufficient_reason: "Employee 851 Reports")
  DeniedReason.create(insufficient_reason: "Tour Commander/Supervisor Report")
  DeniedReason.create(name: "Insufficient evidence to establish that you sustained a recurrence or aggravation of prior injury initially sustained on")
  DeniedReason.create(name: "No loss Time/Disability, Evaluation/Treatment Only")

end