# frozen_string_literal: true

require 'csv'

desc 'import data'
task :nassau_coba_update_employee_id, [:file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!('nassaucoba')

  file_name = args[:file_path].split('.').first
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  @current_account = Account.find_by(subdomain: Apartment::Tenant.current)

  # NAME,MM,DD,YY,SOCIAL SECURITY,Employee ID

  csv_file.each do |row|

    name = row['NAME']
    month = row['MM']
    date = row['DD']
    year = row['YY']
    ssn = row['SOCIAL SECURITY']
    birthday = parse_birth_day(date, month, year)

    @row_number = ssn

    employees = Employee.where(birthday: birthday)
    employee = nil
    employee_id = ''

    if employees.present?
      if employees.count > 1
        feed_errors('MULTIPLE MEMBER DETAILS')
      else
        employee = employees.first
      end
    else
      feed_errors('MEMBER NOT FOUND')
    end

    employee_id = employee.payroll_id if employee.present?

    CSV.open("#{Rails.root}/#{file_name}_updated1.csv", 'a+') do |csv|
      csv << [employee_id]
    end

  rescue => e
    p @row_number, e.message
    feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/nassaucoba_#{Date.today}_#{file_name}_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end

def find_member(members, ssn)
  if members.present?
    if members.count > 1
      employees = members.where(social_security_number: ssn.last(4))
      return find_member(employees, ssn)
    else
      return members.first
    end
  else
    feed_errors('MEMBER NOT FOUND')
  end
end

def parse_birth_day(date, month, year)
  return nil unless date.present?

  date = date.to_i
  month = month.to_i
  year = year.to_i

  parsed_date = nil

  if year.to_s.length == 2
    if year > 20 && year <= 99
      year = ('19' + year.to_s).to_i
    else
      year = ('20' + year.to_s).to_i
    end
  elsif year.to_s.length == 1
    year = ('200' + year.to_s).to_i
  elsif year.to_s.length > 4
    raise 'Invalid Year'
  else
    year = year.to_i
  end

  return Date.new(year, month, date)

rescue => e
  feed_errors("DATE - #{date} " + e.message)
end

def feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = message
  end
end
