# frozen_string_literal: true

desc 'Nysscoa Send Employees Credentials'
task :nysscoa_send_employees_credentials, [ :account, :file_path ] => [ :environment ] do |_t, args|

  @errors = {}
  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  csv_file.each do |row|
    @row_number = row['Member Name'] || ''
    first_name, last_name = @row_number.split(' ')
    email = row['Email'] || ''
    contact_email = Contact.kept.where(contact_type: 'email', value: email)
    employee = contact_email&.first&.employee&.full_name&.downcase&.include?(first_name.downcase) && contact_email&.first&.employee

    if contact_email.count == 1 && employee.present?
      unless (mail_id = contact_email.first.value).present?
        @errors[@row_number] = "Employee work email id is missing"
      end
      username = employee.username || mail_id
      password = Devise.friendly_token(8)

      if mail_id.present? && username.present? && password.present?
        employee.assign_attributes(username: username, password: password, password_confirmation: password)
        if employee.save(validate: false)
          member_credential_mail(mail_id, username, password, args[:account])
        else
          @errors[@row_number] = employee.errors.full_messages
        end
      end
    else
      if contact_email.count > 1
        @errors[@row_number] = "More than one email is found"
        next
      elsif contact_email.blank?
        @errors[@row_number] = "Email is not found"
        next
      elsif employee.blank?
        @errors[@row_number] = "Employee is not found"
        next
      else
        @errors[@row_number] = "No errors found"
      end
    end
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_send_employees_credentials_import_errors.csv", 'w') do |csv|
    csv << [ "Row Number", "Errors" ]

    @errors.each do |error|
      csv << error
    end
  end
end

def work_mail(employee)
  return nil unless employee.present? && employee.contacts.present?

  work_email = employee.contacts.where(contact_type: "email", contact_for: "work").first

  return work_email.present? && work_email.value.present? ? work_email.value : nil
end

def member_credential_mail(mail_address, username, password, account)
  if mail_address.present? && username.present? && password.present?
    MemberCredentialsMailer.details(username, password, mail_address, account).deliver_later
  end
end
