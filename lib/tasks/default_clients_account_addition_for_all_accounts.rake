# frozen_string_literal: true

desc 'default client account addition as a member for all accounts'
task default_client_account_addition_for_all_accounts: :environment do
  skip_or_set_all_callbacks('skip_callback')
  accounts = Account.pluck(:subdomain)
  @default_client_email = '<EMAIL>'
  @default_client_phone = '(718) 473 - 2135'

  accounts.each do |subdomain|
    Apartment::Tenant.switch!(subdomain)
    email_ids = Contact.kept.where(value: @default_client_email).pluck(:employee_id)
    phone_ids = Contact.kept.where(value: @default_client_phone).pluck(:employee_id)
    employee_ids = (email_ids + phone_ids).uniq.compact
    Employee.kept.includes(:contacts).where(id: employee_ids).each do |employee|
      if (employee.full_name.downcase.split & %w[elyse eversman amar]).present?
        contacts_create_or_delete(employee, 'email', 'create')
        contacts_create_or_delete(employee, 'phone', 'create')
        next
        ## Commenting this now, because there is deleting logics is not needed now. Please uncomment if its needed.
        # else
        #   contacts_create_or_delete(employee, 'email', 'delete')
        #   contacts_create_or_delete(employee, 'phone', 'delete')
      end
    end
    create_member_with_client_name if employee_ids.blank?
  end

  skip_or_set_all_callbacks('set_callback')
end

def contacts_create_or_delete(employee, email_or_phone, create_or_delete)
  updating_value = email_or_phone == 'email' ? @default_client_email : @default_client_phone
  if create_or_delete == 'create'
    return if employee.contacts.where(value: updating_value).present?

    contact = Contact.where(employee_id: employee.id, contact_for: 'personal', contact_type: email_or_phone).first_or_create
    contact.update_columns(value: updating_value)
  else
    contact = Contact.where(employee_id: employee.id, contact_type: email_or_phone, value: updating_value)
    contact.update_all(value: '') if contact.present?
  end
end

def create_member_with_client_name
  contacts_hash = [
    { contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE, value: '' },
    { contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE, value: '' },
    { contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL, value: '' },
    { contact_for: Contact::ContactFor::COLLEAGUE, contact_type: Contact::ContactType::EMERGENCY, value: '' },
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMERGENCY, value: '' },
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE, value: @default_client_phone },
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL, value: @default_client_email }
  ]

  employee = Employee.new(first_name: 'Elyse', last_name: 'Everseman Amar')
  employee.save!(validate: false)
  employee.contacts.import contacts_hash
end

def skip_or_set_all_callbacks(name)
  Employee.send(name, :validation, :before, :payroll_id_autoload)
  Employee.send(name, :save, :before, :a_number_autoload)
  Employee.send(name, :save, :before, :update_issued_at_time)
  Employee.send(name, :save, :before, :update_prescription)
  Employee.send(name, :save, :before, :update_address_to_mailing_address)
  Employee.send(name, :save, :after, :create_analytic_config)
  Employee.send(name, :save, :after, :set_expiration_for_mobile_access_token)
  Employee.send(name, :save, :after, :update_benefit_address)
  Employee.send(name, :commit, :after, :update_legislative_detail)
  Employee.send(name, :save, :after, :update_coverage_marital_status)
end
