# frozen_string_literal: true

desc 'Create user permission roles'
task :create_roles, [:tenant] => [:environment] do |_t, args|
  Apartment::Tenant.switch!(args[:tenant])
  send(args[:tenant] + '_roles')
  Apartment::Tenant.reset
end

def nysscoa_roles
  # General user don't have access to user module alone
  Role.create_role_with_rights('General User',
                               Right.all.pluck(:name) - %w[read_user write_user])

  Role.create_role_with_rights('View Profile Upload Benefit User', %w[read_employee read_employee_upload read_employee_benefit])
end

def sssa_roles
  Role.create_role_with_rights('Custom 1',
                               %w[read_employee write_employee_paf write_employee_analytics write_employee_award
                                  write_employee_discipline_setting write_employee_grievance write_employee_upload
                                  report_sick_bank report_disciplines report_grievances read_user_profile])

  Role.create_role_with_rights('Custom 2',
                               %w[read_employee write_employee_paf write_employee_award read_employee_analytics
                                  write_employee_discipline_setting write_employee_grievance write_employee_upload
                                  report_single_employee report_disciplines report_grievances read_user_profile
                                  read_notification write_notification])

  Role.create_role_with_rights('Custom 3',
                               %w[read_employee read_employee_paf write_employee_award write_employee_discipline_setting
                                  write_employee_grievance write_employee_upload read_user_profile read_user_employee_analytics])

  Role.create_role_with_rights('Custom 4',
                               %w[write_employee write_employee_paf write_employee_award write_employee_discipline_setting
                                  write_employee_grievance write_employee_position write_employee_office write_employee_upload
                                  write_gender write_grievance write_marital_status write_office write_paf
                                  write_payment_type write_position write_section write_title write_unit
                                  write_department write_discipline_setting write_employment_status report_single_employee
                                  report_disciplines report_grievances report_janus read_user_profile read_user_employee_analytics])

  Role.create_role_with_rights('Custom 5', %w[read_employee read_user_profile read_user_employee_analytics])

  Role.create_role_with_rights('Custom 6',
                               %w[read_employee write_employee_discipline_setting write_employee_grievance
                                  write_employee_upload report_grievances report_disciplines read_user_profile read_user_employee_analytics])

  Role.create_role_with_rights('Custom 7', %w[read_employee read_user_profile
                                  write_employee_discipline_setting write_employee_grievance write_discipline_setting
                                  write_grievance write_discipline_charge read_user_employee_analytics])

  Role.create_role_with_rights('Custom 8',
                               %w[write_employee write_employee_paf write_employee_award write_employee_discipline_setting
                                  write_employee_grievance write_employee_position write_employee_office write_employee_upload
                                  write_gender write_grievance write_marital_status write_office write_paf
                                  write_payment_type write_position write_section write_title write_unit
                                  write_department write_discipline_setting write_employment_status report_single_employee
                                  report_disciplines report_grievances report_janus read_user_profile
                                  read_notification write_notification read_user_employee_analytics])

  Role.create_role_with_rights('Custom 9',
                               %w[read_employee write_employee_paf write_employee_analytics write_employee_award
                                  write_employee_discipline_setting write_employee_grievance write_employee_upload
                                  report_sick_bank report_disciplines report_grievances read_user_profile])

  Role.create_role_with_rights('Custom 10',
                               %w[read_employee write_employee_paf write_employee_award read_employee_analytics
                                  write_employee_discipline_setting write_employee_grievance write_employee_upload
                                  report_single_employee report_disciplines report_grievances read_user_profile
                                  read_notification write_notification])

  Role.create_role_with_rights('Custom 11',
                               %w[read_employee write_employee_paf write_employee_award read_employee_analytics
                                  write_employee_discipline_setting write_employee_grievance write_employee_upload
                                  report_single_employee report_disciplines report_grievances read_user_profile
                                  read_notification write_notification write_analytics_configuration])

end

def nassaucoba_roles
  # Members
  Role.create_role_with_rights('General User',
                               %w[write_employee write_employee_firearm_status write_employee_meeting_type
                                       write_employee_upload read_employee_analytics read_employee_benefit read_life_insurance
                                       read_employee_discipline_setting read_employee_grievance write_notification
                                       report_benefits report_disciplines report_grievances report_janus report_lodi
                                       report_single_employee report_union_meetings])

  # Disciplines and Grievances
  Role.create_role_with_rights('Custom 1',
                               %w[write_employee write_employee_firearm_status write_employee_meeting_type
                                       write_employee_upload write_employee_discipline_setting write_employee_grievance
                                       read_employee_analytics read_employee_benefit read_life_insurance write_notification
                                       report_benefits report_disciplines report_grievances report_janus report_lodi
                                       report_single_employee report_union_meetings])

  # LODI
  Role.create_role_with_rights('Custom 2',
                               %w[write_employee write_employee_firearm_status write_employee_meeting_type
                                       write_employee_upload write_employee_analytics read_employee_benefit read_life_insurance
                                       read_employee_discipline_setting read_employee_grievance write_notification
                                       report_benefits report_disciplines report_grievances report_janus report_lodi
                                       report_single_employee report_union_meetings])

  # Benefits, Settings and Users
  Role.create_role_with_rights('Custom 3',
                               %w[write_employee write_employee_firearm_status write_employee_meeting_type
                                       write_employee_upload write_employee_benefit write_life_insurance
                                       read_employee_analytics read_employee_discipline_setting read_employee_grievance
                                       write_notification report_benefits report_disciplines report_grievances report_janus
                                       report_lodi report_single_employee report_union_meetings write_user write_user_profile
                                       write_rank write_position write_payment_type write_office write_meeting_type
                                       write_marital_status write_grievance_status write_grievance_charge write_grievance
                                       write_gender write_firearm_status write_employment_status write_discipline_status
                                       write_discipline_setting write_discipline_charge write_benefit])

  Role.create_role_with_rights('Custom 4',
                                   %w[read_employee read_employee_firearm_status read_employee_analytics read_employee_benefit
                                            read_employee_discipline_setting read_employee_grievance read_employee_meeting_type
                                            read_employee_upload report_single_employee report_disciplines report_grievances
                                            report_benefits report_lodi report_union_meetings report_janus read_life_insurance])
end

def cobanc_roles
  Role.create_role_with_rights('General User',
                               %w[write_notification write_employee report_single_employee])
  Role.create_role_with_rights('Member & Reports Custom 1', %w[write_employee report_single_employee write_employee_benefit report_benefits ])
  Role.create_role_with_rights('Member & Reports Custom 2', %w[write_employee report_single_employee read_employee_benefit report_benefits ])
end

def sccea_roles
  # Except Users
  Role.create_role_with_rights('Account Admin',
                               Right.all.pluck(:name) - %w[read_user write_user])

  # General User
  Role.create_role_with_rights('General User',
                               %w[write_user_profile write_employee write_employee_award write_employee_benefit
                               write_employee_firearm_status write_employee_benefit write_employee_discipline_setting
                               write_employee_grievance write_employee_meeting_type write_employee_upload write_employee_analytics
                               report_single_employee report_benefits report_sick_bank report_lodi report_disciplines
                               report_grievances report_employee_delegate_assignment report_union_meetings report_janus
                               ])
end

def nats_roles
  # For Account Admins
  Role.create_role_with_rights('Account Admin',
                               Right.all.pluck(:name) - %w[write_super_user read_super_user read_user write_user])
end

def suffolkame_roles
  Role.create_role_with_rights('General User',
                               %w[write_notification write_employee read_user_profile read_employee
                                        read_employee_paf read_employee_benefit read_employee_analytics read_employee_award
                                        read_employee_discipline_setting read_employee_grievance read_employee_upload
                                        read_employee_meeting_type read_report_single_employee read_report_benefits
                                        read_report_sick_bank read_report_lodi read_report_disciplines
                                        read_report_grievances read_report_janus])
end

def sccoba_roles
  # Except Users
  Role.create_role_with_rights('Account Admin',
                               Right.all.pluck(:name) - %w[read_user write_user])

  # Only Reports, Notifications and Member List
  Role.create_role_with_rights('General User',
                               Right.all.pluck(:name) - Right.where(rights_type: %w[user office rank employment_status marital_status position gender affiliation meeting_type user platoon paf]).pluck(:name))


  # Only Member List and Reports
  Role.create_role_with_rights('Custom',
                               %w[write_user_profile write_employee write_employee_award write_employee_benefit
                               write_employee_firearm_status write_employee_benefit write_employee_discipline_setting
                               write_employee_grievance write_employee_meeting_type write_employee_upload write_employee_analytics
                               report_single_employee report_benefits report_sick_bank report_lodi report_disciplines
                               report_grievances report_employee_delegate_assignment report_union_meetings report_janus
                               ])
end

def btoba_roles
  # Except Users
  Role.create_role_with_rights('Account Admin',
                               Right.all.pluck(:name) - %w[read_user write_user])

  # General User
  Role.create_role_with_rights('General User',
                               %w[write_user_profile write_employee write_employee_award read_employee_benefit
                               write_employee_firearm_status write_employee_discipline_setting
                               write_employee_grievance write_employee_meeting_type write_employee_upload write_employee_analytics
                               report_single_employee report_benefits report_sick_bank report_lodi report_disciplines
                               report_grievances report_employee_delegate_assignment report_union_meetings report_janus
                               write_notification])
end

def nyscoa_roles
  # Except Users
  Role.create_role_with_rights('Account Admin',
                               Right.all.pluck(:name) - %w[read_user write_user])

  # General User
  Role.create_role_with_rights('General User',
                               Right.all.pluck(:name) - %w[read_user write_user read_notification write_notification])

  # View Only User
  Role.create_role_with_rights('View Only User',
                               Right.all.pluck(:name) - %w[read_user write_user read_notification write_notification] - Right.where("name like 'write%'").pluck(:name))

  # View Only + Write Notification + Read Change Requests

  Role.create_role_with_rights('View Only Change Request and Notification User',
                               Right.all.pluck(:name) - %w[read_user write_user] - Right.where("name like 'write%'").pluck(:name) + %w[read_change_request] + Right::NOTIFICATION_RIGHTS )


  # View Profile and Benefits alone
  Role.create_role_with_rights('Member Profile', %w[write_user_profile read_employee read_employee_benefit])

  Role.create_role_with_rights('Beneficiary Form User',
                               Right.all.pluck(:name) - %w[read_user write_user read_notification write_notification] - Right.where("name like 'write%'").pluck(:name) + %w[write_benefit_disbursement])
end

def ncpddai_roles
  # Except Users
  Role.create_role_with_rights('Account Admin',
                               Right.all.pluck(:name) - %w[read_user write_user])

  # Edit permissions for Only Discipline & Grievances
  Role.create_role_with_rights('User - Only Discipline & Grievance',
                               Right.all.pluck(:name) - %w[read_user write_user] - Right::SETTINGS_RIGHTS - Right::EMPLOYEE_WRITE_RIGHTS + %w[write_employee write_employee_discipline_setting write_employee_grievance write_employee_upload])

  # Edit permissions for Only Dues
  Role.create_role_with_rights('User - Only Dues',
                               Right.all.pluck(:name) - %w[read_user write_user] - Right::SETTINGS_RIGHTS - Right::EMPLOYEE_WRITE_RIGHTS + %w[write_employee write_employee_paf write_employee_upload])

  # View Only User
  Role.create_role_with_rights('User - Member',
                               Right.all.pluck(:name) - %w[read_user write_user] - Right::SETTINGS_RIGHTS - Right.where("name like 'write%'").pluck(:name) + %w[write_employee write_employee_upload write_notification])

  # View Only User
  Role.create_role_with_rights('Read Only User',
                               Right.all.pluck(:name) - %w[read_user write_user] - Right::SETTINGS_RIGHTS - Right.where("name like 'write%'").pluck(:name) - Right.where("name like '%report%'").pluck(:name) - Right.where("name like '%notification%'").pluck(:name))


  Role.create_role_with_rights('User - Only Member & Notification',
                               Right.all.pluck(:name) - %w[read_user write_user write_notification] - Right::SETTINGS_RIGHTS - Right::REPORT_RIGHTS - Right.where("name like 'write%'").pluck(:name))
end

def nyshbca_roles
  # Except Users
  Role.create_role_with_rights('Account Admin',
                               Right.all.pluck(:name) - %w[read_user write_user])
end

def ufadba_roles
  # Account Admin
  Role.create_role_with_rights('Account Admin',
                               Right.all.pluck(:name) - %w[read_user write_user])

  # General Users
  Role.create_role_with_rights('General User',
                               Right.all.pluck(:name) - %w[read_user write_user write_notification]- Right::REPORT_RIGHTS - Right::SETTINGS_RIGHTS)
end

def papba_roles
  # View only User
  Role.create_role_with_rights('View Only User', Right.all.pluck(:name) + %w[read_reminder_user read_reminder write_reminder] - %w[read_user write_user read_notification write_notification read_grievance write_grievance read_employee_grievance write_employee_grievance] - Right.where("name like 'write%'").pluck(:name) - Right::SETTINGS_RIGHTS)
  # View and edit for all without User and Settings
  Role.create_role_with_rights('Custom1 (No Access to User and Settings)', Right.all.pluck(:name) + %w[read_reminder_user read_reminder write_reminder] - %w[read_user write_user read_beneficiary write_beneficiary read_grievance write_grievance read_employee_grievance write_employee_grievance] - Right::SETTINGS_RIGHTS)

  Role.create_role_with_rights('Custom2', %w[read_contact_person write_employee_firearm_status read_reminder_user read_reminder write_reminder] + Right::EMPLOYEE_READ_RIGHTS + Right::NOTIFICATION_RIGHTS + Right::REPORT_RIGHTS - %w[ read_beneficiary write_beneficiary read_grievance write_grievance read_employee_grievance write_employee_grievance])

  Role.create_role_with_rights('Custom3', %w[read_reminder_user read_reminder write_reminder] + Right::CONTACT_PERSON_READ_RIGHTS + Right::EMPLOYEE_READ_RIGHTS - %w[ read_beneficiary write_beneficiary read_grievance write_grievance read_employee_grievance write_employee_grievance])
  Role.create_role_with_rights('Custom3 With Grievance', %w[read_reminder_user read_reminder write_reminder read_grievance write_grievance read_employee_grievance write_employee_grievance] + Right::CONTACT_PERSON_READ_RIGHTS + Right::EMPLOYEE_READ_RIGHTS - %w[ read_beneficiary write_beneficiary]  )
end

def sccoa_roles
  Role.create_role_with_rights('Member List User', %w[read_employee read_employee_firearm_status read_employee_benefit read_employee_discipline_setting read_employee_grievance read_employee_meeting_type read_employee_upload])
end

def local2507_roles
  Role.create_role_with_rights('Custom User 1', %w[write_employee read_employee_analytics read_employee_discipline_setting read_employee_grievance read_employee_meeting_type read_employee_upload read_legislative_detail read_employee_paf read_employee_award])
end


def nyscca_roles
  # View only User
  Role.create_role_with_rights('User 1', %w[write_employee write_employee_analytics write_employee_firearm_status write_employee_meeting_type
                                                  write_employee_upload write_contact_person read_legislative_detail
                                                  read_report report_disciplines read_report_disciplines report_grievances read_report_grievances
                                                  report_benefit_coverages read_report_benefit_coverages report_union_meetings read_report_single_employee
                                                  read_report_union_meetings report_single_employee write_notification write_employee_benefit])
  # View and edit for all without User and Settings

  Role.create_role_with_rights('User 2', %w[write_employee write_employee_analytics write_employee_firearm_status write_employee_meeting_type
                                                  write_employee_upload write_contact_person write_notification write_employee_benefit read_report report_disciplines read_report_disciplines report_grievances read_report_grievances
                                                  report_benefit_coverages read_report_benefit_coverages report_union_meetings read_report_single_employee
                                                  read_report_union_meetings report_single_employee])

  Role.create_role_with_rights('User 3', %w[write_employee write_employee_analytics write_employee_firearm_status write_employee_meeting_type
                                                  write_employee_upload write_employee_grievance write_notification write_employee_discipline_setting read_report report_disciplines read_report_disciplines report_grievances read_report_grievances
                                                  report_benefit_coverages read_report_benefit_coverages report_union_meetings read_report_single_employee
                                                  read_report_union_meetings report_single_employee write_employee_benefit])

  Role.create_role_with_rights('User 4', %w[write_employee write_employee_firearm_status write_employee_meeting_type write_employee_upload
                                                          write_employee_analytics write_notification report_single_employee read_report_single_employee
                                                          report_disciplines read_report_disciplines report_grievances read_report_grievances report_benefit_coverages
                                                          read_report_benefit_coverages report_union_meetings read_report_union_meetings write_employee_benefit])
  Role.create_role_with_rights('User 5', %w[write_employee write_employee_benefit])
end

def nyccoba_roles
  # Except Users
  Role.create_role_with_rights('Account Admin',
                               Right.all.pluck(:name) - %w[read_user write_user] - Right::SETTINGS_RIGHTS)
  # Tier 2 Users
  Role.create_role_with_rights('Tier 2 User',
                               Right.all.pluck(:name) - %w[read_user write_user read_user_audit] - Right::NOTIFICATION_RIGHTS - Right::SETTINGS_RIGHTS)

  # View Only User
  Role.create_role_with_rights('View Only User',
                               %w[read_employee read_employee_analytics read_employee_grievance read_employee_discipline_setting
                                            read_pesh read_employee_meeting_type read_employee_award read_legislative_detail read_employee_benefit
                                             read_employee_paf read_employee_firearm_status read_note])

  Role.create_role_with_rights('Notifications User', %w[read_notification write_notification])

  Role.create_role_with_rights('Annuity Access User', %w[read_employee write_employee_paf])

  Role.create_role_with_rights('Member Profile User', %w[write_employee])

  Role.create_role_with_rights('Placard User', %w[read_employee write_maillog write_placard_only], 'true')

  ## We are using write_employee_employment_status_in_employee_benefit right to handle the employment_status in the employee_benefit for the BenefitFundUser.
  ## Since client requires EditAccess only for the EmploymentStatus in the MemberProfile. We are handling like this. And This right is only used for this account.

  Role.create_role_with_rights('Benefit Fund User', %w[write_employee write_note write_employee_benefit write_maillog])

  Role.create_role_with_rights('Benefit Fund User II', %w[write_employee write_note write_employee_benefit report_single_employee read_report_single_employee report_benefit_coverages read_report_benefit_coverages])

  Role.create_role_with_rights('Farmer', %w[write_employee write_note write_employee_benefit write_maillog write_employee_paf])

  Role.create_role_with_rights('Goodman', %w[read_employee write_maillog write_employee_grievance write_pesh read_pesh write_employee_discipline_setting])

  Role.create_role_with_rights('Quan', %w[read_employee write_maillog write_employee_analytics read_legislative_detail write_placard_only], 'true')

  Role.create_role_with_rights('Benefits View Only', %w[write_employee read_employee_benefit write_maillog write_placard_only], 'true')
end

def scpc_roles
  Role.create_role_with_rights('Account Admin',
                               Right.all.pluck(:name) - %w[read_user write_user])

  Role.create_role_with_rights('View Employee Only',%w[read_employee])
end
