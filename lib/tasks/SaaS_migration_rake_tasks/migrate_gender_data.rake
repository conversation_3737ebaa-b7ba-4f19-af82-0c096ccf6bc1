# frozen_string_literal: true

desc 'Migrate Gender to employee'
task :migrate_gender_data, [:tenant] => [:environment] do |_t, args|
  Apartment::Tenant.switch!(args[:tenant])
  gender = [{ name: 'Female' }, { name: 'Male' }]
  gender_list = Gender.create(gender)

  employee_ids = []
  Employee.kept.find_in_batches do |employees|
    employees.each do |employee|
      gender_name = [employee.read_attribute('gender')] & gender_list.pluck(:name).map(&:downcase)
      if gender_name.present?
        employee.update_columns(gender_id: gender_list.select { |name| name['name'].downcase == gender_name.first }.first.id)
      else
        employee_ids << employee.id
      end
    end
  end
  p "Failed to update: #{employee_ids}"
  Apartment::Tenant.reset
end
