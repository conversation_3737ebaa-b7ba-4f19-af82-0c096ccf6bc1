# frozen_string_literal: true

desc 'update role for user - Access role to User permissions migration'
task nysscoa_access_role_to_user_permissions_migration: [:environment] do
  Apartment::Tenant.switch!('nysscoa')

  admin_role_id = Role.find_by(name: 'Admin').id
  general_user_role_id = Role.find_by(name: 'General User').id
  User.where(access_role: 'Administrator').update_all(role_id: admin_role_id)
  User.where(access_role: 'General User').update_all(role_id: general_user_role_id)

  Apartment::Tenant.reset
end
