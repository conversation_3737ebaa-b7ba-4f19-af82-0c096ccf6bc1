# frozen_string_literal: true

desc 'Update Existing Notification'
task :update_existing_notification, [:accounts] => [:environment] do |_t, args|

  subdomains = args[:accounts]&.split('|') || []
  subdomains.each do |subdomain|
    Apartment::Tenant.switch!(subdomain)
    @errors = {}

    Notification.kept.where(is_scheduled: false, status: 0).each do |notification|
      @row_name = "#{subdomain} - Notification Id: #{notification.id}"

      next if notification.status == 'completed'

      begin
        ActiveRecord::Base.transaction do
          notification.update_columns(status: 'completed')
        end
      rescue StandardError => e
        member_feed_errors(e.message)
      end
    end
  end

  CSV.open("#{Rails.root}/update_existing_notifications_#{Time.new.strftime('%d-%m-%Y-%H:%M')}.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']

    @errors.each do |error|
      csv << error
    end
  end
end

def member_feed_errors(message)
  if @errors[@row_name].present?
    @errors[@row_name] << message
  else
    @errors[@row_name] = [message]
  end
end

# bundle exec rake 'update_existing_notification[papba|nyccoba|nysscoa|nyscoa|btoba|local2507]'
