desc 'Updated the user profile id with user id for staff members '
task :updated_user_profile_with_user_id => :environment do |_t|
  Apartment::Tenant.switch!("sssa")
  @errors = {}
  employees = Employee.kept.where(staff_member: true)
  users = User.kept
  employees.each do |employee|
    full_name = "#{employee.first_name} #{employee.last_name}"
    @row_number = full_name
    email = employee.contacts.where(contact_type: 'email', contact_for: 'work').first&.value || employee.contacts.where(contact_type: 'email', contact_for: 'personal').first&.value
    user = users.find { |u| u.email.downcase == email.downcase && u.name.downcase == full_name.downcase }
    employee.update(user_profile_id: user.id) if user.present?

  rescue => e
    sssa_feed_errors(e.message)
    next
  end
  CSV.open("#{Rails.root}/updated_user_profile_with_user_id.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]
    @errors.each do |error|
      csv << error
    end
  end
end

def sssa_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end