# frozen_string_literal: true

require 'csv'

desc 'import data'
task :import_member_details, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  csv_file.each do |row|
    @row_number = row['Social Security Number']

    # Default values
    section = nil
    title = nil

    gender = check_gender(row['Sex'])
    unit = check_unit(row['Unit'])
    marital_status = check_marital_status(row['Marital Status'])
    benefit_type = check_benefit_type(row['Benefits Type'])
    department = check_department(row['Department'])
    section = check_section(row['Section'], department.id) if department
    title = check_title(row['Title'], department.id, section.id) if section
    rank = check_rank(row['Title'])
    employment_status = check_employment_status(row['Status'])
    dob = parse_string_date(row['Birth Date'], 'DOB')
    ssn = parse_ssn(row['Social Security Number'])
    start_date = parse_date(row['Member Start Date'], 'Member Start Date')
    longevity_date = parse_string_date(row['Lgvty'], 'Longevity Date')
    leave_progression_date = parse_string_date(row['Leave Progression Date'], 'Leave Progression Date')
    ncc_date = parse_string_date(row['NCC Date'], 'NCC Date')
    work_phone = parse_phone(row['Phone Number'])
    cell_phone = parse_phone(row['Cell Phone'])
    zip_code = row['Zip Code']
    zip_code = zip_code.rjust(5,'0') if zip_code.present?

    current_account = Account.find_by(subdomain: Apartment::Tenant.current)
    @current_account = Account.find_by(subdomain: Apartment::Tenant.current)

    employee = Employee.new

    last_name = row['Last Name']
    last_name += ' ' + row['Suffix'].strip if row['Suffix'].present?

    employee.first_name = row['First Name']
    employee.last_name = last_name
    employee.middle_name = row['Middle Initial'] || ''
    employee.street = row['Street Address'] || ''
    employee.apartment = row['Apt'] || ''
    employee.city = row['Town'] || ''
    employee.state = row['State'] || 'New York'
    employee.zipcode = zip_code || ''
    employee.shield_number = row['ShieldNum'] || ''
    employee.title_code = row['Title Code'] || ''
    employee.janus_card = row['Janus Card'].present?
    employee.birthday = dob
    employee.social_security_number = ssn || ''
    employee.longevity_date = longevity_date
    employee.leave_progression_date = leave_progression_date
    employee.ncc_date = ncc_date
    employee.unit = unit if unit
    employee.gender_id = gender.id if gender
    employee.marital_status_id = marital_status.id if marital_status

    if employee.save
      employee.contacts.new(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE, value: work_phone).save! if work_phone
      employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE, value: cell_phone).save! if cell_phone
      employee.employee_departments.new(department_id: department.id).save(validate: false) if department
      employee.employee_sections.new(department_id: department.id, section_id: section.id).save(validate: false) if section
      employee.employee_titles.new(department_id: department.id, section_id: section.id, title_id: title.id).save(validate: false) if title
      employee.employee_benefits.new(benefit_id: benefit_type.id, start_date: Date.today).save(validate: false) if benefit_type
      employee.employee_employment_statuses.new(employment_status_id: employment_status.id, start_date: start_date).save(validate: false) if employment_status
      employee.employee_ranks.new(rank_id: rank.id).save(validate: false) if rank
    else
      @errors[@row_number] = employee.errors.full_messages
    end

  rescue => e
    p @row_number, e.message
    feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_members_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end

def check_gender(gender_type)
  return nil unless gender_type.present?

  gender = Gender.where(name: gender_type).first_or_create

  gender

rescue => e
  feed_errors('GENDER ' + gender.errors.full_messages)
end

def check_unit(unit_name)
  return nil unless unit_name.present?

  unit = Unit.where(name: unit_name).first_or_create

  unit

rescue => e
  feed_errors('UNIT ' + unit.errors.full_messages)
end

def check_marital_status(marital_status_name)
  return nil unless marital_status_name.present?

  marital_status = MaritalStatus.where(name: marital_status_name).first_or_create

  marital_status

rescue => e
  feed_errors('MARITAL STATUS ' + marital_status.errors.full_messages)
end

def check_benefit_type(benefit_type_name)
  return nil unless benefit_type_name.present?

  benefit_type = Benefit.where(name: benefit_type_name).first_or_create

  benefit_type

rescue => e
  feed_errors('BENEFIT TYPE ' + benefit_type.errors.full_messages)
end

def check_department(department_name)
  return nil unless department_name.present?

  department = Department.where(name: department_name).first_or_create

  department

rescue => e
  feed_errors('DEPARTMENT ' + department.errors.full_messages)
end

def check_section(section_name, department_id)
  return nil unless section_name.present?

  section = Section.where(department_id: department_id, name: section_name).first_or_create

  section

rescue => e
  feed_errors('SECTION ' + section.errors.full_messages)
end

def check_title(title_name, department_id, section_id)
  return nil unless title_name.present?

  title = Title.where(department_id: department_id, section_id: section_id, name: title_name).first_or_create

  title

rescue => e
  feed_errors('TITLE ' + title.errors.full_messages)
end

def check_rank(rank_name)
  return nil unless rank_name.present?

  rank = Rank.where(name: rank_name).first_or_create

  rank

rescue => e
  feed_errors('RANK ' + rank.errors.full_messages)
end


def check_employment_status(employment_status_name)
  return nil unless employment_status_name.present?

  employment_status_name = EmploymentStatus.where(name: employment_status_name).first_or_create

  employment_status_name

rescue => e
  feed_errors('EMPLOYEMENT STATUS ' + employment_status_name.errors.full_messages)
end

def check_paf(paf)
  if paf.present?
    paf = Pacf.where(name: 'Donating').first_or_create
  else
    paf = Pacf.where(name: 'Not Donating').first_or_create
  end

  paf

rescue => e
  feed_errors('PAF ' + paf.errors.full_messages)
end

def check_paf_opt_out(paf)
  return nil unless paf.present?

  paf_opt_out = Pacf.where(name: 'Opt-Out').first_or_create

  paf_opt_out

rescue => e
  feed_errors('PAF Opt Out ' + paf_opt_out.errors.full_messages)
end

def parse_amount(amount, type)
  return 0.00 unless amount.present?

  parsed_amount = amount.remove('US', ' ', '$', ' ').to_f

  parsed_amount

rescue => e
  feed_errors('AMOUNT - ' + type + ' ' + e.message)
end

def parse_date(date, type)
  return nil unless date.present?

  parsed_date = nil

  begin
    date_array = date.split('/')
    day = date_array[1].to_i
    month = date_array[0].to_i
    year = date_array[2].to_i

    if year.to_s.length == 2
      if year > 20 && year <= 99
        year = ('19' + year.to_s).to_i
      else
        year = ('20' + year.to_s).to_i
      end
    elsif year.to_s.length == 1
      year = ('200' + year.to_s).to_i
    elsif year.to_s.length > 4
      raise 'Invalid Year'
    else
      year = year.to_i
    end

    parsed_date = Date.new(year, month, day)
  rescue
    parsed_date = Date.parse(date)
  end

  parsed_date

rescue => e
  feed_errors('DATE - ' + type + " #{date} " + e.message)
end

def parse_string_date(date, type)
  return nil unless date.present?

  parsed_date = nil

  begin
    date_array = date.split('')
    year = date_array[0..1].join.to_i
    month = date_array[2..3].join.to_i
    day = date_array[4..5].join.to_i

    if year.to_s.length == 2
      if year > 20 && year <= 99
        year = ('19' + year.to_s).to_i
      else
        year = ('20' + year.to_s).to_i
      end
    elsif year.to_s.length == 1
      year = ('200' + year.to_s).to_i
    elsif year.to_s.length > 4
      raise 'Invalid Year'
    else
      year = year.to_i
    end

    parsed_date = Date.new(year, month, day)
  rescue
    parsed_date = Date.parse(date)
  end

  parsed_date

rescue => e
  feed_errors('DATE - ' + type + " #{date} " + e.message)
end

def parse_phone(phone)
  return nil unless phone.present?

  phone.remove!('(', ')', ' ', '-')

  phone_number = '(' + phone.first(3) + ') ' + phone[3..5] + ' - ' + phone.last(4)

  phone_number

rescue => e
  feed_errors('PHONE ' + e.message)
end

def parse_ssn(ssn)
  return nil unless ssn.present?

  ssn.remove!('-', ' ')

  # ssn_number = ssn.first(3) + ' - ' + ssn[3..4] + ' - ' + ssn.last(4)
  ssn_number = ssn.last(4)

  ssn_number
rescue => e
  feed_errors('SSN ' + e.message)
  raise
end

def feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end
