require 'csv'

desc 'import data'
task :btoba_update_gender_fields, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  csv_file.each do |row|

    first_name = row['FIRSTNAME']
    last_name = row['LASTNAME']

    @row_number = first_name + ' ' + last_name
    relation = coba_relationship(row['RELATION']) if row['RELATION']
    gender = btoba_gender(row['SEX']) if row['SEX']
    birthday = parse_date(row['DOB'], 'DOB') if row['DOB']

    current_account = Account.find_by(subdomain: Apartment::Tenant.current)
    @current_account = Account.find_by(subdomain: Apartment::Tenant.current)

    employee = Employee.find_by(payroll_id: row['MBRID']) if row['MBRID'].present?

    if employee.present? && gender.present?
      benefit_coverages = employee.benefit_coverages.where('name ilike ? and name ilike ?', (first_name + '%'), ('%' + last_name))
      beneficiaries = employee.beneficiaries.where('name ilike ? and name ilike ?', (first_name + '%'), ('%' + last_name))

      if benefit_coverages.present?
        benefit_coverages.update_all(gender_id: gender.id)
      else
        gender_feed_errors('No occurrences for benefit coverage')
      end

      if beneficiaries.present?
        beneficiaries = beneficiaries.where(birthday: birthday) if beneficiaries.present? && beneficiaries.count > 1 && birthday.present?
        beneficiaries = beneficiaries.where(relationship: relation) if beneficiaries.present? && beneficiaries.count > 1 && relation.present?

        if beneficiaries.present? && beneficiaries.count == 1
          beneficiary = beneficiaries.first
          beneficiary.update(gender_id: gender.id) if gender && beneficiary.present? && beneficiary.gender_id.blank?
        else
          gender_feed_errors('Multiple occurrences for beneficiary')
        end
      else
        gender_feed_errors('No occurrences for beneficiary')
      end

    else
      gender_feed_errors('Member or Gender does not exists')
    end

  rescue => e
    p @row_number, e.message
    gender_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_gender_update_import_errors.csv", 'w') do |csv|
    csv << ["Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

def btoba_gender(gender_type)
  return nil unless gender_type.present?
  gender = Gender.where(name: gender_type).first_or_create

  gender

rescue => e
  gender_feed_errors('GENDER ' + gender.errors.full_messages)
end

def parse_date(date, type)
  return nil unless date.present?

  parsed_date = nil

  begin
    date_array = date.split('-')
    day = date_array[2].to_i
    month = date_array[1].to_i
    year = date_array[0].to_i

    if year.to_s.length == 2
      if year > 20 && year <= 99
        year = ('19' + year.to_s).to_i
      else
        year = ('20' + year.to_s).to_i
      end
    elsif year.to_s.length == 1
      year = ('200' + year.to_s).to_i
    elsif year.to_s.length > 4
      raise 'Invalid Year'
    else
      year = year.to_i
    end

    parsed_date = Date.new(year, month, day)
  rescue
    parsed_date = Date.parse(date)
  end

  parsed_date

rescue => e
  gender_feed_errors('DATE - ' + type + " #{date} " + e.message)
end

def coba_relationship(relationship)
  return nil unless relationship.present?

  if %w(wife ex-spouse spouse).include?(relationship.downcase)
    'spouse'
  elsif %w(son daughter).include?(relationship.downcase)
    'child'
  elsif relationship.downcase == 'disabled dependent'
    'disabled_child'
  elsif %w(step-daughter step-son).include?(relationship.downcase)
    'step_child'
  end
end

def gender_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end
