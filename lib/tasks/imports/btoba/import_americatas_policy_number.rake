# frozen_string_literal: true

require 'csv'

desc 'import data'
task :import_americatas_policy_number, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  @errors = {}

  csv_file ||= CSV.parse(file, headers: true)
  csv_file.each do |row|
    employee_full_name = row["Insured Name"]
    first_name, last_name = employee_full_name.split.first, employee_full_name.split.last
    @row_number = employee_full_name
    middle_name ||= employee_full_name.split.second if employee_full_name.split.count == 3
    employee = Employee.where('lower(first_name) LIKE ? and lower(last_name) LIKE ?', "%#{first_name.downcase}%", "%#{last_name.downcase}%")
    employee = Employee.where('(lower(first_name) = ? and lower(last_name) = ?) OR (lower(first_name) = ? and lower(last_name) = ? and lower(middle_name) = ?)',
                              first_name.downcase + " #{middle_name&.downcase}.", last_name.downcase, first_name.downcase, last_name.downcase, middle_name&.downcase) if employee.count > 1 || employee.blank?
    policy_number = row["Policy Number "]

    if employee.first.present? && employee.count == 1
      employee.first.update_column(:previous_shield_number, policy_number)
    elsif employee.first.blank?
      member_feed_errors('No occurrences for the Employee')
    elsif employee.count > 1
      member_feed_errors('Multiple occurrences for the Employee')
    end

  rescue => e
    p @row_number, e.message
    member_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_member_update_americatas_policy_errors.csv", 'w') do |csv|
    csv << ["Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end

task :benefit_coverage_name_split, [:account] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  @errors = {}
  employees = Employee.kept
  employees.each do |employee|
    @row_number = employee.first_name + " " + employee.last_name
    benefit_coverages = employee.benefit_coverages
    next if benefit_coverages.blank?
    benefit_coverages.each do |benefit_coverage|
      full_name = benefit_coverage.name.split
      if full_name.blank?
        member_feed_errors("Benefit Coverage Name is blank")
        next
      end
      benefit_coverage.first_name = full_name.first
      benefit_coverage.last_name = if full_name.length > 2
                                     full_name.slice(1, full_name.length - 2).join(" ")
                                   elsif full_name.length > 1
                                     full_name.last
                                   end
      benefit_coverage.suffix = if full_name.length > 2
                                  full_name.last
                                end
      benefit_coverage.save!
    end

  rescue => e
    p @row_number, e.message
    member_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_benefit_coverage_name_split_errors.csv", 'w') do |csv|
    csv << ["Errors"]
    @errors.each do |error|
      csv << error
    end
  end
end

task :import_bsc_and_bt_and_emplid, [:account, :file_path] => :environment do |_t, args|
  processed_bsc_ids = []
  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  @errors = {}
  csv_file ||= CSV.parse(file, headers: true)
  csv_file.each do |row|

    bsc_number = row['BSC ID']
    bt_number = row['EMPLID'] || row['B&T ID']
    employees = Employee.where("placard_number = ?", bsc_number)
    full_name = row['Name']&.split(",")
    @row_number = full_name.join(" ") if full_name.present?
    if employees&.blank?
      if full_name&.present?
        first_name = full_name.last.split.first
        last_name = full_name.first
        employees = Employee.where('lower(first_name) like ? and lower(last_name) = ?', "#{first_name.downcase}%", last_name.downcase)
      end
    end

    if employees.present? && employees.count > 1
      member_feed_errors("More than One Employee found")
      next
    elsif employees.present? && employees.count == 1
      unless processed_bsc_ids.include?(bsc_number)
        employee = employees.first
        employee.placard_number = bsc_number if row['Status'] == 'Retired'
        employee.primary_work_location = bt_number
        employee.save
      end
    else
      member_feed_errors("Members Not found")
      next
    end
    processed_bsc_ids << bsc_number
  rescue => e
    p @row_number, e.message
    member_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_import_bsc_and_bt_and_emplid_#{args[:file_path].to_s.split("/").last.gsub(".csv", "")}.csv", 'w') do |csv|
    csv << ["Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

task :import_alternative_emails, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  @errors = {}
  EMAIL_REGEX = Devise.email_regexp

  csv_file ||= CSV.parse(file, headers: true)
  csv_file.each do |row|

    @row_number = row['Name']

    if @row_number.blank? || row['Alternative Email'].blank?
      member_feed_errors('Mandatory Details are not present')
      next
    end

    unless EMAIL_REGEX.match(row['Alternative Email'])
      member_feed_errors('Email is Not Valid')
      next
    end

    first_name = row['Name'].split.first
    last_name = row['Name'].split.last
    employees = Employee.where("lower(first_name) = ? and lower(last_name) = ?", first_name.downcase, last_name.downcase)

    if employees.blank?
      member_feed_errors("No Member Found")
      next
    elsif employees.count > 1
      member_feed_errors("More than One Member Found")
      next
    end

    employee = employees.first
    email_contact = employee.contacts.where(contact_for: "personal", contact_type: "email").first_or_create
    email_contact.update!(value: row['Alternative Email'])

  rescue => e
    p @row_number, e.message
    member_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_import_alternative_emails.csv", 'w') do |csv|
    csv << ["Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

task :send_member_user_credential_mailer_to_active_and_retired, [:account] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])
  employees = Employee.includes(:employee_employment_statuses).where("employee_employment_statuses.employment_status_id in (?) and employee_employment_statuses.end_date is NULL or employee_employment_statuses.end_date > ?", EmploymentStatus.where("lower(name) in (?)", ["active", "retired"]).pluck(:id), Date.today).references(:employee_employment_statuses)
  @mail_sented_members = []
  @errors = {}
  @already_sented_mail = ["Daniel V Reichert", "ROBERT BRAISTED JR", "Eugene P Ballestero", "Thomas A Bertuccio", "Jason E Vasquez", "Ivan W Kellman", "ANTHONY BARBATO", "Kamil M Zielinski", "Bryan C Walsh", "LARRY M HERZOG", "WAYNE JOSEPH", "Gladys L Despaigne", "Cerrone N Danzy", "Emil Sand", "MARY E. NOVAK", "Edwin Collazo JR", "Michael T Tritschler", "Erick Nieves", "RICARDO M CRUZ Jr", "Scott L Denley", "THOMAS ADRIAN", "Joseph A Marchese", "Edward J Daly"]
  employees.each do |employee|
    @row_number = employee.full_name
    next if @already_sented_mail.include?(@row_number)

    personal_email = employee.contacts.where(contact_type: "email", contact_for: "personal").first
    if personal_email&.value.blank?
      member_feed_errors('Mail is blank')
      next
    elsif employee.email_opt_out == true
      member_feed_errors('Email Opt Out box has been checked')
      next
    elsif employee.enable_mobile_access == false
      member_feed_errors('Mobile Access is not enabled for this Member')
      next
    end

    password = Devise.friendly_token(8)
    username = (employee.username.present? ? employee.username : personal_email.value)
    employee.assign_attributes(username: username, password: password, password_confirmation: password)
    employee.save!(validate: false)
    if MemberCredentialsMailer.details(username, password, personal_email.value, 'btoba').deliver_later
      @mail_sented_members << employee.full_name
    end

  rescue => e
    p @row_number, e.message
    member_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_send_member_user_credential_mailer_to_active_and_retired_errors.csv", 'w') do |csv|
    csv << ["Errors"]

    @errors.each do |error|
      csv << error
    end
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_mail_succeed_members.csv", 'w') do |csv|
    csv << ["Member Full Name"]

    @mail_sented_members.each do |name|
      csv << [name]
    end
  end
end

task :import_united_healthcare_id, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  @errors = {}
  csv_file ||= CSV.parse(file, headers: true)
  csv_file.each do |row|
    first_name = row['Fname']
    last_name = row['Lname']
    @row_number = "#{first_name} #{last_name}"
    next unless row['REL'] == 'EE'

    employees = Employee.where('lower(first_name) like ? and lower(last_name) = ?', "#{first_name.downcase}%", last_name.downcase)
    if employees.present? && employees.count > 1
      member_feed_errors('More than One Employee found')
      next
    elsif employees.present? && employees.count == 1
      employees = employees.first
      employees.update_columns(a_number: row['ALT ID'])
    else
      member_feed_errors('No matches found')
    end
  rescue => e
    p @row_number, e.message
    member_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_import_united_healthcare_id_#{args[:file_path].to_s.split('/').last.gsub('.csv', '')}.csv", 'w') do |csv|
    csv << ['Errors']

    @errors.each do |error|
      csv << error
    end
  end
end

def member_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end

