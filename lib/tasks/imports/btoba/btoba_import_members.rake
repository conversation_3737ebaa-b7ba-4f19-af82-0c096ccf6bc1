# frozen_string_literal: true

require 'csv'

desc 'import data'
task :btoba_import_members, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  # MBRID, SHIELD#, SSN, HEALTHPLEX#, EMPLOYEEID, FNAME, INIT, LNAME, ADDR1, ADDR2, CITY, STATE, ZIP, SUFFIX, PHONE1, CELLPHONE, DOB, SEX, MSTAT, STATUS, EMAIL, ALTEMAIL, EMPLOYER
  # BSCID,B&TID,Name,Pension #,Address 1,Address 2,City,State,Postal,Fac / Department,Marital Status,Birthdate,Hire Date,Empl Status

  csv_file.each do |row|

    # member_id = row['MBRID']
    # shield_number = row['SHIELD#'] || ''
    # ssn = btoba_parse_ssn(row['SSN']) || ''
    # healthplex_number = row['HEALTHPLEX#'] || ''
    # employee_id = row['EMPLOYEEID'] || ''
    # first_name = row['FNAME']
    # last_name = row['LNAME']
    # last_name += ' ' + row['SUFFIX'].strip if row['SUFFIX'].present?
    # mi = row['INIT'] || ''
    # home_phone = btoba_parse_phone(row['PHONE1'])
    # cell_phone = btoba_parse_phone(row['CELLPHONE'])
    # personal_email = row['EMAIL'] || ''
    # work_email = row['ALTEMAIL'] || ''
    # sex = btoba_check_gender(row['SEX'])

    placard_number = row['BSCID']
    @row_number = placard_number
    name = row['Name'].split(',')
    last_name = name.first
    first_name = name.last.split(' ').first
    mi = name.last.split(' ')[1..-1].join(' ')
    rdo = row['Pension #'] || ''
    street = row['Address 1'] || ''
    apartment = row['Address 2'] || ''
    city = row['City'] || ''
    state = row['State'] || ''
    bt_id = row['B&TID'] || ''
    zipcode = row['Postal'].present? ? row['Postal'].split('-').first.first(5).rjust(5, '0') : ''
    dob = btoba_parse_date(row['Birthdate'], 'DOB')
    hire_date = btoba_parse_date(row['Hire Date'], 'Hire Date')
    marital_status = btoba_check_marital_status(row['Marital Status'])
    employment_status = btoba_check_employment_status(row['Empl Status'])
    command = btoba_generate_offices(row['Fac / Department'])

    current_account = Account.find_by(subdomain: Apartment::Tenant.current)
    @current_account = Account.find_by(subdomain: Apartment::Tenant.current)

    employees = Employee.where("lower(first_name) like ? and lower(last_name) like ?", "#{first_name.downcase}%", "#{last_name.downcase}%")

    existing_member = false

    if employees.count == 1
      employee = employees.first
      existing_member = true
    elsif employees.count > 1
      btoba_feed_errors("#{row['Name']} - Multiple Members found")
      next
    else
      btoba_feed_errors("#{row['Name']} - Employee Not Found")
      next
      # employee = Employee.new
    end

    # employee.social_security_number = ssn
    # employee.shield_number = shield_number
    # employee.a_number = healthplex_number
    # employee.payroll_id = member_id
    # employee.title_code = employee_id
    # employee.gender_id = sex.id if sex

    employee.first_name = first_name
    employee.last_name = last_name
    employee.middle_name = mi
    employee.street = street
    employee.apartment = apartment
    employee.city = city
    employee.state = state
    employee.zipcode = zipcode
    employee.birthday = dob
    employee.start_date = hire_date
    employee.placard_number = placard_number
    employee.rdo = rdo
    employee.primary_work_location = bt_id
    employee.marital_status_id = marital_status.id if marital_status

    if employee.save
      unless existing_member
        personal_phone_contact = employee.contacts.where(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE).first_or_initialize
        # personal_phone_contact.value = cell_phone if cell_phone
        personal_phone_contact.save!

        home_phone_contact = employee.contacts.where(contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE)
        # home_phone_contact.value = home_phone if home_phone
        home_phone_contact.save!

        employee.contacts.new(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE).save!

        personal_email_contact = employee.contacts.where(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL)
        # personal_email_contact.value = personal_email if personal_email.present?
        personal_email_contact.save!

        personal_email_contact = employee.contacts.where(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL)
        # personal_email_contact.value = work_email if work_email.present?
        personal_email_contact.save!
      end

      create_new_status = true
      past_statuses = employee.employee_employment_statuses.where(discarded_at: nil, end_date: nil)
      if past_statuses.present?
        if past_statuses.map(&:status_name).uniq.include? employment_status.name.downcase
          create_new_status = false
        else
          create_new_status = true
          past_statuses.update_all(end_date: Date.today.beginning_of_month)
        end
      end
      employee.employee_employment_statuses.new(employment_status_id: employment_status.id).save(validate: false) if create_new_status && employment_status.present?

      employee.employee_offices.new(office_id: command.id).save(validate: false) if command
    else
      @errors[@row_number] = employee.errors.full_messages
    end

  rescue => e
    p @row_number, e.message
    btoba_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_member_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end

task :btoba_update_member_details, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  csv_file.each do |row|

    employee_id = row['EMPLID']
    @row_number = employee_id
    name = row['NAME'].split(',')
    last_name = name.first
    first_name = name.last.split(' ').first
    middle_name = name.last.split(' ')[1..-1].join(' ') || ''
    placard_number = row['BSCID']

    employment_status = btoba_check_employment_status(row['STATUS'])
    command = btoba_generate_offices(row['Facility'])

    @current_account = Account.find_by(subdomain: Apartment::Tenant.current)

    employee = Employee.find_by(title_code: employee_id)

    if employee.present?
      employee.first_name = first_name
      employee.last_name = last_name
      employee.middle_name = middle_name
      employee.placard_number = placard_number

      if employee.save
        if employment_status.present? && employee.employee_employment_statuses.where(discarded_at: nil, end_date: nil).map{|x| x.name}.exclude?(employment_status.name)
          employee.employee_employment_statuses.new(employment_status_id: employment_status.id).save(validate: false)
        end
        if command.present? && employee.employee_offices.where(discarded_at: nil, end_date: nil).map{|x| x.name}.exclude?(command.name)
          employee.employee_offices.new(office_id: command.id).save(validate: false)
        end
      else
        @errors[@row_number] = employee.errors.full_messages
      end

    else
      btoba_feed_errors("#{row['NAME']} - Employee Not Found")
      next
    end

  rescue
    p @row_number, e.message
    btoba_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_update_member_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

task :btoba_import_commands, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  # ID, NAME, ADDR1, ADDR2, CITY, STATE, ZIP

  csv_file.each do |row|
    @row_number = row['ID']

    office = Office.where(name: row['NAME']).first_or_initialize
    address = [row['ADDR1'], row['ADDR2'], row['CITY'], row['STATE'], row['ZIP']].compact.join(', ')
    office.address = address
    office.fax = row['ID']
    office.save(validate: false)
  rescue => e
    p @row_number, e.message
    btoba_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_command_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

task :btoba_import_beneficiaries, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  # MBRID, PERCENT, LEVEL, SSN, FAME, LNAME, INTI, ADDR1, ADDR2, CITY, STATE, ZIP, PHONE1, DOB, RELATION
  csv_file.each do |row|
    @row_number = row['MBRID']
    member = Employee.where(payroll_id: row['MBRID']).first
    next unless member.present?
    name = [row['FAME'], row['INTI'], row['LNAME']].compact.join(' ')
    percentage = row['PERCENT']
    address = [row['ADDR1'], row['ADDR2'], row['CITY'], row['STATE'], row['ZIP']].compact.join(' ')
    dob = Date.parse(row['DOB']) rescue nil
    relationship = row['RELATION'] || ''
    beneficiary_type = row['LEVEL'] == 'A' ? 'Primary' : 'Secondary'
    beneficiary = member.beneficiaries.new
    beneficiary.name = name
    beneficiary.address = address
    beneficiary.relationship = relationship
    beneficiary.beneficiary_type = beneficiary_type
    beneficiary.percentage = percentage
    beneficiary.birthday = dob
    beneficiary.save(validate: false)
  rescue => e
    p @row_number, e.message
    btoba_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_beneficiaries_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

task :btoba_import_dependents, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  now = Time.now

  @errors = {}

  # MBRID, DSEQ, SSN, FIRSTNAME, LASTNAME, ADDR1, ADDR2, CITY, STATE, ZIP, PHONE1, DOB, OPTICAL, RELATION, SEX, MARRIAGECERT, DIVORCE

  optical = Benefit.where(name: 'Optical').first_or_create
  dental = Benefit.where(name: 'Dental').first_or_create
  prescription = Benefit.where(name: 'Prescription').first_or_create

  csv_file.each do |row|
    @row_number = row['MBRID']
    next if ['Domestic Partner', 'Friend', 'Cousin'].include?(row['RELATION'])

    member = Employee.where(payroll_id: row['MBRID']).first
    unless member.present?
      btoba_feed_errors('Member not found')
      next
    end

    optical_benefit = member.employee_benefits.where(benefit_id: optical.id).first
    unless optical_benefit.present?
      optical_benefit = member.employee_benefits.new(benefit_id: optical.id, start_date: member.start_date)
      optical_benefit.save(validate: false)
    end

    dental_benefit = member.employee_benefits.where(benefit_id: dental.id).first
    unless dental_benefit.present?
      dental_benefit = member.employee_benefits.new(benefit_id: dental.id, start_date: member.start_date)
      dental_benefit.save(validate: false)
    end

    prescription_benefit = member.employee_benefits.where(benefit_id: prescription.id).first
    unless prescription_benefit.present?
      prescription_benefit = member.employee_benefits.new(benefit_id: prescription.id, start_date: member.start_date)
      prescription_benefit.save(validate: false)
    end

    name = [row['FIRSTNAME'], row['LASTNAME']].compact.join(' ')
    address = [row['ADDR1'], row['ADDR2'], row['CITY'], row['STATE'], row['ZIP']].compact.join(' ')
    dob = Date.parse(row['DOB']) rescue nil
    relationship = btoba_parse_relation(row['RELATION'])
    ssn = btoba_parse_ssn(row['SSN'])
    phone = btoba_parse_phone(row['PHONE1'])
    marriage_date = Date.parse(row['MARRIAGECERT']) rescue nil
    divorce_date = Date.parse(row['DIVORCE']) rescue nil
    optical_date = Date.parse(row['OPTICAL']) rescue nil
    dependent = row['DSEQ']

    benefit_coverage = member.benefit_coverages.new
    benefit_coverage.employee_benefit_id = optical_benefit.id
    benefit_coverage.name = name
    benefit_coverage.relationship = relationship
    benefit_coverage.dependent = dependent
    benefit_coverage.social_security_number = ssn
    benefit_coverage.address = address
    benefit_coverage.phone = phone
    benefit_coverage.birthday = dob
    benefit_coverage.age = now.year - dob.to_time.year - (dob.to_time.change(:year => now.year) > now ? 1 : 0) if dob
    if divorce_date.present?
      benefit_coverage.expires_at = divorce_date
    end
    benefit_coverage.save(validate: false)

    dental_coverage = benefit_coverage.dup
    dental_coverage.employee_benefit_id = dental_benefit.id
    dental_coverage.save(validate: false)

    prescription_coverage = benefit_coverage.dup
    prescription_coverage.employee_benefit_id = prescription_benefit.id
    prescription_coverage.save(validate: false)

    if optical_date.present?
      benefit_disbursement = member.benefit_disbursements.new
      benefit_disbursement.employee_benefit_id = optical_benefit.id
      benefit_disbursement.year = optical_date.year
      benefit_disbursement.date = optical_date
      benefit_disbursement.notes = "Dependent - #{name}"
      benefit_disbursement.save(validate: false)
    end

  rescue => e
    p @row_number, e.message
    btoba_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_optical_coverage_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

task :btoba_import_member_dates, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  now = Time.now

  @errors = {}

  # MBRID, DATETYPE, REASON/CODE, DATE

  benefit = Benefit.where(name: 'Optical').first_or_create

  csv_file.each do |row|
    @row_number = row['MBRID']
    type = row['DATETYPE']
    next if type == 'Benefits Coverage'

    member = Employee.where(payroll_id: row['MBRID']).first
    next unless member.present?

    date = Date.parse(row['DATE']) rescue nil
    next unless date.present?

    reason = row['REASON/CODE']

    case type
    when 'Hire Date'
      member.start_date = date
      member.save(validate: false)
      member.employee_benefits.update_all(start_date: date)
    when 'Marital Status', 'Marriage Certificate'
      if reason == 'Divorced'
        marital = btoba_check_marital_status('Divorced')
        member.marital_status_id = marital.id if marital
      end
      member.marital_status_date = date
      member.save(validate: false)
    when 'Optical Date'
      member_benefit = member.employee_benefits.where(benefit_id: benefit.id).first
      unless member_benefit.present?
        member_benefit = member.employee_benefits.new(benefit_id: benefit.id, start_date: member.start_date)
        member_benefit.save(validate: false)
      end

      benefit_disbursement = member.benefit_disbursements.where(date: date).first_or_initialize
      benefit_disbursement.employee_benefit_id = member_benefit.id
      benefit_disbursement.year = date.year
      benefit_disbursement.date = date
      benefit_disbursement.notes = "Member Visit"
      benefit_disbursement.save(validate: false)
    when 'Promotion Date'
      emp_status = btoba_check_employment_status('Promoted')
      promoted_status = member.employee_employment_statuses.where(employment_status_id: emp_status.id).first

      if emp_status && promoted_status
        promoted_status.start_date = date
        promoted_status.save(validate: false)
      end
    when 'Resignation Date'
      emp_status = btoba_check_employment_status('Resigned')
      resigned_status = member.employee_employment_statuses.where(employment_status_id: emp_status.id).first

      if emp_status && resigned_status
        resigned_status.start_date = date
        resigned_status.save(validate: false)
      end
    when 'Deceased Date'
      emp_status = btoba_check_employment_status('Deceased')
      deceased_status = member.employee_employment_statuses.where(employment_status_id: emp_status.id).first

      if emp_status && deceased_status
        deceased_status.start_date = date
        deceased_status.save(validate: false)
      end
    when 'Retirement Date'
      emp_status = btoba_check_employment_status('Retired')
      retired_status = member.employee_employment_statuses.where(employment_status_id: emp_status.id).first

      if emp_status && retired_status
        retired_status.start_date = date
        retired_status.save(validate: false)
      end
    when 'Termination Date'
      emp_status = btoba_check_employment_status('Terminated')
      terminated_status = member.employee_employment_statuses.where(employment_status_id: emp_status.id).first

      if emp_status && terminated_status
        terminated_status.start_date = date
        terminated_status.save(validate: false)
      end
    when 'Union Status'
      position = btoba_check_position('Board Member')

      member.employee_positions.new(position_id: position.id, start_date: date).save(validate: false) if position
    else
      puts "#{type} - Nothing satisfied"
    end

  rescue => e
    p @row_number, e.message
    btoba_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_member_dates_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

task :btoba_create_employee_benefits, [:account] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  optical_benefit = Benefit.where(name: 'Optical').first_or_create
  dental_benefit = Benefit.where(name: 'Dental').first_or_create
  prescription_benefit = Benefit.where(name: 'Prescription').first_or_create

  status_ids = EmploymentStatus.where(name: ['Active', 'Retired']).pluck(:id)
  emp_statuses = EmployeeEmploymentStatus.includes(:employee).where(employment_status_id: status_ids)
  emp_statuses.each do |emp_status|
    emp = emp_status.employee
    emp.employee_benefits.where(benefit_id: optical_benefit.id, start_date: emp.start_date).first_or_create
    emp.employee_benefits.where(benefit_id: dental_benefit.id, start_date: emp.start_date).first_or_create
    emp.employee_benefits.where(benefit_id: prescription_benefit.id, start_date: emp.start_date).first_or_create
  end
end

task :btoba_update_deceased, [:account] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  deceased_status = btoba_check_employment_status('Deceased')

  emp_statuses = EmployeeEmploymentStatus.includes(:employee).where(employment_status_id: deceased_status.id)
  emp_statuses.each do |emp_status|
    emp = emp_status.employee
    emp.employee_employment_statuses.where(employment_status_id: emp_status.id).update_all(end_date: emp_status.start_date)
    emp.employee_benefits.where(benefit_id: dental_benefit.id, start_date: emp.start_date).first_or_create
    emp.employee_benefits.where(benefit_id: prescription_benefit.id, start_date: emp.start_date).first_or_create
  end
end

def btoba_parse_date(date, type)
  return nil unless date.present?

  parsed_date = nil

  begin
    date_array = date.split('/')
    day = date_array[1].to_i
    month = date_array[0].to_i
    year = date_array[2].to_i

    if year.to_s.length == 2
      if year > 20 && year <= 99
        year = ('19' + year.to_s).to_i
      else
        year = ('20' + year.to_s).to_i
      end
    elsif year.to_s.length == 1
      year = ('200' + year.to_s).to_i
    elsif year.to_s.length > 4
      raise 'Invalid Year'
    end

    parsed_date = Date.new(year, month, day)
  rescue
    parsed_date = Date.parse(date)
  end

  parsed_date

rescue => e
  feed_errors('DATE - ' + type + ' ' + e.message)
end

def btoba_parse_phone(phone)
  return nil unless phone.present?
  return nil if phone.length < 10

  phone.remove!('(', ')', ' ', '-')

  phone_number = '(' + phone.first(3) + ') ' + phone[3..5] + ' - ' + phone.last(4)

  phone_number

rescue => e
  btoba_feed_errors('PHONE ' + e.message)
end

def btoba_parse_ssn(ssn)
  return nil unless ssn.present?

  ssn.remove!('(', ')', ' ', '-')

  ssn = ssn.rjust(9, '0')

  social_security = ssn.first(3) + '-' + ssn[3..4] + '-' + ssn.last(4)

  social_security

rescue => e
  btoba_feed_errors('SSN ' + e.message)
end

def btoba_check_gender(gender_type)
  return nil unless gender_type.present?

  gender = Gender.where(name: gender_type).first_or_create

  gender

rescue => e
  btoba_feed_errors('GENDER ' + gender.errors.full_messages)
end

def btoba_check_marital_status(marital_status_name)
  return nil unless marital_status_name.present?

  marital_status = MaritalStatus.where(name: marital_status_name).first_or_create

  marital_status

rescue => e
  btoba_feed_errors('MARITAL STATUS ' + marital_status.errors.full_messages)
end

def btoba_check_employment_status(employment_status_type)
  return nil unless employment_status_type.present?

  employment_status = EmploymentStatus.where(name: employment_status_type).first_or_create

  employment_status

rescue => e
  btoba_feed_errors('EMPLOYMENT_STATUS ' + employment_status.errors.full_messages)
end

def btoba_check_offices(id)
  return nil unless id.present?

  office = Office.where(fax: id).first

  office

rescue => e
  btoba_feed_errors('OFFICE ' + office.errors.full_messages)
end

def btoba_generate_offices(office_name)
  return nil unless office_name.present?

  office = Office.where("name = ? or fax = ?", office_name, office_name).first_or_create

  office

rescue => e
  btoba_feed_errors('OFFICE ' + office.errors.full_messages)
end

def btoba_check_position(position_name)
  return nil unless position_name.present?

  position = Position.where(name: position_name).first_or_create

  position

rescue => e
  btoba_feed_errors('POSITION ' + position.errors.full_messages)
end

def btoba_parse_relation(relation)
  if %w(Ex-Spouse Spouse Wife).include?(relation)
    'spouse'
  elsif %w(Daughter Son).include?(relation)
    'child'
  elsif %w(Step-Daughter Step-Son).include?(relation)
    'step_child'
  elsif 'Disabled Dependent' == relation
    'disabled_child'
  end
end

def btoba_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end

# bundle exec rake 'btoba_import_members[btoba,btoba_members.csv]'
# bundle exec rake 'btoba_import_commands[btoba,btoba_employers.csv]'
# bundle exec rake 'btoba_create_employee_benefits[btoba]'
# bundle exec rake 'btoba_import_beneficiaries[btoba,btoba_beneficiaries.csv]'
# bundle exec rake 'btoba_import_dependents[btoba,btoba_dependents.csv]'
# bundle exec rake 'btoba_import_member_dates[btoba,btoba_member_dates.csv]'

# Benefits Coverage
# COBRA Active
# COBRA Retiree
# Deceased Date
# Hire Date
# Marital Status
# Marriage Certificate
# Optical Date
# Promotion Date
# Resignation Date
# Retirement Date
# Termination Date
# Union Status

# Active
# Board Member
# COBRA
# Dental
# Divorced
# Married
# Optical & Dental
# Optical Date
# Retiree
# RX
# RX & Dental
# RX & Optical
# RX, Optical & Dental
# Single
