# frozen_string_literal: true

require 'csv'

desc 'import data'
task :scpc_import_members, %i[account file_path position_name] => :environment do |t, args|
  Apartment::Tenant.switch!(args[:account])

  contacts_hash = [
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL, value: '' },
    { contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE, value: '' },
    { contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE, value: '' },
    { contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL, value: '' },
    { contact_for: Contact::ContactFor::COLLEAGUE, contact_type: Contact::ContactType::EMERGENCY, value: '' },
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMERGENCY, value: '' },
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE, value: '' }
  ]

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  # count = 0
  # employee_statuses_array = []
  # employee_positions_array = []

  @errors = {}

  Employee.skip_callback :save, :after, :update_benefit_address
  Employee.skip_callback :commit, :after, :update_legislative_detail
  EmployeeEmploymentStatus.skip_callback :save, :after, :update_expiry_status

  active_status = EmploymentStatus.kept.where('lower(name) = ?', 'active').first_or_create!(name: 'ACTIVE')
  # retired_status = EmploymentStatus.kept.where('lower(name) = ?', 'retired').first_or_create!(name: 'RETIRED')
  # pba_position = Position.kept.where('lower(name) = ?', 'suffolk pba').first_or_create!(name: 'SUFFOLK PBA')
  employee_position = Position.kept.where('lower(name) = ?', args[:position_name].downcase).first_or_create!(name: args[:position_name])

  # statuses = { 'active' => active_status.id, 'retired' => retired_status.id }
  # positions = { 'suffolk pba' => pba_position.id, 'suffolk soa' => soa_position.id }

  csv_file.each do |row|
    first_name = row['FIRST NAME']&.strip || ''
    last_name = row['LAST NAME']&.strip || ''
    cell_number = parse_phone(row['PHONE 1']) || ''
    email = row['E-MAIL']&.strip || ''
    @row_name = "#{first_name} #{last_name}"

    employee = Employee.new(first_name: first_name, last_name: last_name)
    if employee.save
      contacts_hash.last[:value] = cell_number
      contacts_hash.first[:value] = email
      employee.contacts.import contacts_hash

      employee.employee_employment_statuses.create!(employment_status_id: active_status.id)
      employee.employee_positions.create!(position_id: employee_position.id)

      # employee_status_id = statuses[row['MEMBER TYPE']&.strip&.downcase]
      # employee_position_id = positions[row['Union Affilation']&.strip&.downcase]

      # employee_statuses_array << { employee_id: employee.id, employment_status_id: employee_status_id }
      # employee_positions_array << { employee_id: employee.id, position_id: employee_position_id }
    end

    # if count == 500
    #   EmployeeEmploymentStatus.import employee_statuses_array.flatten
    #   EmployeePosition.import employee_positions_array.flatten
    #   employee_statuses_array = []
    #   employee_positions_array = []
    #   count = 0
    # end
    # count += 1
  rescue StandardError => e
    p "#{@row_name} #{e.message}"
    store_error(e.message)
  end

  # EmployeeEmploymentStatus.import employee_statuses_array.flatten
  # EmployeePosition.import employee_positions_array.flatten

  CSV.open("#{Rails.root}/#{t}_errors_#{Time.now.to_i}.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']

    @errors.each do |row_number, error_messages|
      csv << [row_number, error_messages.join(';')] # Join errors into a single string
    end
  end

  Employee.set_callback :save, :after, :update_benefit_address
  Employee.set_callback :commit, :after, :update_legislative_detail
  EmployeeEmploymentStatus.set_callback :save, :after, :update_expiry_status
end

def create_employee_status(status)
  create_status = EmploymentStatus.where(name: status).first_or_create
  create_status
rescue StandardError => e
  store_error('Employment Status' + e.message)
end

def parse_phone(phone)
  return nil unless phone.present?

  phone.remove!('(', ')', ' ', '-')

  phone_number = '(' + phone.first(3) + ') ' + phone[3..5] + ' - ' + phone.last(4)

  phone_number
rescue StandardError => e
  store_error('PHONE ' + e.message)
end

def store_error(message)
  if @errors[@row_name]
    @errors[@row_name] << message
  else
    @errors[@row_name] = [ message ]
  end
end

def scpc_create_new_employee(first_name, last_name, union_affiliation, union_status)
  employee = Employee.new(first_name: first_name, last_name: last_name)

  if employee.save
    employee.employee_employment_statuses.create!(employment_status: union_status)
    employee.employee_positions.create!(position: union_affiliation)
    # employee.employee_ranks.create!(rank: employee_rank) unless employee_rank.blank?

    employee.contacts.create!(contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE, value: '')
    employee.contacts.create!(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE, value: '')
    employee.contacts.create!(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL, value: '')
    employee.contacts.create!(contact_for: Contact::ContactFor::COLLEAGUE, contact_type: Contact::ContactType::EMERGENCY, value: '')
    employee.contacts.create!(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMERGENCY, value: '')
    employee.contacts.create!(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL, value: '')
    employee.contacts.create!(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE, value: '')

    # employee.contacts.where(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL).first.update!(value: email)
    # employee.contacts.where(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE).first.update!(value: cell_number)
  end
rescue StandardError => e
  p "#{@row_name} #{e.message}"
  store_error(e.message)
end

def split_name(name)
  name = name.split
  if name.length > 2 && name.include?('III,')
    first_name = name[0..1].join(' ')
    last_name = name[2..-1].join(' ')
  else
    first_name = name.first
    last_name = name[1..-1].join(' ')
  end
  [first_name, last_name]
end

task :scpc_import_and_update_members, %i[account file_path position_name] => :environment do |t, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  @errors = {}
  Employee.skip_callback :save, :after, :update_benefit_address
  Employee.skip_callback :commit, :after, :update_legislative_detail
  EmployeeEmploymentStatus.skip_callback :save, :after, :update_expiry_status

  active_status = EmploymentStatus.kept.where('lower(name) = ?', 'active').first_or_create!(name: 'ACTIVE')
  employee_position = Position.kept.where('lower(name) = ?', args[:position_name].downcase).first_or_create!(name: args[:position_name])

  csv_file.each do |row|
    next if row['Name'].nil?

    first_name, last_name = split_name(row['Name'])
    cell_number = parse_phone(row['Cell']) || ''
    email = row['Email']&.strip || ''
    rank = row['Rank']&.strip || ''
    employee_rank = Rank.kept.where('lower(name) = ?', rank&.downcase).first_or_create!(name: rank) unless rank.blank?
    @row_name = row['Name']
    next if (cell_number.blank? && email.blank?) || (first_name.blank? || last_name.blank?)

    employees = Employee.kept.where("lower(REPLACE(first_name, ',', '')) = ? AND lower(REPLACE(last_name, ',', '')) = ?", first_name.downcase.gsub(',', ''), last_name.downcase.gsub(',', ''))
    if employees.count > 1
      store_error("More than one employee found: #{employees.count}")
    elsif employees.empty?
      scpc_create_new_employee(first_name, last_name, cell_number, email, active_status, employee_position, employee_rank)
    else
      employee = employees.first
      employee.employee_positions.find_or_create_by(position_id: employee_position.id)
      employee.employee_employment_statuses.find_or_create_by(employment_status_id: active_status.id)
      employee.employee_ranks.find_or_create_by(rank_id: employee_rank.id) unless employee_rank.blank?

      if email.present?
        email_contact = employee.contacts.find_or_initialize_by(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL)
        email_contact.value = email
        email_contact.save!
      end

      if cell_number.present?
        phone_contact = employee.contacts.find_or_initialize_by(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE)
        phone_contact.value = cell_number
        phone_contact.save!
      end
    end
  rescue StandardError => e
    p "#{@row_name} #{e.message}"
    store_error(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:position_name]}_errors_#{Time.now.to_i}.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']

    @errors.each do |row_number, error_messages|
      csv << [row_number, error_messages.join(';')] # Join errors into a single string
    end
  end

  Employee.set_callback :save, :after, :update_benefit_address
  Employee.set_callback :commit, :after, :update_legislative_detail
  EmployeeEmploymentStatus.set_callback :save, :after, :update_expiry_status
end

task :scpc_create_or_update_retired_members, %i[account file_path] => :environment do |t, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  @errors = {}
  Employee.skip_callback :save, :after, :update_benefit_address
  Employee.skip_callback :commit, :after, :update_legislative_detail
  EmployeeEmploymentStatus.skip_callback :save, :after, :update_expiry_status

  employee_position_suffolk_soa = Position.kept.where('lower(name) = ?', 'suffolk soa').first_or_create!(name: 'Suffolk SOA')
  employee_position_suffolk_pba = Position.kept.where('lower(name) = ?', 'suffolk pba').first_or_create!(name: 'Suffolk PBA')
  employee_status = EmploymentStatus.kept.where('lower(name) = ?', 'inactive retiree').first_or_create!(name: 'Inactive Retiree')
  retired_status = EmploymentStatus.kept.where("lower(name) = ?", "retired").first

  csv_file.each do |row|
    first_name = row['First Name']
    last_name = row['Last Name']
    union_affiliation = row['Union Affiliation']
    @row_name = row['First Name'] + ' ' + row['Last Name']
    employee_position = if union_affiliation.downcase == 'suffolk soa'
                          employee_position_suffolk_soa
                        else
                          employee_position_suffolk_pba
                        end

    employees = Employee.kept.where("lower(REPLACE(first_name, ',', '')) = ? AND lower(REPLACE(last_name, ',', '')) = ?", first_name.downcase.gsub(',', ''), last_name.downcase.gsub(',', ''))

    if employees.count > 1
      store_error("More than one employee found: #{employees.count}")
    elsif employees.empty?
      scpc_create_new_employee(first_name, last_name, employee_position, employee_status)
    else
      employee = employees.first
      employee.employee_positions.find_or_create_by(position_id: employee_position.id)
      existing_retired_status = if retired_status
                                  employee.employee_employment_statuses.where(employment_status_id: retired_status.id).first
                                end

      if existing_retired_status
        existing_retired_status.update_columns(employment_status_id: employee_status.id)
      else
        employee.employee_employment_statuses.find_or_create_by(employment_status_id: employee_status.id)
      end
    end
  rescue StandardError => e
    p "#{@row_name} #{e.message}"
    store_error(e.message)
  end
  file_name = args[:file_path].split('.').first
  CSV.open("#{Rails.root}/#{file_name}_errors_#{Time.now.to_i}.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']

    @errors.each do |row_number, error_messages|
      csv << [row_number, error_messages.join(';')] # Join errors into a single string
    end
  end

  Employee.set_callback :save, :after, :update_benefit_address
  Employee.set_callback :commit, :after, :update_legislative_detail
  EmployeeEmploymentStatus.set_callback :save, :after, :update_expiry_status
end

# bundle exec rake 'scpc_import_members[scpc, scpc contact info (1)2025 (1).csv]'
# bundle exec rake 'scpc_import_members[scpc, Quogue ROster 2025.csv, Quogue Village]'
# bundle exec rake 'scpc_import_members[scpc, Riverhead PBA Full List March 2025 SCPC.csv, Riverhead Town PBA]'
# bundle exec rake 'scpc_import_and_update_members[scpc,scpc/Amityville-PBA-Table.csv, Amityville PBA]'
# bundle exec rake 'scpc_import_and_update_members[scpc,scpc/EASTHAMPTON-VILLAGE-PBA-Table.csv,EASTHAMPTON VILLAGE PBA]'
# bundle exec rake 'scpc_import_and_update_members[scpc,scpc/lloyd-Harbor-PBA-Table.csv,lloyd Harbor PBA]'
# bundle exec rake 'scpc_import_and_update_members[scpc,scpc/Northport-PBA-Table.csv,Northpost PBA]'
# bundle exec rake 'scpc_import_and_update_members[scpc,scpc/Sag-Harbor-PBA-Table.csv,Sag Harbor PBA]'
# bundle exec rake 'scpc_import_and_update_members[scpc,scpc/Shelter-Island-PBA-Table.csv,Shelter Island PBA]'
# bundle exec rake 'scpc_import_and_update_members[scpc,scpc/Southampton-SOA-Table.csv,Southampton SOA]'
# bundle exec rake 'scpc_import_and_update_members[scpc,scpc/Southampton-Town-PBA-Table.csv,Sounthampton Town PBA]'
# bundle exec rake 'scpc_import_and_update_members[scpc,scpc/Southold-PBA-Table.csv,Sounthold PBA]'
# bundle exec rake 'scpc_import_and_update_members[scpc,scpc/Suffolk-DA-Investigator-PBA-Table.csv,Suffolk DA Invertigator PBA]'
# bundle exec rake 'scpc_import_and_update_members[scpc,scpc/Westhampton-Beach-PBA-Table.csv,Westhampton Beach PBA]'
# bundle exec rake 'scpc_import_and_update_members[scpc,scpc/Huntington-Bay-PBA-Table.csv,Huntington Bay PBA]'
# bundle exec rake 'scpc_create_or_update_retired_members[scpc, scpc_retired_list_highlighted.csv]'