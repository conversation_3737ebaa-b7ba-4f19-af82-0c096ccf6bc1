# frozen_string_literal: true

require 'csv'

desc 'import data'
task :nassaucoba_update_retiree, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  csv_file.each do |row|

    employment_status = check_employment_status(row['Employment Status'])
    dob = parse_date(row['D.O.B.'], 'DOB') if row['D.O.B']
    zip_code = row['Zip Code']
    zip_code = zip_code.rjust(5,'0') if zip_code.present?
    work_phone = parse_phone(row['Phone #']) if row['Phone #']

    current_account = Account.find_by(subdomain: Apartment::Tenant.current)
    @current_account = Account.find_by(subdomain: Apartment::Tenant.current)

    @first_name = row['First Name']
    @last_name = row['Last Name']
    @mi = row['M.I.'] || ''

    employee = Employee.new

    employee.first_name = row['First Name']
    employee.last_name = row['Last Name']
    employee.middle_name = row['M.I.'] || ''
    employee.street = row['Street Address'] || ''
    employee.apartment = row['Apt'] || ''
    employee.city = row['Town'] || ''
    employee.state = row['State'] || 'New York'
    employee.zipcode = zip_code || ''
    employee.birthday = dob

    if employee.save
      employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE, value: work_phone).save! if work_phone
      employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL, value: row['Email Address']).save! if row['Email Address']
      employee.employee_employment_statuses.new(employment_status_id: employment_status.id).save(validate: false) if employment_status
    else
      @errors[@row_number] = employee.errors.full_messages
    end

  rescue => e
    p @row_number, e.message
    feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_retirees_members_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end

def check_employment_status(employment_status_name)
  return nil unless employment_status_name.present?

  employment_status_name = EmploymentStatus.where(name: employment_status_name).first_or_create

  employment_status_name

rescue => e
  feed_errors('EMPLOYEMENT STATUS ' + employment_status_name.errors.full_messages)
end

def parse_date(date, type)
  return nil unless date.present?

  parsed_date = nil

  begin
    date_array = date.split('/')
    day = date_array[1].to_i
    month = date_array[0].to_i
    year = date_array[2].to_i

    if year.to_s.length == 2
      if year > 20 && year <= 99
        year = ('19' + year.to_s).to_i
      else
        year = ('20' + year.to_s).to_i
      end
    elsif year.to_s.length == 1
      year = ('200' + year.to_s).to_i
    elsif year.to_s.length > 4
      raise 'Invalid Year'
    else
      year = year.to_i
    end

    parsed_date = Date.new(year, month, day)
  rescue
    parsed_date = Date.parse(date)
  end

  parsed_date

rescue => e
  feed_errors('DATE - ' + type + " #{date} " + e.message)
end

def parse_phone(phone)
  return nil unless phone.present?

  phone.remove!('(', ')', ' ', '-')

  phone_number = '(' + phone.first(3) + ') ' + phone[3..5] + ' - ' + phone.last(4)

  phone_number

rescue => e
  feed_errors('PHONE ' + e.message)
end

def feed_errors(message)
  @errors[@first_name] = [@last_name, @mi, message]
end
