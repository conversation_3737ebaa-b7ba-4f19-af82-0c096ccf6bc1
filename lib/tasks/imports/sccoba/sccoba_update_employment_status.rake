# frozen_string_literal: true

require 'csv'

desc 'import data'
task :sccoba_update_employment_status, [:account] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  active_dues_status = EmploymentStatus.kept.where(name: 'Active - Dues Paying')
  retired_dues_status = EmploymentStatus.kept.where(name: 'Retired - Dues Paying')

  active_status = EmploymentStatus.where(name: 'Active').first_or_create
  retired_status = EmploymentStatus.where(name: 'Retired').first_or_create

  active_employees = Employee.kept.includes(:employee_employment_statuses).where(employee_employment_statuses: { employment_status_id: active_dues_status.first.id })
                             .where('employee_employment_statuses.end_date is NULL OR employee_employment_statuses.end_date >= ?', Date.today) if active_dues_status.present?
  retired_employees = Employee.kept.includes(:employee_employment_statuses).where(employee_employment_statuses: { employment_status_id: retired_dues_status.first.id })
                              .where('employee_employment_statuses.end_date is NULL OR employee_employment_statuses.end_date >= ?', Date.today) if retired_dues_status.present?

  if active_employees.present? && active_status.present?
    active_employees.each do |active_employee|
      employee_employment_status = active_employee.employee_employment_statuses.new(employment_status_id: active_status.id)
      employee_employment_status.save(validate: false)
    end
  end

  if retired_employees.present? && retired_status.present?
    retired_employees.each do |retired_employee|
      employee_employment_status = retired_employee.employee_employment_statuses.new(employment_status_id: retired_status.id)
      employee_employment_status.save(validate: false)
    end
  end
end

task :sccoba_create_due_reports, [:account, :date, :comparison] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  begin
    input_date = Date.parse(args[:date])
  rescue StandardError
    puts 'Invalid date format, please use yyyy-mm-dd'
    next
  end

  case args[:comparison]
  when 'greater'
    operator = '>'
  when 'greater_equal'
    operator = '>='
  when 'lesser'
    operator = '<'
  when 'lesser_equal'
    operator = '<='
  else
    puts 'Invalid comparison,please use greater,greater_equal,lesser,lesser_equal'
    next
  end
  file_path = "#{Rails.root}/#{args[:account]}_#{Time.now.to_i}.csv"

  CSV.open(file_path, 'w') do |csv|
    csv << %w[Name Shield Title Employment_Status]

    Employee.kept.joins(:employee_pacfs).where("employee_pacfs.date #{operator} ?", input_date).includes(:employee_ranks, :employee_employment_statuses).find_each do |employee|
      employee_rank = employee.employee_ranks.where('employee_ranks.end_date IS NULL OR employee_ranks.end_date > ?', Date.today).first
      employment_status = employee.employee_employment_statuses.where('employee_employment_statuses.end_date IS NULL OR employee_employment_statuses.end_date > ?', Date.today).first
      csv << [
        "#{employee.first_name} #{employee.last_name}",
        employee.shield_number,
        employee_rank&.name,
        employment_status&.name
      ]
    end
  end
end


# bundle exec rake 'sccoba_update_employment_status[sccoba]'
# bundle exec rake 'sccoba_create_due_reports[sccoba,2024-12-01,greater_equal]' , bundle exec rake 'sccoba_create_due_reports[sccoba,2024-12-01,lesser_equal]' , bundle exec rake 'sccoba_create_due_reports[sccoba,2024-12-01,greater]' ,bundle exec rake 'sccoba_create_due_reports[sccoba,2024-12-01,lesser]'