# frozen_string_literal: true

require 'csv'

desc 'import data'
task :papba_import_members, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  csv_file.each do |row|
    @row_number = row['PA-Emp-No']
    if (row['First'].blank? && row['Last'].blank?) || row['First'].blank? || row['Last'].blank?
      @row_number = row['PA-Emp-No']
      papba_feed_errors("Mandatory details aren't present")
      next
    end
    @current_account = Account.find_by(subdomain: Apartment::Tenant.current)

    employee = Employee.new
    employee.a_number = row['PA-Emp-No'] || ''
    employee.last_name = row['Last'] || ''
    employee.first_name = row['First'] || ''
    employee.street = row['Addr1'] || ''
    employee.city = row['City'] || ''
    employee.state = row['State'] || ''
    employee.birthday = papba_parse_date(row['Date-Birth'], 'Member Birthday') || ''
    employee.start_date = papba_parse_date('14/07/23', 'Date Employed') || ''
    employee.member_start_date = papba_parse_date('05/01/24', 'Date Graduated') || ''
    if row['Sex'].downcase == 'male'
      sex = Gender.where("lower(name) = ?", 'm').first
    elsif row['Sex'].downcase == 'female'
      sex = Gender.where("lower(name) = ?", 'f').first
    end
    sex = Gender.create!(name: row['Sex']) if sex.blank? && row['Sex'].present?
    employee.gender_id = sex&.id || ''
    employee.previous_shield_number = '122'
    if employee.save!
      home_number = format_phone_number(row['Home-No'])
      personal_number = format_phone_number(row['Call-No'])
      emergency_number = format_phone_number(row['Emergency contact phone number'])
      additional_attributes = {
        contact_name: row['Emergency contact name'],
        contact_relationship: row['Relationship to emergency contact']
      }
      create_contacts(employee, Contact::ContactFor::HOME, Contact::ContactType::PHONE, home_number)
      create_contacts(employee, Contact::ContactFor::PERSONAL, Contact::ContactType::PHONE, personal_number)
      create_contacts(employee, Contact::ContactFor::WORK, Contact::ContactType::PHONE, '')
      create_contacts(employee, Contact::ContactFor::PERSONAL, Contact::ContactType::EMAIL, '')
      create_contacts(employee, Contact::ContactFor::WORK, Contact::ContactType::EMAIL, '')
      create_contacts(employee, Contact::ContactFor::PERSONAL, Contact::ContactType::EMERGENCY, emergency_number, additional_attributes)
      create_contacts(employee, Contact::ContactFor::COLLEAGUE, Contact::ContactType::EMERGENCY, '')
      status = 'Active'
      employment_status = EmploymentStatus.where("lower(name) = ?", status.downcase).first
      unless employment_status
        employment_status = EmploymentStatus.create!(name: status)
      end
      employee.employee_employment_statuses.new(employment_status_id: employment_status.id, start_date: papba_parse_date('7/1/24', "Employment Status Start Date")).save!

      command_status = Office.where("lower(name) = ?", row['Command'].downcase).first
      unless command_status
        command_status = Office.create!(name: row['Command'])
      end
      employee.employee_offices.new(office_id: command_status.id, start_date: papba_parse_date('7/1/24', "Command Start Date")).save!
    else
      @errors[@row_number] = employee.errors.full_messages
    end

  rescue => e
    p @row_number, e.message
    papba_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_member_import_errors.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']

    @errors.each do |error|
      csv << error
    end
  end
end

task :papba_import_member_contact_information, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  EMAIL_REGEX = Devise.email_regexp
  csv_file.each do |row|
    @row_number = row['PA-Emp-No']
    next if row['Type'].blank?

    employee = Employee.where(a_number: @row_number)
    if employee.present? && employee.count > 1
      papba_feed_errors("#{employee.first.first_name + " " + employee.first.last_name  } - More than One Employee Found")
      next
    elsif employee.blank?
      papba_feed_errors("Employee not found")
      next
    end

    type = row['Type']
    if type.downcase.delete(" ").include?('phone')
      if type.split.first.downcase == "cell"
        contact_for = Contact::ContactFor::PERSONAL
      elsif type.split.first.downcase == 'work'
        contact_for = Contact::ContactFor::WORK
      end
      phone_number = row['Data'].gsub("(", "").gsub(")", "").gsub(" ", "").gsub('-', "") if row['Data'].present?
      phone_number = phone_number&.gsub(/(\d{3})(\d{3})(\d{4})/, '(\1) \2 - \3')
      contact = employee.first.contacts.where(contact_type: Contact::ContactType::PHONE, contact_for: contact_for).first_or_initialize
      contact.value = phone_number
      contact.save!
    elsif type.gsub('-', '').downcase.include?('email')
      email = row['Data']
      contact_for = Contact::ContactFor::PERSONAL # assuming here it is personal email
      if email.present? && EMAIL_REGEX.match(email)
        contact = employee.first.contacts.where(contact_type: Contact::ContactType::EMAIL, contact_for: contact_for).first_or_initialize
        contact.value = email
        contact.save!
      else
        papba_feed_errors("Invalid Email address")
      end
    end

  rescue => e
    p @row_number, e.message
    papba_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_contact_info_member_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

task :papba_import_member_rank, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  csv_file.each do |row|
    @row_number = row['PA-Emp-No']
    if row['Grade'].blank?
      papba_feed_errors("Mandatory Details aren't Present")
      next
    end

    employee = Employee.where(a_number: @row_number)
    if employee.present? && employee.count > 1
      papba_feed_errors("#{employee.first.first_name + " " + employee.first.last_name  } - More than One Employee Found")
      next
    elsif employee.blank?
      papba_feed_errors("Employee not found")
      next
    end

    rank = Rank.where(name: row['Grade']).first_or_create if row['Grade'].present?
    start_date = papba_parse_date(row['EffectiveDate'], "Rank Start Date") || ""
    employee_ranks = employee.first.employee_ranks
    employee_rank = employee_ranks.new
    employee_rank.rank_id = rank&.id
    employee_rank.start_date = start_date
    employee_rank.save!

    if employee_ranks.present? && employee_ranks.count > 1 && start_date.present?
      employee_ranks.order(:start_date).each do |rank|
        if ((rank.start_date.present? && rank.start_date <= start_date && rank.end_date.present? && rank.end_date >= start_date) || (rank.start_date.present? && rank.start_date <= start_date && rank.end_date.blank?)) && rank.start_date != start_date
          rank.end_date = start_date
          rank.save!
        elsif ((rank.start_date.present? && rank.start_date >= start_date && rank.end_date.present? && rank.end_date <= start_date) || (rank.start_date.present? && rank.start_date >= start_date && rank.end_date.blank?)) && rank.start_date != start_date
          employee_rank.end_date = start_date
          employee_rank.end_date = rank.start_date
          employee_rank.save!
        end
      end
    end

  rescue => e
    p @row_number, e.message
    papba_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_employee_rank_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

task :papba_import_member_command, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  csv_file.each do |row|
    @row_number = row['PA-Emp-No']
    if row['FAC'].blank?
      papba_feed_errors("Mandatory details aren't present")
      next
    end

    employee = Employee.where(a_number: @row_number)
    if employee.present? && employee.count > 1
      papba_feed_errors("#{employee.first.first_name + " " + employee.first.last_name  } - More than One Employee Found")
      next
    elsif employee.blank?
      papba_feed_errors("Employee not found")
      next
    end

    office = Office.where(name: row['FAC']).first_or_create if row['FAC'].present?
    start_date = papba_parse_date(row['DateAssigned'], "Office Start Date") || ""
    employee_offices = employee.first.employee_offices
    employee_office = employee.first.employee_offices.new
    employee_office.office_id = office&.id
    employee_office.start_date = start_date
    employee_office.save!

    if employee_offices.present? && employee_offices.count > 1 && start_date.present?
      employee_offices.order(:start_date).each do |office|
        if ((office.start_date.present? && office.start_date <= start_date && office.end_date.present? && office.end_date >= start_date) || (office.start_date.present? && office.start_date <= start_date && office.end_date.blank?)) && office.start_date != start_date
          office.end_date = start_date
          office.save!
        elsif ((office.start_date.present? && office.start_date >= start_date && office.end_date.present? && office.end_date <= start_date) || (office.start_date.present? && office.start_date >= start_date && office.end_date.blank?)) && office.start_date != start_date
          employee_office.end_date = start_date
          employee_office.end_date = office.start_date
          employee_office.save!
        end
      end
    end

  rescue => e
    p @row_number, e.message
    papba_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_employee_office_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

task :papba_import_beneficiaries, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  csv_file.each_with_index do |row, index|
    @row_number = row['PA-Emp-No']
    full_name = full_name(row['Bfirst'], row['BMI'], row['Blast'], row['Bsuff'])
    if @row_number.blank?
      papba_feed_errors("Member detail isn't present")
      next
    elsif full_name.blank?
      papba_feed_errors("Mandatory details aren't present")
      next
    end

    employee = Employee.where(a_number: @row_number)
    if employee.present? && employee.count > 1
      papba_feed_errors("More than one Member found")
      next
    elsif employee.blank?
      papba_feed_errors("Member not found")
      next
    end

    beneficiary = employee.first.beneficiaries.new
    beneficiary.name = full_name
    beneficiary.relationship = row['Brelation'] || ''
    social_security_number = row['BSSN'].gsub('-', '').gsub(/(\d{3})(\d{2})(\d{4})/, '\1-\2-\3') if row['BSSN'].present?
    beneficiary.social_security_number = social_security_number || ''
    beneficiary.beneficiary_type = "Primary"
    beneficiary.address = employee.first.full_address
    beneficiary.save!


    beneficiaries = employee.first.beneficiaries

    if beneficiaries.present? && beneficiaries.count > 1
      percentage = 100.0 / beneficiaries.count
      beneficiaries.update_all(percentage: percentage.round(2))
    elsif beneficiaries.present? && beneficiaries.count == 1
      beneficiary.percentage = 100.0
      beneficiary.save!
    end


  rescue => e
    p @row_number, e.message
    papba_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_employee_beneficiary_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

task :papba_import_benefit_coverages, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  csv_file.each do |row|
    @row_number = row['PA-Emp-No']
    full_name = full_name(row['Dfirst'], row['DMI'], row['Dlast'], row['Dsuff'])
    if @row_number.blank?
      papba_feed_errors("Member detail isn't present")
      next
    elsif full_name.blank?
      papba_feed_errors("Mandatory details aren't present")
      next
    end

    employee = Employee.where(a_number: @row_number)
    if employee.present? && employee.count > 1
      papba_feed_errors("More than One Employee Found")
      next
    elsif employee.blank?
      papba_feed_errors("Employee not found")
      next
    end

    benefit_coverage = employee.first.benefit_coverages.new
    benefit_coverage.name = full_name
    benefit_coverage.relationship = row['Drel']&.downcase || ''
    sex = Gender.where(name: row['Sex']).first_or_create if row['Sex'].present?
    benefit_coverage.gender_id = sex&.id || ''
    birthday = papba_parse_date(row['Date-Dbirth'], "Benefit Coverage Birthday") || ""
    benefit_coverage.birthday = birthday
    social_security_number = row['DSSN'].gsub('-', '').gsub(/(\d{3})(\d{2})(\d{4})/, '\1-\2-\3') if row['DSSN'].present?
    benefit_coverage.social_security_number = social_security_number || ''
    benefit_coverage.address = employee.first.full_address
    benefit_coverage.save!

  rescue => e
    p @row_number, e.message
    papba_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_employee_benefit_coverage_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

task :papba_import_additional_members, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  EMAIL_REGEX = Devise.email_regexp

  csv_file.each do |row |

    @row_number = row['Emplid']
    if (row['First Name'].blank? && row['Last Name'].blank?) || row['First Name'].blank? || row['Last Name'].blank?
      papba_feed_errors("Mandatory details aren't present")
      next
    end

    @current_account = Account.find_by(subdomain: Apartment::Tenant.current)

    existing_employee = Employee.where(a_number: row['Emplid'])

    if existing_employee.present?
      employee = existing_employee.first
    else
      employee = Employee.new
    end

    employee.a_number = row['Emplid'] || ''
    employee.shield_number = row['Shield Number'] || ''
    employee.last_name = row['Last Name'] || ''
    employee.first_name = row['First Name'] || ''
    employee.street = row['Add1'] || ''
    employee.apartment = row['Apt'] || ''
    employee.city = row['City'] || ''
    employee.state = row['State'] || ''
    employee.zipcode = row[' Zip'] || ''
    employee.birthday = papba_parse_date(row['Birthdate'], "Additional Member Birthday") || ''

    if employee.save!
      phone_number = row['Home/Main'].gsub("/", "").gsub(" ", "").gsub('-', "") if row['Home/Main'].present?
      phone_number = phone_number&.gsub(/(\d{3})(\d{3})(\d{4})/, '(\1) \2 - \3') || ""

      employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE).save!
      employee.contacts.new(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE).save!
      if phone_number.present?
        employee.contacts.new(contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE, value: phone_number).save!
      else
        employee.contacts.new(contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE).save!
      end

      employee.contacts.new(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL).save!
      employee.contacts.new(contact_for: Contact::ContactFor::COLLEAGUE, contact_type: Contact::ContactType::EMAIL).save!
      email = row['Email']
      if email.present? && EMAIL_REGEX.match(email)
        contact = employee.contacts.where(contact_type: Contact::ContactType::EMAIL, contact_for: Contact::ContactFor::PERSONAL ).first_or_initialize
        contact.value = email
        contact.save!
      else
        employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL).save!
      end

      office = Office.where(name: row['Command']).first_or_create if row['Command'].present?
      employee_office = employee.employee_offices.new
      employee_office.office_id = office&.id
      employee_office.save!

    else
      @errors[@row_number] = employee.errors.full_messages
    end

  rescue => e
    p @row_number, e.message
    papba_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_addtional_member_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end

task :papba_import_njspba_enrollment, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  # Here we are updating the records which is already present, to staff_member TRUE
  Employee.where("member_since is NOT NULL").update_all(staff_member: true)

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  csv_file.each_with_index do |row, i|

    if row['Emp.']&.present?
      @row_number = row['Emp.']
    elsif row['Name']&.present?
      @row_number = row['Name']
    elsif row['Name']&.blank? && row['Emp.']&.blank?
      papba_feed_errors("Mandotary details aren't present")
      next
    end

    a_number = row['Emp.']
    if a_number.present?
      employee = Employee.where(a_number: a_number)
    else
      first_name = row["Name"].split(',').last
      last_name = row['Name'].split(',').first
      employee = Employee.where(first_name: first_name, last_name: last_name)
    end

    if employee&.blank?
      papba_feed_errors("Member Details not found")
      next
    elsif employee.count > 1
      papba_feed_errors("More Than One employee found")
      next
    elsif employee.present? && employee.count == 1
      employee.first.update(staff_member: true)
    end

  rescue => e
    p @row_number, e.message
    papba_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_njspba_enrollment_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end

task :papba_import_retirement_unknown, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  status = ['Promoted', 'Sergeant', 'Lieutenant', 'Detective']
  matched_members = []
  @errors = {}
  csv_file.each do |row|
    if row['Name']&.present?
      @row_number = row['Name']
      first_name = row['Name'].split(' ').first
      last_name = row['Name'].split(' ').last
      employee = Employee.where(first_name: first_name, last_name: last_name).first
      if employee.nil?
        papba_feed_errors("Member Details not found")
        next
      elsif employee.notes.present? && status.any? { |word| employee.notes.downcase.include?(word.downcase) }
        matched_members << [employee.first_name, employee.middle_name, employee.last_name]
      end
    end
  rescue => e
    p @row_number, e.message
    papba_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_matched_members.csv", 'w') do |csv|
    csv << ["First Name", "Middle Name", "Last Name"]
    matched_members.each do |member|
      csv << member
    end
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_import_retirement_unknown_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]
    @errors.each do |error|
      csv << error
    end
  end
end


task :papba_do_not_mail_import, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  @errors = {}
  csv_file.each do |row|
    if row['Name']&.present?
      @row_number = row['Name']
      first_name = row['Name'].split(' ').first
      last_name = row['Name'].split(' ').last
      employee = Employee.where(first_name: first_name, last_name: last_name)
      if employee&.blank?
        papba_feed_errors('Member Details not found')
        next
      elsif employee.count > 1
        papba_feed_errors('More Than One employee found')
        next
      else
        employee = employee.first
        if employee.do_not_mail != true
          employee.update_attribute(:do_not_mail, "#{true}")
        end
      end
    end
  rescue StandardError => e
    p @row_number, e.message
    papba_feed_errors(e.message)
  end
  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_do_not_mail_import_errors.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']
    @errors.each do |error|
      csv << error
    end
  end
end

task :papba_import_member_status_retired_promoted, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  employment_status = EmploymentStatus.find_or_create_by(name: 'Retired Promoted')
  retired = EmploymentStatus.find_or_create_by(name: 'Retired')
  date = '26/09/2023'
  @errors = {}
  csv_file.each do |row|
    if row['Name']&.present?
      @row_number = row['Name']
      first_name = row['Name'].split(' ').first
      last_name = row['Name'].split(' ').last
      employee = Employee.where(first_name: first_name, last_name: last_name)
      if employee&.blank?
        papba_feed_errors('Member Details not found')
        next
      elsif employee.count > 1
        papba_feed_errors('More Than One employee found')
        next
      else
        employee = employee.first
        employee_employment_status = employee.employee_employment_statuses.first_or_create(employment_status_id: retired&.id, start_date: papba_parse_date(date, 'member status date'))
        employee_employment_status.update!(employment_status_id: employment_status&.id, start_date: papba_parse_date(date, 'member status date')) if employee_employment_status.present?
      end
    end

  rescue => e
    p @row_number, e.message
    papba_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_member_status_retired_promoted_errors.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']

    @errors.each do |error|
      csv << error
    end
  end
end

task :papba_import_new_members, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  csv_file.each do |row |

    @row_number = row['Emp. ID#']
    if (row['First Name'].blank? && row['Last Name'].blank?) || row['First Name'].blank? || row['Last Name'].blank?
      @row_number = row['Emp. ID#']
      papba_feed_errors("Mandatory details aren't present")
      next
    end
    @current_account = Account.find_by(subdomain: Apartment::Tenant.current)

    next if Employee.kept.where(first_name: row['First Name'], last_name: row['Last Name']).first.present?

    employee = Employee.new
    employee.a_number = row['Emp. ID#'] || ''
    employee.last_name = row['Last Name'] || ''
    employee.first_name = row['First Name'] || ''
    address = row['Address']&.split(",")
    if address.present?
      position = address.first.rindex(/\s/)
      street = address.first.slice(0, position)
      city = address.first.slice(position, address.first.length)
      state = address.last.split.first
      zip = address.last.split.last
    end

    employee.street = street || ''
    employee.city = city || ''
    employee.state = state || ''
    employee.zipcode = zip || ''
    employee.start_date = papba_parse_date(row['Employment date'], 'Start Date')
    employee.member_start_date = papba_parse_date(row['Graduation Date'], "Graduation Date")
    employee.previous_shield_number = row['Class'] || ''

    if employee.save!
      phone_number = row['Phone Number'].gsub("(", "").gsub(")", "").gsub(" ", "").gsub('-', "") if row['Phone Number'].present?
      phone_number = phone_number&.gsub(/(\d{3})(\d{3})(\d{4})/, '(\1) \2 - \3') || ""

      employee.contacts.new(contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE).save!
      employee.contacts.new(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE).save!
      if phone_number.present?
        employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE, value: phone_number).save!
      else
        employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE).save!
      end

      employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL).save!
      employee.contacts.new(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL).save!
      employee.contacts.new(contact_for: Contact::ContactFor::COLLEAGUE, contact_type: Contact::ContactType::EMAIL).save!

      status = row['Member Status']
      employment_status = EmploymentStatus.where("lower(name) = ?", status.downcase).first_or_create
      EmploymentStatus.new(name: status).save! if employment_status.blank?
      employee.employee_employment_statuses.new(employment_status_id: employment_status&.id, employee_id: employee.id, start_date: papba_parse_date(row['Member Status Start Date'], 'Member Status Start date')).save!
      employee_rank = row['Rank']
      rank = Rank.where("lower(name) = ?", employee_rank.downcase).first
      Rank.new(name: employee_rank).save! if rank.blank?
      employee.employee_ranks.new(rank_id: rank.id, employee_id: employee.id, start_date: papba_parse_date(row['Employment date'], 'Employment Date')).save!
    else
      @errors[@row_number] = employee.errors.full_messages
    end

  rescue => e
    p @row_number, e.message
    papba_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_papba_import_new_members_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end

task :papba_member_rank_import, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  status = 'Police Officer'
  ranks = Rank.where('lower(name) = ?', status.downcase).first
  unless ranks
    ranks = Rank.create!(name: status)
  end
  csv_file.each do |row|
    @row_number = row['PA-Emp-No']
    if (row['First'].blank? && row['Last'].blank?) || row['First'].blank? || row['Last'].blank?
      @row_number = row['PA-Emp-No']
      papba_feed_errors("Mandatory details aren't present")
      next
    end
    employees = Employee.where('a_number = ? and previous_shield_number = ?', row['PA-Emp-No'], '122')
    if employees.blank?
      papba_feed_errors('Member not found')
      next
    elsif employees.count > 1
      papba_feed_errors('More than one Member found')
      next
    end
    employees = employees.first
    employees.employee_ranks.new(rank_id: ranks.id, start_date: papba_parse_date('7/1/24', 'Ranks Start Date')).save!

  rescue => e
    p @row_number, e.message
    papba_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_papba_member_rank_import_errors.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']

    @errors.each do |error|
      csv << error
    end
  end
end

def papba_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end

def papba_parse_date(date, type)
  return nil unless date.present?

  parsed_date = nil

  begin
    date_array = date.split('/')
    day = date_array[0].to_i
    month = date_array[1].to_i
    year = date_array[2].to_i

    if year.to_s.length == 2
      if year > 25 && year <= 99
        year = ('19' + year.to_s).to_i
      else
        year = ('20' + year.to_s).to_i
      end
    elsif year.to_s.length == 1
      year = ('200' + year.to_s).to_i
    elsif year.to_s.length > 4
      raise 'Invalid Year'
    else
      year = year.to_i
    end

    parsed_date = Date.new(year, month, day)
  rescue
    parsed_date = Date.parse(date)
  end

  parsed_date

rescue => e
  papba_feed_errors('DATE - ' + type + " #{date} " + e.message)
end

def format_phone_number(phone_number)
  return '' unless phone_number.present? && phone_number != 'N/A'

  formatted_number = phone_number.gsub('(', '').gsub(')', '').gsub(' ', '').gsub('-', '').gsub('.', '')
  formatted_number.gsub(/(\d{3})(\d{3})(\d{4})/, '(\1) \2 - \3')
end

def create_contacts(employee, contact_for, contact_type, value, additional_attributes = {})
    contact_attributes = {
      contact_for: contact_for,
      contact_type: contact_type,
      value: value || '',
      contact_name: additional_attributes.fetch(:contact_name, ''),
      contact_relationship: additional_attributes.fetch(:contact_relationship, '')
    }
  employee.contacts.new(contact_attributes).save!
end

def full_name(first_name, middle_name, last_name, suffix)
  [first_name, middle_name, last_name, suffix].reject { |x| x.blank? }.join(' ')
end

# bundle exec rake 'papba_import_members[papba,Members.csv]'
# bundle exec rake 'papba_import_member_contact_information[papba,ContactInfo.csv]'
# bundle exec rake 'papba_import_member_rank[papba,Rank.csv]'
# bundle exec rake 'papba_import_member_command[papba,Location.csv]'
# bundle exec rake 'papba_import_beneficiaries[papba,Beneficiary.csv]'
# bundle exec rake 'papba_import_benefit_coverages[papba, Dependent.csv]'
# bundle exec rake 'papba_import_additional_members[papba, AdditionalMembers.csv]'
# # bundle exec rake 'papba_import_njspba_enrollment[papba, njspba_enrollment.csv]'
