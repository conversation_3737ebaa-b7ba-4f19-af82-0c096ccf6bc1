task :papba_annual_dues_import, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  csv_file.each do |row|

    @row_number = row['PA-Emp-No']
    a_number = @row_number
    next if @row_number.blank?

    employee = Employee.where(a_number: a_number)
    if employee.blank?
      papba_feed_errors('Employee Not found')
      next
    elsif employee.count > 1
      papba_feed_errors('More than One Employee Found')
      next
    end

    pacf_name = row['TransactionType']
    if pacf_name.blank?
      papba_feed_errors("TransactionType is blank")
      next
    end

    employee = employee.first
    pacf_id = (Pacf.where(name: pacf_name).first_or_create!).id
    employee_pacf = employee.employee_pacfs.new
    employee_pacf.date = Date.parse(row['TransactionDate']) if row['TransactionDate'].present?
    employee_pacf.notes = row["TransactionDescription"]
    employee_pacf.pacf_id = pacf_id
    employee_pacf.amount = row['Amount'].blank? || row['Amount'] == '0.00' ? nil : row['Amount'].to_f.round(2)
    employee_pacf.save!

  rescue => e
    p @row_number, e.message
    papba_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_annual_dues_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]
    @errors.each do |error|
      csv << error
    end
  end
end

task :papba_annual_dues_import_2023, %i[account file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  pacf_name = 'Deposit'
  pacf_id = Pacf.where('lower(name) = ?', pacf_name.downcase).first.id
  search_columns = Account.find_by(subdomain: 'papba').saas_json.dig('schema', 'employees', 'search_columns')
  @same_model = search_columns&.dig('same_model') || []
  @associated_model = search_columns&.dig('associated_model')&.symbolize_keys || {}

  csv_file.each do |row|
    name = row['Member Name'] || ''
    next if name.blank?

    previous_shield_number = row['Class'] || ''

    @row_number = name

    employees = employees_by_name(name, previous_shield_number)
    suffix = suffix_present?(name) if employees.blank?
    employees = employees_by_suffix(name) if suffix

    if employees.count > 1
      papba_feed_errors('More than One Employee found')
      next
    elsif employees.blank?
      papba_feed_errors('Invalid Employee or Employee was missing')
      next
    end

    create_employee_annual_due(employees, pacf_id, previous_shield_number)

  rescue StandardError => e
    p @row_number, e.message
    papba_feed_errors(e.message)
  end

  generate_error_report_csv(args[:account], args[:file_path])
end

task :papba_updated_annual_dues_import_2023, %i[account file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  pacf_name = 'Deposit'
  pacf_id = Pacf.kept.where('lower(name) = ?', pacf_name.downcase).first.id
  search_columns = Account.find_by(subdomain: 'papba').saas_json.dig('schema', 'employees', 'search_columns')
  @same_model = search_columns&.dig('same_model') || []
  @associated_model = search_columns&.dig('associated_model')&.symbolize_keys || {}

  csv_file.each_with_index do |row, index|
    next if index < 1517

    previous_shield_number = row['Class'] || ''
    name = row['Member Name'] || ''
    next if previous_shield_number.present? || name.blank?

    @row_number = name

    employees = employees_by_name(name)
    suffix = suffix_present?(name) if employees.blank?
    employees = employees_by_suffix(name) if suffix

    update_annual_due(employees, pacf_id)

  rescue StandardError => e
    p @row_number, e.message
    papba_feed_errors(e.message)
  end

  generate_error_report_csv(args[:account], args[:file_path])
end

task :papba_annual_dues_import_2024, %i[account pacf_name date class] => :environment do |t, args|
  Apartment::Tenant.switch!(args[:account])

  @errors = {}
  pacf_id = Pacf.where('lower(name) = ?', args[:pacf_name].downcase).first.id
  date = Date.parse(args[:date])
  active_status_id = EmploymentStatus.kept.where('lower(name) = ?', 'active').first.id
  active_employees = Employee.kept.includes(:employee_employment_statuses)
                             .where('employee_employment_statuses.employment_status_id = ? AND (employee_employment_statuses.end_date IS NULL OR employee_employment_statuses.end_date >= ?)', active_status_id, Date.today)
                             .references(:employee_employment_statuses)
  class_122_employee_ids = active_employees.where(previous_shield_number: args[:class]).pluck(:id)

  ActiveRecord::Base.transaction do
    active_employees.find_each do |employee|
      @row_number = employee.name
      amount = class_122_employee_ids.include?(employee.id) ? 802.38 : 869.24
      employee.employee_pacfs.create!(pacf_id: pacf_id, date: date, notes: '2024 ANNUAL DUES', amount: amount)
    end
  rescue StandardError => e
    p @row_number, e.message
    papba_feed_errors(e.message)
  end

  generate_error_report_csv(t, 'errors')
end

def suffix_present?(name)
  name_word_count = name.split.count
  last_word = name.split.last
  suffix = %w[Mr Mr. Ms Ms. Jr Jr.]
  return false unless [3, 4].include?(name_word_count) && suffix.include?(last_word)

  true
end

def employees_by_suffix(name)
  name_array = name.split
  name_word_count = name_array.count

  case name_word_count
  when 3
    employees = Employee.kept.where(first_name: name_array.first, last_name: name_array.second, suffix: name_array.last)
  when 4
    employees = Employee.kept.where(first_name: name_array.first, middle_name: name_array.second, last_name: name_array.third, suffix: name_array.last)
  end
  employees
end

def employees_by_name(name)
  name_array = name.split
  f_name = name_array.first
  l_name = name_array.last
  m_name = name_array.second if name_array.count > 2

  employees = if m_name.present?
                Employee.kept.where(first_name: f_name, middle_name: m_name, last_name: l_name)
              else
                Employee.kept.where(first_name: f_name, last_name: l_name)
              end

  return employees unless employees.count > 1 || employees.blank?

  employees = Employee.kept.search_by_employee(@same_model, @associated_model, name)
  employees.first&.previous_shield_number.blank? ? [employees.first] : []
end

def create_employee_annual_due(employees, pacf_id, previous_shield_number)
  amount = amount_based_on_class(previous_shield_number)
  return unless amount

  employee = employees.first
  date = Date.parse('31-12-2023')
  EmployeePacf.create!(employee_id: employee.id, pacf_id: pacf_id, date: date, notes: '2023 ANNUAL DUES', amount: amount)
end

def amount_based_on_class(previous_shield_number)
  pre_shield_number = previous_shield_number.to_i

  case pre_shield_number
  when 107..119
    849.09
  when 120
    601.78
  when 121
    267.45
  when 122
    0
  else
    papba_feed_errors('Updated with Amount 0$ as Class field is missing')
    0
  end
end

def update_annual_due(employees, pacf_id)
  if employees.count > 1
    papba_feed_errors('More than One Employee found')
    return
  elsif employees.blank?
    papba_feed_errors('Invalid Employee or Employee was missing')
    return
  end

  employee = employees.first

  ActiveRecord::Base.transaction do
    old_annual_due = employee.employee_pacfs.kept.where(pacf_id: pacf_id, notes: '2023 ANNUAL DUES', amount: 0).first
    if old_annual_due.blank?
      papba_feed_errors('2023 ANNUAL DUE record not found for the employee')
    else
      old_annual_due.update_columns(amount: 849.09)
    end
  end
end

def generate_error_report_csv(account, file_name)
  CSV.open("#{Rails.root}/#{account}_#{Date.today}_#{file_name}", 'w') do |csv|
    csv << ['Row Number', 'Errors']

    @errors.each do |error|
      csv << error
    end
  end
end

def papba_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end

# bundle exec rake 'papba_annual_dues_import_2023[papba, FILENAME]'
# bundle exec rake 'papba_annual_dues_import_2024[papba, Deposit, 31-12-2024, 122]'
