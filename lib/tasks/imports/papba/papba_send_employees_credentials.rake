require 'csv'

desc 'import data'
task :papba_member_user_credential_mailer_to_active_and_life_members, [:account] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])
  employees = Employee.includes(:employee_employment_statuses).where('employee_employment_statuses.employment_status_id in (?) and (employee_employment_statuses.end_date is NULL or employee_employment_statuses.end_date > ?)', EmploymentStatus.where("lower(name) in (?)", ['active', 'life member']).pluck(:id), Date.today).references(:employee_employment_statuses)
  @mail_sented_members = []
  @errors = {}
  employees.each do |employee|
    @row_number = employee.full_name
    next if employee.employee_positions.present?

    personal_email = employee.contacts.where(contact_type: 'email', contact_for: 'personal').first
    if personal_email&.value.blank?
      papba_feed_errors('Mail is blank')
      next
    elsif employee.email_opt_out == true
      papba_feed_errors('Email Opt Out box has been checked')
      next
    elsif employee.enable_mobile_access == false
      papba_feed_errors('Mobile Access is not enabled for this Member')
      next
    end

    password = Devise.friendly_token(8)
    username = (employee.username.present? ? employee.username : personal_email.value)
    employee.assign_attributes(username: username, password: password, password_confirmation: password)
    employee.save!(validate: false)
    if MemberCredentialsMailer.details(username, password, personal_email.value, 'papba').deliver_later
      @mail_sented_members << employee.full_name
    end

  rescue => e
    p @row_number, e.message
    papba_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_papba_member_user_credential_mailer_to_active_and_life_member_errors.csv", 'w') do |csv|
    csv << ['Errors']

    @errors.each do |error|
      csv << error
    end
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_mail_succeed_members.csv", 'w') do |csv|
    csv << ['Member Full Name']

    @mail_sented_members.each do |name|
      csv << [name]
    end
  end
end


task :papba_import_new_member, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  @errors = {}
  member_status = papba_create_model_data('EmploymentStatus', 'Active')
  start_date = Date.strptime("06/20/2025", '%m/%d/%Y')

  csv_file.each do |row|
    last_name = row['Last name']&.strip
    first_name = row['First name']&.strip
    # Uncomment this line when you want to import employee number
    # employee_number = row['Employee #']
    @row_number = [first_name, last_name]
    address = row['Address'] || ''
    city = row['City'] || ''
    state = row['State'] || ''
    home_phone_number = parse_phone(row['Home phone number']) || ''
    cell_phone_number = parse_phone(row['Cell phone number']) || ''
    gender = papba_create_model_data('Gender', row['Gender'])
    date_of_birth = row['Date of birth'].present? ? papba_parse_date(row['Date of birth'], 'Date of birth') : ''
    military_branch = row['Military Branch'] || ''
    emergency_contact_name = row['Emergency contact name'] || ''
    emergency_contact_phone_number = parse_phone(row['Emergency contact phone number']) || ''
    relationship_to_emergency_contact = row['Relationship to emergency contact'] || ''
    marital_status = papba_create_model_data('MaritalStatus', row['Marital status'])

    if first_name.blank? || last_name.blank?
      papba_feed_errors('Mandatory details not present')
      next
    end

    employees = Employee.kept.where('lower(first_name) = ? AND lower(last_name) = ?', first_name.downcase, last_name.downcase)
    employees = employees.kept.where('birthday = ?', date_of_birth) if employees.present? && employees.count > 1
    if employees.present? && employees.count == 1
      employee = employees.first
    else
      employee = Employee.new
    end
    employee.first_name = first_name
    employee.last_name = last_name
    employee.street = address
    employee.city = city
    # Uncomment this line when you want to import employee number
    # employee.a_number = employee_number
    employee.state = state
    employee.gender_id = gender.id if gender
    employee.birthday = date_of_birth
    employee.marital_status_id = marital_status.id if marital_status
    employee.previous_shield_number = '123'
    employee.veteran_status = true if military_branch.present? && military_branch.downcase != 'none'

    if employee.new_record?
      employee.save!
      create_contacts(employee)
    else
      employee.save!
    end

    employment_status = employee.employee_employment_statuses.where('end_date IS NULL AND employment_status_id = ?', member_status&.id).first
    if employment_status.present?
      employment_status.start_date = start_date
      employment_status.save!
    else
      employee.employee_employment_statuses.create!(employment_status_id: member_status&.id, start_date: start_date)
    end


    if home_phone_number.present? && validate_phone_number(home_phone_number, 'Invalid Home Phone Number')
      contact = employee.contacts.find_or_initialize_by(contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE)
      contact.value = home_phone_number
      contact.save!
    end

    if cell_phone_number.present? && validate_phone_number(cell_phone_number, 'Invalid Cell Phone Number')
      contact = employee.contacts.find_or_initialize_by(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE)
      contact.value = cell_phone_number
      contact.save!
    end

    if emergency_contact_phone_number.present? && validate_phone_number(emergency_contact_phone_number, 'Invalid Emergency Contact Phone Number')
      contact = employee.contacts.find_or_initialize_by(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMERGENCY)
      contact.value = emergency_contact_phone_number
      contact.contact_name = emergency_contact_name
      contact.contact_relationship = relationship_to_emergency_contact
      contact.save!
    end


  rescue => e
    p @row_number, e.message
    papba_feed_errors(e.message)
  end
  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_papba_import_new_member_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end
def create_contacts(employee)
  employee.contacts.create!(contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE, value: '')
  employee.contacts.create!(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE, value: '')
  employee.contacts.create!(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE, value: '')
  employee.contacts.create!(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL, value: '')
  employee.contacts.create!(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL, value: '')
  employee.contacts.create!(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMERGENCY, value: '')
  employee.contacts.create!(contact_for: Contact::ContactFor::COLLEAGUE, contact_type: Contact::ContactType::EMERGENCY, value: '')
end

def parse_phone(phone)
  return nil unless phone.present?

  phone.remove!('(', ')', ' ', '-')

  phone_number = '(' + phone.first(3) + ') ' + phone[3..5] + ' - ' + phone.last(4)

  phone_number
rescue StandardError => e
  papba_feed_errors('PHONE ' + e.message)
end

def papba_create_model_data(model_name, value)
  return nil unless value.present?

  model_name.constantize.kept.where('lower(name) = ?', value.strip.downcase).first_or_create!(name: value.strip)
rescue StandardError => e
  papba_feed_errors("#{model_name} " + e.message)
end

def papba_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end

def validate_phone_number(phone_number, error_message)
  return false if phone_number.nil? || phone_number.strip.empty?

  if phone_number.match(/\A\(\d{3}\)\s\d{3}\s-\s\d{4}\z/)
    true
  else
    papba_feed_errors(error_message)
    false
  end
end

def papba_parse_date(date, type)
  return nil unless date.present?

  parsed_date = nil

  begin
    date_array = date.split('/')
    day = date_array[1].to_i
    month = date_array[0].to_i
    year = date_array[2].to_i

    if year.to_s.length == 2
      if year > 20 && year <= 99
        year = ('19' + year.to_s).to_i
      else
        year = ('20' + year.to_s).to_i
      end
    elsif year.to_s.length == 1
      year = ('200' + year.to_s).to_i
    elsif year.to_s.length > 4
      raise 'Invalid Year'
    else
      year = year.to_i
    end

    parsed_date = Date.new(year, month, day)
  rescue
    parsed_date = Date.parse(date)
  end

  parsed_date

rescue => e
  papba_feed_errors('DATE - ' + type + " #{date} " + e.message)
end

# bundle exec rake 'papba_import_new_member[papba, papba_new_member_import.csv]'

