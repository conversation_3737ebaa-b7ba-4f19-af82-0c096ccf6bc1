# frozen_string_literal: true

require 'csv'
require 'spreadsheet'

desc 'Change the start date for all benefits'
task :cobanc_change_active_benefit_start_date, [] => :environment do |_t, _args|
  @errors = {}

  Apartment::Tenant.switch!('cobanc')
  members = Employee.includes(:employee_employment_statuses, :employee_benefits)
                    .where('employee_employment_statuses.employment_status_id in (?) and (employee_employment_statuses.end_date is null or employee_employment_statuses.end_date >= ?)',
                           EmploymentStatus.where('lower(name) in (?)', ['retired', 'active', 'out of state retiree']).ids, Date.today)
                    .references(:employee_employment_statuses, :employee_benefits)
  members.each do |member|
    employee_benefits = member.employee_benefits

    employee_benefits.where('start_date is null').update_all(start_date: Date.parse('2023-07-01')) if employee_benefits.present?

  rescue StandardError => e
    p @row_number, e.message
    cobanc_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/COBANC_#{Date.today}_change_active_benefit_start_date.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']

    @errors.each do |error|
      csv << error
    end
  end
end

task :cobanc_united_healthcare_id_import, %i[account file_path] => :environment do |t, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  csv_file.each do |row|
    # relationship = row['Rel (M=Subscriber)'] || ''
    # next if relationship != 'M'

    uhc_id = row['MembersFundId'] || ''
    first_name = row['MemberFirstName'] || ''
    last_name = row['MemberLastName'] || ''
    ssn = parse_ssn(row['SS']&.remove("'")) || ''

    if uhc_id.blank?
      cobanc_feed_errors('Aso ID Missing')
      next
    end

    # middle_name = row['Middle Initial'] || ''
    birthday = row['Birthdate'].present? ? Date.strptime(row['Birthdate'], '%m/%d/%Y') : ''

    @row_number = "#{first_name} #{last_name}"
    employees = Employee.kept.where(social_security_number: ssn) if ssn.present?
    employees = Employee.kept.where('lower(first_name) = ? and lower(last_name) = ?', first_name.downcase, last_name.downcase) if employees.blank?
    employees = employees.where('birthday = ?', birthday) if employees.count > 1 && birthday.present?
    valid_employee = check_employees_count(employees)
    next unless valid_employee

    ActiveRecord::Base.transaction do
      valid_employee.update_columns(previous_shield_number: uhc_id)
    rescue StandardError => e
      p @row_number, e.message
      cobanc_feed_errors(e.message)
    end
  end
  generate_csv_report_errors(t.name)
end

task :generate_cobanc_member_report, %i[account statuses] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  status_names = args[:statuses]&.split('|') || []
  benefit_id = Benefit.kept.where('lower(name) = ?', 'dental').first.id
  employees = Employee.kept.includes(:gender, :employee_employment_statuses, employee_benefits: :benefit_coverages).where('employee_benefits.benefit_id = ? ', benefit_id).references(:employee_benefits)
  if status_names.present?
    emp_status_ids = EmploymentStatus.kept.where('lower(name) IN (?)', status_names).ids
    employees = employees.where('employee_employment_statuses.employment_status_id IN (?)', emp_status_ids).references(:employee_employment_statuses)
  end
  spreadsheet = Spreadsheet::Workbook.new
  xls_file = spreadsheet.create_worksheet(name: 'Member Report')

  xls_file.row(0).concat [
    'Member SS No', 'Member First Name', 'Member MI', 'Member Last Name', 'Member Date of Birth', 'Member Gender', 'Address', 'City', 'St', 'Zip Code',
    'Eligibility Start Date', 'Eligibility End Date', 'Eligibility Type', 'Start Date of Employment Status', 'End date of Employment Status', 'Spouse First Name',
    'Spouse Last Name', 'Spouse Date of Birth', 'Dependent First Name', 'Dependent Last Name', 'Dependent Date of Birth', 'Dependent Relationship'
  ]
  row_number = 1

  employees.find_each do |employee|
    employee_data = [
      employee.social_security_number || '', employee.first_name, employee.middle_name || '', employee.last_name,
      employee.birthday&.strftime('%d-%m-%Y') || '', employee.gender&.name || '', employee.street || '',
      employee.city || '', employee.state || '', employee.zipcode || ''
    ]
    employee_benefits = employee.employee_benefits.where('benefit_id = ?', benefit_id)

    employment_statuses_with_dates = employee.employee_employment_statuses.kept.select { |status| status.start_date || status.end_date }
    employment_statuses_without_dates = employee.employee_employment_statuses.kept.reject { |status| status.start_date || status.end_date }

    grouped_employment_status = employment_statuses_without_dates.map { |status| status.employment_status&.name }.compact.join(', ')

    spouse_coverages = []
    dependent_coverages = []

    coverages = employee.benefit_coverages.where(employee_benefit_id: employee_benefits).group_by { |record| [record.first_name, record.last_name, record.birthday, record.relationship] }

    coverages.each do |coverage, _grouped_coverage|
      if coverage[-1] == 'spouse'
        spouse_coverages << coverage
      else
        dependent_coverages << coverage
      end
    end

    max_rows = [
      employment_statuses_with_dates.size + (employment_statuses_without_dates.present? ? 1 : 0),
      employee_benefits.size, spouse_coverages.size, dependent_coverages.size
    ].max

    max_rows.times do |i|
      employment_status_data = if i.zero? && grouped_employment_status.present?
                                 [grouped_employment_status] + Array.new(2)
                               elsif (employment_status =  employment_statuses_with_dates[i - (grouped_employment_status.present? ? 1 : 0)])
                                 [employment_status.employment_status&.name || '', employment_status.start_date&.strftime('%d-%m-%Y') || '', employment_status.end_date&.strftime('%d-%m-%Y') || '']
                               else
                                 Array.new(3)
                               end

      benefit_data = if (benefit = employee_benefits[i])
                       [benefit.start_date&.strftime('%d-%m-%Y') || '', benefit.end_date&.strftime('%d-%m-%Y') || '']
                     else
                       Array.new(2)
                     end

      spouse_data = if (spouse_coverage = spouse_coverages[i])
                      [spouse_coverage[0] || '', spouse_coverage[1] || '', spouse_coverage[2]&.strftime('%d-%m-%Y') || '']
                    else
                      Array.new(3)
                    end

      dependent_data = if (dependent_coverage = dependent_coverages[i])
                         [dependent_coverage[0] || '', dependent_coverage[1] || '', dependent_coverage[2]&.strftime('%d-%m-%Y') || '', dependent_coverage[3] || '']
                       else
                         Array.new(3)
                       end

      xls_file.row(row_number).concat(employee_data + benefit_data + employment_status_data + spouse_data + dependent_data)
      row_number += 1
      employee_data = Array.new(10)
    end
  end

  file_path = Rails.root.join("#{args[:account]}_aso_report_#{Time.now.strftime('%d-%m-%Y-%H:%M:%S')}.xls")
  spreadsheet.write file_path
end

def cobanc_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end

def check_employees_count(employees)
  if employees.count > 1
    cobanc_feed_errors('More than One Employee found')
    false
  elsif employees.blank?
    cobanc_feed_errors('Invalid Employee or Employee was missing')
    false
  else
    employees.first
  end
end

def generate_csv_report_errors(name)
  CSV.open("#{Rails.root}/#{name}#{Time.new.strftime('%d-%m-%Y-%H:%M')}.csv", 'w') do |csv|
    csv << ['Employee Name', 'Errors']

    @errors.each do |error|
      csv << error
    end
  end
end

# bundle exec rake 'cobanc_united_healthcare_id_import[cobanc, COBANC_roster_WITH_UHG_ID_Aug20_2024 (1).csv]'
# bundle exec rake 'generate_cobanc_member_report[cobanc, retired|out of state retiree]'
