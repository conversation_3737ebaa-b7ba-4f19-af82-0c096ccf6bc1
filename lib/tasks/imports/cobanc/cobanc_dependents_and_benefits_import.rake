require 'csv'

desc 'import data'
task :cobanc_active_dependents_and_benfits_import, [:file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!('cobanc')
  ["Active Death Benefit", "COBRA", "Dental", "Hearing Aide", "Heartscan", "Hospital Income", "Inner Imaging", "Laser Correction", "Maternity/Adoption", "Optical", "Orthodontic Expense", "Part-time Buy Up", "Prescription Medical Copay Reimbursement", "Supplemental Workers Compensation", "Retiree Death Benefit"].each do |benefit|
    benefits = Benefit.where("name = ?", benefit)
    if benefits.blank?
      Benefit.create(name: benefit)
    end
  end
  employee_ids = []
  @active_benefits = ["Active Death Benefit", "COBRA", "Dental", "Hearing Aide", "Heartscan", "Hospital Income", "Inner Imaging", "Laser Correction", "Maternity/Adoption", "Optical", "Orthodontic Expense", "Part-time Buy Up", "Prescription Medical Copay Reimbursement", "Supplemental Workers Compensation"]
  @active_employees = Employee.includes(:employee_employment_statuses, :employment_statuses).where("employee_employment_statuses.employment_status_id in  (?) and (employee_employment_statuses.end_date is NULL or employee_employment_statuses.end_date > ?)", EmploymentStatus.where("lower(name) in (?)", ["active"]).pluck(:id), Date.today).references(:employee_employment_statuses, :employment_statuses)
  @active_employees.each do |employee|
    employee_benefits_create(@active_benefits, employee)
  end

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  csv_file.each do |row|
    if row['Dep'] == "0"
      employee = get_employee_and_row_number(row)
      next if employee.blank? || employee.count > 1
      employee_ids << employee.first.id
      employee_benefits_create(@active_benefits, employee.first)
      next
    else
      employee = get_employee_and_row_number(row)
      next if employee.blank? || employee.count > 1
      add_benefit_coverages_for_employee(employee.first, row, @active_benefits)
    end

  rescue => e
    p @row_number, e.message
    cobanc_feed_errors(e.message)
  end

  @active_employees_not_in_file = @active_employees&.pluck(:id)&.uniq - employee_ids.uniq
  file_name = args[:file_path].split('.').first
  CSV.open("#{Rails.root}/COBANC_#{Date.today}_#{file_name}_active_benefits_and_dependents_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end

    csv << ["", ""]
    csv << ["Active Employees Not in File", ""]
    csv << ["HealthPlexID", "Name"]
    @active_employees_not_in_file.each do |id|
      next if employee_ids.uniq.include?(id)
      employee = Employee.find(id)
      csv << [employee.a_number, employee.full_name]
    end
  end
end

task :cobanc_part_time_members_dependents_and_benefits_import, [:file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!('cobanc')
  employee_ids = []
  @part_time_benefits = ["Active Death Benefit", "COBRA", "Dental", "Hearing Aide", "Heartscan", "Hospital Income", "Inner Imaging", "Laser Correction", "Maternity/Adoption", "Optical", "Orthodontic Expense", "Part-time Buy Up", "Prescription Medical Copay Reimbursement", "Supplemental Workers Compensation"]
  @part_time_employees = Employee.includes(:employee_employment_statuses, :employment_statuses).where("employee_employment_statuses.employment_status_id in  (?) and (employee_employment_statuses.end_date is NULL or employee_employment_statuses.end_date > ?)", EmploymentStatus.where("lower(name) in (?)", ["active part-time"]).pluck(:id), Date.today).references(:employee_employment_statuses, :employment_statuses)
  @part_time_employees.each do |employee|
    employee_benefits_create(@part_time_benefits, employee)
  end

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  csv_file.each do |row|
    if row['Dep'] == "0"
      employee = get_employee_and_row_number(row)
      next if employee.blank? || employee.count > 1
      employee_ids << employee.first.id
      employee_benefits_create(@part_time_benefits, employee.first)
      next
    else
      employee = get_employee_and_row_number(row)
      next if employee.blank? || employee.count > 1
      add_benefit_coverages_for_employee(employee.first, row, @part_time_benefits)
    end

  rescue => e
    p @row_number, e.message
    cobanc_feed_errors(e.message)
  end

  @part_time_employees_not_in_file = @part_time_employees&.pluck(:id)&.uniq - employee_ids.uniq

  file_name = args[:file_path].split('.').first
  CSV.open("#{Rails.root}/COBANC_#{Time.now.to_i}_#{file_name}_part_time_members_benefits_and_dependents_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end

    csv << ["", ""]
    csv << ["Part Time Employees Not in File", ""]
    csv << ["HealthPlexID", "Name"]
    @part_time_employees_not_in_file.each do |id|
      next if employee_ids.uniq.include?(id)
      employee = Employee.find(id)
      csv << [employee.a_number, employee.full_name]
    end
  end
end

task :cobanc_retired_dependents_and_benefits_import, [:file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!('cobanc')
  @retired_benefits = ["COBRA", "Dental", "Hearing Aide", "Optical", "Orthodontic Expense", "Prescription Medical Copay Reimbursement", "Retiree Death Benefit"]
  @retired_employees = Employee.includes(:employee_employment_statuses, :employment_statuses).where("employee_employment_statuses.employment_status_id in  (?) and (employee_employment_statuses.end_date is NULL or employee_employment_statuses.end_date > ?)", EmploymentStatus.where("lower(name) in (?)", "retired").pluck(:id), Date.today).references(:employee_employment_statuses, :employment_statuses)
  @retired_employees.each do |employee|
    employee_benefits_create(@retired_benefits, employee)
  end
  employee_ids = []

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  csv_file.each do |row|
    if row['Dep'] == "0"
      employee = get_employee_and_row_number(row)
      next if employee.blank? || employee.count > 1
      employee_ids << employee.first.id
      employee_benefits_create(@retired_benefits, employee.first)
      next
    else
      employee = get_employee_and_row_number(row)
      next if employee.blank? || employee.count > 1
      add_benefit_coverages_for_employee(employee.first, row, @retired_benefits)
    end

  rescue => e
    p @row_number, e.message
    cobanc_feed_errors(e.message)
  end
  @retired_employees_not_in_file = @retired_employees&.pluck(:id)&.uniq - employee_ids.uniq
  file_name = args[:file_path].split('.').first
  CSV.open("#{Rails.root}/COBANC_#{Date.today}_#{file_name}_retired_benefits_and_dependents_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end

    csv << ["", ""]
    csv << ["Retired Employees Not in File", ""]
    csv << ["HealthPlexID", "Name"]
    @retired_employees_not_in_file.each do |id|
      next if employee_ids.uniq.include?(id)
      employee = Employee.find(id)
      csv << [employee.a_number, employee.full_name]
    end
  end
end

task :cobanc_dependent_county_remove, [] => :environment do |_t, args|

  Apartment::Tenant.switch!("cobanc")
  county_list = ["AIKEN", "ALBEMARLE", "ATLANTIC", "BALDWIN", "BALTIMORE", "BEAUFORT", "BERGEN", "BLANCO", "BRONX", "BROWARD", "BRUNSWICK", "BUCKS", "CABARRUS", "CATAWBA", "CHARLOTTE", "CHATHAM", "CITRUS", "CLARK", "CLAY", "COBB", "COCONINO", "COLLIER", "DAUPHIN", "DAVIDSON", "DE KALB", "DEKALB", "DUPAGE", "DURHAM", "DUVAL", "ESSEX", "FAIRFIELD", "FLAGLER", "FRANKLIN", "GREENVILLE", "GUILFORD", "GWINNETT", "HAWKINS", "HENDERSON", "HERNANDO", "HIGHLANDS", "HILLSBOROUGH", "HORRY", "INDIAN RIVER", "KENT", "KINGS", "LEBANON", "LEE", "LEHIGH", "LOUDON", "MANATEE", "MARICOPA", "MARION", "MARTIN", "MIAMI-DADE", "MIDDLESEX", "MONMOUTH", "MONROE", "MONTGOMERY", "NASSAU", "NEW HANOVER", "NEW HAVEN", "NEW YORK", "NORFOLK", "NORFOLK CITY", "NORTHAMPTON", "NORTHUMBERLAND", "OCEAN", "ORANGE", "OTSEGO", "PAGE", "PALM BEACH", "PASCO", "PASSAIC", "PENOBSCOT", "PIMA", "PLATTE", "POLK", "PUTNAM", "QUEENS", "RICHLAND", "ROCKINGHAM", "ROWAN", "SAINT LUCIE", "SAN DIEGO", "SARASOTA", "SNYDER", "SPARTANBURG", "ST. JOHNS", "ST. LUCIE", "SUFFOLK", "SUSSEX", "ULSTER", "UNION", "VIRGINIA BEACH", "VOLUSIA", "WASHINGTON", "WAYNE", "WESTCHESTER", "WORCESTER", "YORK"]
  BenefitCoverage.select(:address).group_by(&:address).each do |benefit_coverage|
    coverage_address = benefit_coverage.first
    if coverage_address.present?
      address = coverage_address.dup
      address.gsub!(/#{county_list.join('|')}/i, "")
      BenefitCoverage.where(address: coverage_address).update_all(address: address)
    end
  end
end

task :cobanc_ssn_import, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  csv_file.each do |row|

    next unless row['RELATIONSHIP'] == 'EE'

    if row['MEMBER_SSN'].blank?
      cobanc_feed_errors('SSN # is blank')
      next
    end

    healthplex_number = row['EMPLOYEE_ID'] || ''
    @row_number = healthplex_number

    employees = Employee.kept.where(a_number: healthplex_number) if healthplex_number.present?

    if healthplex_number.blank? || employees.blank? || employees.count > 1
      first_name = row['FIRST_NAME'] || ''
      last_name = row['LAST_NAME'] || ''
      middle_name = row['MIDDLE_INITIAL'] || ''
      @row_number = "#{first_name} #{last_name}"
      employees = Employee.kept.where("lower(first_name) = ? and lower(last_name) = ?", first_name.downcase, last_name.downcase)
      if employees.blank?
        cobanc_feed_errors('Member not found')
        next
      elsif employees.count > 1
        employees = employees.where("lower(middle_name) = ?", middle_name.downcase)
        if employees.blank?
          cobanc_feed_errors('More than One employee found')
          next
        end
      end
    end

    employee = employees.first
    ssn = row['MEMBER_SSN'] || ''
    ssn_rjust = ssn.rjust(9, '0')
    employee.update_columns(social_security_number: parse_ssn(ssn_rjust))

  rescue => e
    p @row_number, e.message
    cobanc_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/cobanc_ssn_import_errors.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']
    @errors.each do |error|
      csv << error
    end
  end
end

def employee_benefits_create(benefits, employee)
  benefit_ids = Benefit.where(name: benefits).pluck(:id)
  employee_benefit_ids = employee.employee_benefits.pluck(:benefit_id)
  benefit_create_ids = []
  benefit_ids.each { |a| benefit_create_ids << a unless employee_benefit_ids.include?(a) }
  benefit_create_ids.each do |benefit_id|
    employee.employee_benefits.create(benefit_id: benefit_id)
  end
end

def get_employee_and_row_number(row)
  healthplex_num = row['Ssn']
  @row_number = if healthplex_num.present?
                  healthplex_num
                else
                  row['Fname'] + " " + row['Lname']
                end

  employee = Employee.where(a_number: healthplex_num)
  if @row_number.blank?
    cobanc_feed_errors('Mandatory Details not found')
  elsif employee.blank?
    employee = Employee.where(first_name: row['Fname'], last_name: row['Lname'])
    cobanc_feed_errors('No matches found') if employee.blank?
  elsif employee.present? && employee.count > 1
    cobanc_feed_errors('More than One Employee found')
  end
  employee
end

def add_benefit_coverages_for_employee(employee, row, benefits)
  if row['Dep'] == "1"
    ["Active Death Benefit", "COBRA", "Dental", "Hearing Aide", "Heartscan", "Hospital Income", "Laser Correction", "Maternity/Adoption", "Optical", "Orthodontic Expense", "Prescription Medical Copay Reimbursement", "Inner Imaging"].each do |benefit|
      next unless benefits.include?(benefit)
      if benefit == 'Inner Imaging'
        age = get_age(row['Birth'])
        next if age <= 45
      end
      benefit_id = Benefit.where("name = ?", benefit)&.first.id
      employee_benefit_id = employee.employee_benefits.where(benefit_id: benefit_id)&.first.id
      benefit_coverages = employee.benefit_coverages.where(employee_benefit_id: employee_benefit_id, first_name: row['Fname'], last_name: row['Lname'])
      if benefit_coverages.blank?
        benefit_coverage = employee.benefit_coverages.new(employee_benefit_id: employee_benefit_id)
        new_benefit_coverage(row, benefit_coverage)
      end
    end
  else
    ["Active Death Benefit", "COBRA", "Dental", "Hearing Aide", "Hospital Income", "Laser Correction", "Optical", "Orthodontic Expense", "Prescription Medical Copay Reimbursement", "Inner Imaging"].each do |benefit|
      next unless benefits.include?(benefit)
      if benefit == 'Inner Imaging'
        age = get_age(row['Birth'])
        next if age <= 45
      end
      benefit_id = Benefit.where("name = ?", benefit)&.first.id
      employee_benefit_id = employee.employee_benefits.where(benefit_id: benefit_id)&.first.id
      benefit_coverages = employee.benefit_coverages.where(employee_benefit_id: employee_benefit_id, first_name: row['Fname'], last_name: row['Lname'])
      if benefit_coverages.blank?
        benefit_coverage = employee.benefit_coverages.new(employee_benefit_id: employee_benefit_id)
        new_benefit_coverage(row, benefit_coverage)
      end
    end
  end
end

def new_benefit_coverage(row, benefit_coverage)
  benefit_coverage.first_name = row['Fname']
  benefit_coverage.last_name = row['Lname']
  benefit_coverage.dependent = row['Dep']
  benefit_coverage.birthday = row['Birth']
  benefit_coverage.relationship = (row['Dep'] == "1" ? 'spouse' : 'child')
  benefit_coverage.address = [row['Addr1'], row['Addr2'], row['City'], row['County'], row['State'], row['Zip']].join(" ")
  benefit_coverage.save!
end

def cobanc_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end

def get_age(birthday)
  return 0 if birthday.blank?
  birthday = Date.parse(birthday)
  age = Date.today.year - birthday.year
  age -= 1 if Date.today < birthday + age.year
  age
end

def parse_ssn(ssn)
  return nil unless ssn.present?

  ssn.remove!('-', ' ')

  ssn_number = ssn.first(3) + ' - ' + ssn[3..4] + ' - ' + ssn.last(4)

  ssn_number
rescue => e
  cobanc_feed_errors('SSN ' + e.message)
  raise
end

# bundle exec rake "cobanc_active_dependents_and_benfits_import[COBANC_active_member_list_with_dependents.csv]"
# bundle exec rake "cobanc_part_time_members_dependents_and_benefits_import[COBANC_Part_Timers_census.csv]"
# bundle exec rake "cobanc_retired_dependents_and_benefits_import[COBANC_Retirees_census.csv]"
# bundle exec rake "cobanc_ssn_import[cobanc, cobanc_ssn_imports.csv]"
