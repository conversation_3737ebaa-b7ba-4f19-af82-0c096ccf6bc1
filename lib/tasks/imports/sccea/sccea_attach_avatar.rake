# frozen_string_literal: true

require 'csv'

desc 'import data'
# rails "sccea_attach_avatar[sccea,sccea_images]"
task :sccea_attach_avatar, [:account, :dir_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  target_folder_path = File.join(Rails.root, args[:dir_path])

  attachment_names = Dir.children(target_folder_path)

  @errors = {}

  attachment_names.each do |attachment|
    @row_number = attachment

    employee_name = attachment.split('.')[0]
    names = employee_name.split(',')
    last_name = names[0].try(:strip)
    first_names = names[1].try(:strip)
    first_name = nil
    first_name_arr = first_names.split(' ') if first_names.present?
    first_name = first_name_arr[0].try(:strip) if first_name_arr.present?

    employees = Employee.where('last_name ilike ?', last_name)

    if employees.present?
      if employees.count > 1
        employees = employees.where('first_name ilike ?', first_name) if first_name.present?

        if employees.present?
          if employees.count > 1
            employee = employees.first
          else
            employee = employees.first
          end
        else
          feed_errors('Members not found')
        end
      else
        employee = employees.first
      end
    else
      feed_errors('Members not found')
    end

    if employee.present?
      employee.avatar.attach(io: File.open("#{target_folder_path}/#{attachment}"), filename: attachment)
    end

  end

  file_name = args[:dir_path].split('.').first

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_#{file_name}_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end

def feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end

