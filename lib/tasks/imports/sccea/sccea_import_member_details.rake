# frozen_string_literal: true

require 'csv'

desc 'import data'
# rails "sccea_import_member_details[sccea,sccea_contacts_new.csv]"
# rails "sccea_import_member_details[sccea,sccea_name_address.csv]"
# rails "sccea_import_member_details[sccea,sccea_title_salary.csv]"
task :sccea_import_member_details, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  # LAST NAME,FIRST NAME,MIDDLE NAME,ADDRESS,CITY,Apt,STATE,POSTAL
  # FIRST NAME,MIDDLE NAME,LAST NAME,Birthday,Gender,Notes,Personal Email,Cell Phone,Work Locations,Employment Status
  # LAST NAME,FIRST NAME,MIDDLE NAME,GRADE,TITLE
  # LAST NAME,FIRST NAME,<PERSON>DDLE NAME,TITLE,GRADE,ADDRESS,CITY,STATE,POSTAL,UCS Start Date

  csv_file.each do |row|

    first_name = row['First Name'] || ''
    last_name = row['Last'] || ''
    mi = row['Middle'] || ''

    @row_number = first_name + ' ' + last_name + ' ' + mi
    street = row['Address 1'] || ''
    ssn = row['SSN4'] || ''
    city = row['City'] || ''
    state = row['State'] || ''
    apt = row['Apt'] || ''
    zipcode = row['Zip Code'].present? ? row['Zip Code'].split('-').first.rjust(5, '0') : ''
    dob = sccea_parse_date(row['Birthdate'], 'dob')
    sex = check_gender(row['Gender'])
    notes = row['Notes'] || ''
    email = row['Personal Email'] || ''
    cell_phone = parse_phone(row['Cell Phone'])
    court_location = check_office(row['Work Locations'])
    employment_status = check_employment_status(row['Employment Status'])
    employment_status_start_date = sccea_parse_date(row['Start Date of Retirement'], 'Start Date of Retirement')
    grade_number = row['GRADE'].present? ? ('JG' + row['GRADE']) : ''
    ncc_date = parse_date(row['UCS Start Date'], 'USC')
    title = check_rank(row['TITLE'])

    @current_account = Account.find_by(subdomain: Apartment::Tenant.current)

    employees = Employee.where('first_name ilike ? and last_name ilike ?', first_name, last_name)

    if employees.present?
      if employees.count > 1
        feed_errors('Multiple occurrences for name')
        next
      else
        employee = employees.first
      end
    else
      employee = Employee.new
    end

    employee.first_name = first_name
    employee.last_name = last_name
    employee.middle_name = mi
    employee.street = street if street.present?
    employee.apartment = apt if apt.present?
    employee.city = city if city.present?
    employee.state = state if state.present?
    employee.social_security_number = ssn if ssn.present?
    employee.zipcode = zipcode if zipcode.present?
    employee.notes = notes if notes.present?
    employee.placard_number = grade_number if grade_number.present?
    employee.birthday = dob if dob.present?
    employee.gender_id = sex.id if sex.present?
    employee.ncc_date = ncc_date if ncc_date.present?

    if employee.save
      employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE, value: cell_phone).save! if cell_phone.present?
      employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL, value: email).save! if email.present?

      employee.employee_ranks.new(rank_id: title.id).save(validate: false) if title.present?
      employee.employee_offices.new(office_id: court_location.id).save(validate: false) if court_location.present?
      employee.employee_employment_statuses.new(employment_status_id: employment_status.id, start_date: employment_status_start_date).save(validate: false) if employment_status.present?
    else
      @errors[@row_number] = employee.errors.full_messages
    end

  rescue => e
    p @row_number, e.message
    feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end

def check_gender(gender_type)
  return nil unless gender_type.present?

  gender = Gender.where(name: gender_type).first_or_create

  gender

rescue => e
  feed_errors('GENDER ' + gender.errors.full_messages)
end

def check_employment_status(employment_status_type)
  return nil unless employment_status_type.present?

  employment_status = EmploymentStatus.where(name: employment_status_type).first_or_create

  employment_status

rescue => e
  feed_errors('EMPLOYMENT_STATUS ' + employment_status.errors.full_messages)
end

def check_office(office_name)
  return nil unless office_name.present?

  office = Office.where(name: office_name).first_or_create

  office

rescue => e
  feed_errors('OFFICE ' + office.errors.full_messages)
end

def check_rank(rank_name)
  return nil unless rank_name.present?

  rank = Rank.where(name: rank_name).first_or_create

  rank

rescue => e
  feed_errors('RANK ' + rank.errors.full_messages)
end

def sccea_parse_date(date, type)
  return nil unless date.present?

  parsed_date = nil

  begin
    date_array = date.split('/')
    day = date_array[1].to_i
    month = date_array[0].to_i
    year = date_array[2].to_i

    if year.to_s.length == 2
      if year > 20 && year <= 99
        year = ('19' + year.to_s).to_i
      else
        year = ('20' + year.to_s).to_i
      end
    elsif year.to_s.length == 1
      year = ('200' + year.to_s).to_i
    elsif year.to_s.length > 4
      raise 'Invalid Year'
    else
      year = year.to_i
    end

    parsed_date = Date.new(year, month, day)
  rescue
    parsed_date = Date.parse(date)
  end

  parsed_date

rescue => e
  feed_errors('DATE - ' + type + " #{date} " + e.message)
end

def parse_phone(phone)
  return nil unless phone.present?

  phone.remove!('(', ')', ' ', '-')

  phone_number = '(' + phone.first(3) + ') ' + phone[3..5] + ' - ' + phone.last(4)

  phone_number

rescue => e
  feed_errors('PHONE ' + e.message)
end

def feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end

