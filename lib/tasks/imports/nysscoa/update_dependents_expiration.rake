task :nysscoa_update_dependents_expiraiton, [:account] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  officer_statuses =  ["deceased", "transferred", "resigned", "terminated", "promoted to clerk", "opt-out", "no longer member", "bad address"]
  employees = Employee.kept.includes(:employee_officer_statuses).where("(employee_officer_statuses.end_date is NULL or employee_officer_statuses.end_date > ?) and employee_officer_statuses.officer_status_id in (?)",Date.today, OfficerStatus.where("lower(name) in (?)", officer_statuses).pluck(:id)).references(:employee_officer_statuses)
  @errors = {}

  employees.each do |employee|
    employee_benefits = employee.employee_benefits.where("end_date is NULL")
    employee_benefits.update_all(end_date: Date.parse('2023/06/30')) if employee_benefits.present?
    benefit_coverages = employee.benefit_coverages.where("expires_at is NULL")
    benefit_coverages.update_all(expires_at: Date.parse('2023/06/30')) if benefit_coverages.present?

  rescue => e
    p @row_number, e.message
    nysscoa_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_update_dependents_expiration.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

def nysscoa_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end