require 'csv'

desc 'import data'
task :ufadba_import_member_details, [:account, :file_path, :employment_status] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  employment_status = if args[:employment_status].present?
                        check_employment_status(args[:employment_status])
                      else
                        check_employment_status('Active')
                      end

  @errors = {}
  csv_file.each do |row|
    first_name = row['First Name']
    last_name = row['Last Name']
    suffix = row['Suffix'] if row['Suffix'].present?
    @row_number = [first_name, last_name, suffix].join(' ')
    address = if row['Street'].present?
                row['Street'].split(/apt|Apt/)
              elsif row['Home Address 1'].present?
                row['Home Address 1'].split(/apt|Apt/)
              end
    apartment = address[1].prepend('Apt') if address[1].present?
    street = address[0].strip.chomp(',') if address[0].present?
    city = row['City'] if row['City'].present?
    state = if row['State'].present?
              row['State']
            elsif row['State Province'].present?
              row['State Province']
            end
    zipcode = if row['ZIP code'].present?
                row['ZIP code']
              elsif row['Zip/Postal Code']
                row['Zip/Postal Code'].split('-')[0].strip
              end
    personal_mail = row['Email'] if row['Email'].present? # Personal Email
    # Personal Phone
    phone = if row['Phone'].present?
              parse_phone(row['Phone'])
            elsif row['Mobile Phone'].present?
              parse_phone(row['Mobile Phone'])
            end
    mobile = parse_phone(row['Mobile']) if row['Mobile'].present? # work_phone

    employees = Employee.kept.where('last_name ilike ? and first_name ilike ?', ('%' + last_name + '%'), (first_name + '%')) if last_name.present? && first_name.present?

    if employees.present?
      ufadba_feed_errors('Member was already created')
    else
      employee = Employee.new
      employee['first_name'] = first_name
      employee['last_name'] = last_name
      employee['suffix'] = suffix if suffix.present?
      employee['apartment'] = apartment if apartment.present?
      employee['street'] = street
      employee['city'] = city
      employee['state'] = state
      employee['zipcode'] = zipcode.rjust(4, '0') if zipcode.present?

      if employee.save
        personal_phone_contact = employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE)
        personal_phone_contact.value = phone if phone
        personal_phone_contact.save!

        employee.contacts.new(contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE).save!

        work_phone_contact = employee.contacts.new(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE)
        work_phone_contact.value = mobile if mobile
        work_phone_contact.save!

        personal_mail_contact = employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL)
        personal_mail_contact.value = personal_mail if personal_mail
        personal_mail_contact.save!

        employee.contacts.new(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL).save!

        employee.employee_employment_statuses.new(employment_status_id: employment_status.id).save(validate: false) if employment_status
      else
        @errors[@row_number] = employee.errors.full_messages
      end
    end

  rescue => e
    p @row_number, e.message
    ufadba_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_member_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

task :ufadba_file_upload, [:account, :dir_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  target_folder_path = File.join(Rails.root, args[:dir_path])

  attachment_names = Dir.children("#{target_folder_path}")

  @errors = {}

  attachment_names.each do |attachment|
    @row_number = attachment

    employee_name = attachment.split('.')[0]
    names = employee_name.split(',')
    last_name = names[0].try(:strip)
    first_name = names[1].try(:strip)
    sub_attachments = Dir.children("#{target_folder_path}/#{attachment}")

    employees = Employee.kept.where('last_name ilike ? and first_name ilike ?', ('%' + last_name + '%'), (first_name + '%')) if last_name.present? && first_name.present?

    if employees.present?
      if employees.count > 1
        ufadba_feed_errors('Multiple occurrences for member')
      else
        employee = employees.first
      end
    else
      ufadba_feed_errors('Members not found')
    end

    if employee.present? && sub_attachments.present?
      sub_attachments.each do |sub_attachment|
        employee_upload = employee.uploads.new(file: { io: File.open("#{target_folder_path}/#{attachment}/#{sub_attachment}"), filename: attachment })
        @errors[@row_number] = employee_upload.errors.full_messages unless employee_upload.save
      end
    end

  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_ufadba_file_upload_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end

task :ufadba_update_member_details, [:file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!('ufadba')
  @current_account = Account.find_by(subdomain: Apartment::Tenant.current)

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  # NAME,ACTIVE/RETIRED,DOB,ZIP

  active_status = check_employment_status('Active')
  retiree_status = check_employment_status('Retiree')

  csv_file.each do |row|

    full_name = row['NAME'].split(',')

    if full_name.size > 2
      first_name = full_name.first.strip
      last_name = full_name[2..-1].join(" ").strip
      mi = full_name.second.strip
    else
      first_name = full_name.second.strip
      last_name = full_name.first.strip
      mi = ''
    end

    @row_number = row['NAME']
    dob = Date.parse(row['DOB']) rescue nil
    zipcode = row['ZIP'].present? ? row['ZIP'].split('-').first.rjust(5, '0') : ''

    members = Employee.where('lower(first_name) = ? and lower(last_name) = ?', first_name.downcase, last_name.downcase)

    if members.present?
      if  members.count > 1
        ufadba_feed_errors("Multiple Occurrence for the Member")
        next
      else
        member = members.first
      end
    else
      ufadba_feed_errors("Member Not Found")
      next
    end

    member.birthday = dob if dob.present?
    member.zipcode = zipcode if zipcode.present?

    if member.save(validate: false)
      member_status = if row['ACTIVE/RETIRED'] == 'active'
                        active_status
                      else
                        retiree_status
                      end

      unless member.employee_employment_statuses.where(end_date: nil).ids.uniq.include?(member_status.id)
        member.employee_employment_statuses.new(employment_status_id: member_status.id).save(validate: false)
      end
    else
      @errors[@row_number] = member.errors.full_messages
    end

  rescue => e
    p @row_number, e.message
    ufadba_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/ufadba_#{Date.today}_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end

def check_employment_status(employment_status_type)
  return nil unless employment_status_type.present?

  employment_status = EmploymentStatus.where(name: employment_status_type).first_or_create

  employment_status

rescue => e
  ufadba_feed_errors('EMPLOYMENT_STATUS ' + employment_status.errors.full_messages)
end

def parse_phone(phone)
  return nil unless phone.present?
  return nil if phone.length < 10

  phone.gsub!(/[^\d]/, '')

  phone_number = '(' + phone.first(3) + ') ' + phone[3..5] + ' - ' + phone.last(4)

  phone_number

rescue => e
  ufadba_feed_errors('PHONE ' + e.message)
end

def ufadba_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end

# bundle exec rake 'ufadba_import_member_details[ufadba, file_path]'
# bundle exec rake 'ufadba_file_upload[ufadba, directory_path]'
