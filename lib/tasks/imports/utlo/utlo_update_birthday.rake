# frozen_string_literal: true

require 'csv'

desc 'import data'
task :utlo_update_birthday, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  new_records = 0

  # BSCID,Birthdate

  csv_file.each do |row|

    shield_number = row['BSCID']

    @row_number = shield_number

    dob = utlo_date_new(row['Birthdate'], 'DOB')

    current_account = Account.find_by(subdomain: Apartment::Tenant.current)
    @current_account = Account.find_by(subdomain: Apartment::Tenant.current)

    employee = if shield_number.present?
                 Employee.where(shield_number: shield_number).first_or_initialize
               else
                 feed_errors('New Member')
                 new_records += 1
               end

    if employee.present?
      employee.birthday = dob

      unless employee.save
        @errors[@row_number] = employee.errors.full_messages
      end
    end

  rescue => e
    p @row_number, e.message
    feed_errors(e.message)
  end

  file_name = args[:file_path].split('.').first

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_#{file_name}_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

  p "New Records : #{new_records}"

end

def utlo_date_new(date, type)
  return nil unless date.present?

  parsed_date = nil

  begin
    date_array = date.split('/')
    day = date_array[0].to_i
    month = date_array[1].to_i
    year = date_array[2].to_i

    if year.to_s.length == 2
      if year > 20 && year <= 99
        year = ('19' + year.to_s).to_i
      else
        year = ('20' + year.to_s).to_i
      end
    elsif year.to_s.length == 1
      year = ('200' + year.to_s).to_i
    elsif year.to_s.length > 4
      raise 'Invalid Year'
    else
      year = year.to_i
    end

    parsed_date = Date.new(year, month, day)
  rescue
    parsed_date = Date.parse(date)
  end

  parsed_date

rescue => e
  feed_errors('DATE - ' + type + " #{date} " + e.message)
end

def feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end
