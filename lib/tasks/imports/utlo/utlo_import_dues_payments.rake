require 'csv'

desc 'import data'
task :utlo_import_dues_payments, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!('utlo')

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  employee_ids = []

  dues_paying_pacf = Pacf.kept.where('name ilike ?', 'dues paying').first

  csv_file.each do |row|
    shield_number = if row['Empl ID'].present?
                      row['Empl ID']
                    elsif row['ID'].present?
                      row['ID']
                    elsif row['BSCID'].present?
                      row['BSCID']
                    end
    a_number = if row['Legacy Emplid'].present?
                 row['Legacy Emplid']
               elsif row['ID'].present?
                 row['ID']
               elsif row['PASS'].present?
                 row['PASS']
               end

    @row_number = if shield_number.present?
                    shield_number
                  elsif a_number.present?
                    a_number
                  end

    date = if row['Payment Date'].present?
             dues_parse_date(row['Payment Date'])
           end

    current_account = Account.find_by(subdomain: Apartment::Tenant.current)
    @current_account = Account.find_by(subdomain: Apartment::Tenant.current)

    employee = if shield_number.present?
                 Employee.kept.where(shield_number: shield_number).first
               elsif a_number.present?
                 Employee.kept.where(a_number: a_number).first
               end

    if employee.blank? && shield_number.present? && row['Employee Name'].present?
      new_employee = Employee.new
      new_employee.shield_number = shield_number
      new_employee.a_number = a_number if a_number.present?
      new_employee.first_name = row['Employee Name'].split(",").first
      new_employee.last_name = row['Employee Name'].split(",").last
      unit = Unit.where(name: row['Company']).first_or_create if row['Company'].present?
      new_employee.unit_id = unit.id if unit.present?
      if new_employee.save!
        new_employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE).save!
        new_employee.contacts.new(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE).save!
        new_employee.contacts.new(contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE).save!
        new_employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL).save!
        new_employee.contacts.new(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL).save!
        new_employee.contacts.new(contact_for: Contact::ContactFor::COLLEAGUE, contact_type: Contact::ContactType::EMAIL).save!
        employment_status = EmploymentStatus.where('lower(name) ilike ?', '%dues paying%').order(:name).first
        new_employee.employee_employment_statuses.new(employment_status_id: employment_status.id).save!
        employee = new_employee
      end
    end

    if employee.present? && dues_paying_pacf.present?
      employee_ids << employee.id
      employee_pacf = employee.employee_pacfs.new(pacf_id: dues_paying_pacf.id, date: date)
      employee_pacf.save(validate: false)
      employment_status = EmploymentStatus.where('name ilike ?', 'Active - Non-Dues Paying').pluck(:id)
      employee_employment_status = employee.employee_employment_statuses.where('employment_status_id = ?', employment_status)
      if employee_employment_status.present?
        update_id = employment_status
        dues_paying_update(employee, update_id)
      end
    elsif employee.blank?
      dues_feed_errors("Employee Mandatory Details Not Present")
    end

  rescue => e
    p @row_number, e.message
    dues_feed_errors(e.message)
  end

  p Employee.ids - employee_ids.uniq

  employees = Employee.eager_load(:employee_pacfs).where('employees.id not in (?) and employee_pacfs.pacf_id = ?', employee_ids.uniq, dues_paying_pacf.id)
  employees.each do |employee|
    employment_status = EmploymentStatus.where('name ilike ?', 'Active - Dues Paying').pluck(:id)
    employee_employment_status = employee.employee_employment_statuses.where('employment_status_id = ?', employment_status)
    if employee_employment_status.present?
      update_id = employment_status
      dues_paying_update(employee, update_id)
    end
  rescue => e
    p employee.id, e.message
    dues_feed_errors(e.message)
  end
  CSV.open("#{Rails.root}/utlo_import_dues_payments_errors_#{args[:file_path].split(".").first}_#{Time.now.to_i}.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

task :utlo_remove_duplicates_dues, [:account] => :environment do |_t, args|

  Apartment::Tenant.switch!('utlo')

  @errors = {}
  employees = Employee.includes(:employee_pacfs).where("employee_pacfs.date >= ? and employee_pacfs.date <= ?", "2022/09/01", "2023/02/28").references(:employee_pacfs)
  employees.each do |employee|
    @row_number = employee&.shield_number
    employee_pacfs = employee.employee_pacfs
    if employee_pacfs.present?
      employee_pacfs.each do |pacf|
        date = pacf.date
        pacfs = employee_pacfs.where(date: date).order(id: :desc)
        if pacfs&.present? && pacfs&.count > 1
          pacf_id = pacfs.first.id
          employee_pacfs.where('id NOT IN (?) and date = ?', pacf_id, date).destroy_all
        end
      end
    else
      dues_feed_errors("Employee Mandatory Details Not Present")
    end

  rescue => e
    p employee.id, e.message
    dues_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/utlo_remove_duplicates_dues_#{Date.today}_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

task :utlo_adding_dues_payment_date, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!('utlo')

  @errors = {}

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  dues_paying_pacf = Pacf.kept.where('name ilike ?', 'dues paying').first
  table_name = args[:file_path] || ''
  csv_file.each do |row|
    employee_name = row['Employee Name'] || row['Name']
    @row_number = employee_name
    shield_number = row['BSCID']
    pass_number = row['Badge Number']
    @row_number = shield_number if @row_number.blank?
    payment_date = row['Payment Date'] || row['Date']

    if shield_number.blank? && pass_number.blank?
      dues_feed_errors('Mandatory Details not present')
      next
    end

    if shield_number.present?
      employees = Employee.kept.where(shield_number: shield_number)
    end

    if (shield_number.blank? || employees.blank? || employees.count > 1) && pass_number.present?
      employees = Employee.kept.where(a_number: pass_number)
    end

    if (pass_number.blank? || employees.blank? || employees.count > 1) && employee_name.present?
      first_name = employee_name.split(',').last.split(' ').first
      last_name = employee_name.split(',').first
      employees = Employee.kept.where(first_name: first_name, last_name: last_name)
    end

    message = if employees.blank?
                'Employee Not found'
              elsif employees.count > 1
                'More than one Employee found'
              end
    if message.present?
      dues_feed_errors(message)
      next
    end

    if employees.present? && employees.count == 1
      employees.first.employee_pacfs.create!(pacf_id: dues_paying_pacf.id, date: dues_parse_date(payment_date))
      next
    end
  rescue => e
    p employees.id, e.message
    dues_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{table_name.split('/').first}/utlo_dues_errors_may_29/#{table_name.split('/').last.split('.').first}_#{Date.today}_errors.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']

    @errors.each do |error|
      csv << error
    end
  end
end

task :utlo_dues_import_2024, %i[account directory_path] => :environment do |t, args|
  Apartment::Tenant.switch!(args[:account])

  @errors = {}
  dues_paying_pacf_id = Pacf.kept.where('lower(name) = ?', 'dues paying').first.id

  Dir.glob("#{args[:directory_path]}/**/*.csv").each do |file_path|
    @row_number = file_path

    file = File.read(file_path)
    csv_file ||= CSV.parse(file, headers: true)
    csv_file.each do |row|
      shield_number = row['BSCID']&.strip
      name = row['Employee Name']&.strip&.split(',')
      @row_number = "#{file_path} -- #{name}"

      next if shield_number.blank? || name.blank?

      row_date = row['Payment Date'] || row['Date']

      if row_date.blank?
        dues_feed_errors('Payment Date was Missing')
        next
      end
      date = row_date.present? ? Date.strptime(row_date, '%m/%d/%y') : ''
      first_name = name.last
      last_name = name.first

      employees = Employee.kept.where(shield_number: shield_number)
      employees = Employee.kept.where('lower(first_name) LIKE ? AND lower(last_name) LIKE ?', "%#{first_name}%", "%#{last_name}%") if employees.blank?

      if employees.count.zero?
        dues_feed_errors('Invalid Member or Member was missing')
        next
      elsif employees.count > 1
        dues_feed_errors('More than One Member found')
        next
      end

      employees.first.employee_pacfs.create!(pacf_id: dues_paying_pacf_id, date: date, amount: 0)
    rescue StandardError => e
      p @row_number, e.message
      dues_feed_errors(e.message)
    end
  rescue StandardError => e
    dues_feed_errors("Error processing file #{file_path}: #{e.message}")
  end

  CSV.open("#{Rails.root}/#{t}_errors_#{Time.now.strftime('%d-%m-%Y-%H:%M')}.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']

    @errors.each do |error|
      csv << error
    end
  end
end

def dues_parse_date(date)
  return nil unless date.present?

  parsed_date = nil

  begin
    date_array = date.split('/')
    day = date_array[1].to_i
    month = date_array[0].to_i
    year = date_array[2].to_i

    if year.to_s.length == 2
      year = ('20' + year.to_s).to_i
    elsif year.to_s.length == 1
      year = ('200' + year.to_s).to_i
    elsif year.to_s.length > 4
      raise 'Invalid Year'
    else
      year = year.to_i
    end

    parsed_date = Date.new(year, month, day)
  rescue
    parsed_date = Date.parse(date)
  end

  parsed_date

rescue => e
  dues_feed_errors('DATE - ' + " #{date} " + e.message)
end

def dues_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end

def dues_paying_update(employee, update_id)
  employment_statuses = employee.employee_employment_statuses.where('end_date is NUll OR end_date >= ?', Date.today)
  employment_status_ids = employment_statuses.pluck(:employment_status_id) if employment_statuses.present?
  employment_status = EmploymentStatus.where(id: employment_status_ids).where("name ilike '%paying'") if employment_status_ids.present?
  employee_employment_status = employment_statuses.where(employment_status_id: employment_status.ids) if employment_status.present?

  if employee_employment_status.present?
      employee_employment_status.each do |employment_status|
      name = employment_status.name.split(' - ')
      due = EmploymentStatus.kept.where('name ilike ?', "#{name[0]} - Dues paying") if name.present?
      due_id = due.first.id if due.present?
      non_due = EmploymentStatus.kept.where('name ilike ?', "#{name[0]} - Non-Dues paying") if name.present?
      non_due_id = non_due.first.id if non_due.present?
      if update_id == due_id
        employee_employment_status = employee_employment_status.where('employment_status_id = ?', update_id)
        employee_employment_status.update(employment_status_id: non_due_id) if employee_employment_status.present?
      elsif update_id == non_due_id
        employee_employment_status = employee_employment_status.where('employment_status_id = ?', update_id)
        employee_employment_status.update(employment_status_id: due_id) if employee_employment_status.present?
      end
      end
  end
end

## UTLO DUES IMPORT ON 29-MAY-2024
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/MAB20240126-Table1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/NYT20240126-Table1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/NYT20240112-Table1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/SIR20240113-Table1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/MTB20240106-Table1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/MAB20240112-Table1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/MTA20240103-Table1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/MTB20240120-Table1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/SIR\ 20240323-Table\ 1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/MTB\ 20240316-Table\ 1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/MAB\ 20240322-Table\ 1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/NYT\ 20240322-Table\ 1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/MTA\ 20240313-Table\ 1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/SIR\ 20240309-Table\ 1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/MTA\ 20240327-Table\ 1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/MAB\ 20240308-Table\ 1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/NYT\ 20240308-Table\ 1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/MTB\ 20240330-Table\ 1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/MTB\ 20240302-Table\ 1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/MAB\ 20240223-Table\ 1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/MTB\ 20240217-Table\ 1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/SIR\ 20240224-Table\ 1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/NYT\ 20240223-Table\ 1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/SIR\ 20240210-Table\ 1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/MTA\ 20240228-Table\ 1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/MAB\ 20240209-Table\ 1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/MTA\ 20240214-Table\ 1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/NYT\ 20240209-Table\ 1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/MTB\ 20240203-Table\ 1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/MTB\ 20240427-Table\ 1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/MTB\ 20240413-Table\ 1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/NYT\ 2024419-Table\ 1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/NYT\ 20240405-Table\ 1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/SIR\ 20240420-Table\ 1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/MAB\ 20240419-Table\ 1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/MAB\ 20240405-Table\ 1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/MTA\ 20240424-Table\ 1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/MTA\ 20240410-Table\ 1.csv]'
# bundle exec rake 'utlo_adding_dues_payment_date[utlo, utlo_dues_payment_may_29/SIR\ 20240406-Table\ 1.csv]'
# ------------------------------------------------------------------------------------------------------------ #
# bundle exec rake 'utlo_dues_import_2024[utlo , 2024-UTLO-DUES-IMPORT]'
