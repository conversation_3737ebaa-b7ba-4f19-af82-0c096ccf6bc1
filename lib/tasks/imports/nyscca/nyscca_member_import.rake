# frozen_string_literal: true

require 'csv'

desc 'import data'
task :nyscca_members_import, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  # member_id	last_name	first_name	address	city	state_territory	zip	country	ssn	dob	sex	email_address	home_phone	cell_phone	work_phone	status	substatus	present_title	court	county	system_date_entered	title_on_promotion_to_clerk	promotion_dt_senior_court_clerk	promotion_dt_associate_court_clerk	promotion_dt_court_clerk_trainer	promotion_dt_principle_court_clerk	promotion_dt_asst_dept_chief_clerk	promotion_dt_court_clerk_specialist	promotion_dt_retirement	fire_arm	model	serial	shield_number	pension_tier	health_plan	dental_plan	dental_date	beneficiary_name	transfer_to	date_of_transfer	date_of_death	original_date_of_appt	curr_ts	orig_table	orig_idf

  contacts_hash = [
    { contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL, value: '' },
    { contact_for: Contact::ContactFor::COLLEAGUE, contact_type: Contact::ContactType::EMERGENCY, value: '' },
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMERGENCY, value: '' }
  ]
  gender_hash = {}
  affiliation_hash = {}
  benefit_hash = {}
  dental_benefit_hash = {}
  rank_hash = {}
  employment_status_hash = {}
  court_hash = {}
  county_hash = {}
  @value_downcase = ->(x) { x&.downcase&.delete(' ') }

  Employee.skip_callback :save, :after, :update_benefit_address
  if ENV['APP_ENV'] == 'development'
    Employee.skip_callback :commit, :after, :update_legislative_detail
  else
    Employee.skip_callback :save, :after, :update_legislative_detail
  end
  EmployeeEmploymentStatus.skip_callback :save, :after, :update_expiry_status

  csv_file.each do |row|
    first_name = row['first_name'] || ''
    last_name = row['last_name'] || ''
    ssn = row['ssn'] || ''
    @row_number = [first_name, last_name]

    if first_name.blank? || last_name.blank?
      next if (@row_number = row['ssn']).blank?

      nyscca_feed_errors('Mandatory details not present')
      next
    end
    employee = Employee.new
    employee.first_name = first_name
    employee.last_name = last_name
    employee.social_security_number = ssn
    employee.street = row['address'] || ''
    employee.city = row['city'] || ''
    employee.state = row['state_territory'] || ''
    employee.zipcode = row['zip'] || ''
    employee.birthday = row['dob'].present? && row['dob'] != '0000-00-00' ? Date.parse(row['dob']) : ''
    employee.gender_id = if row['sex'].present? && gender_hash[@value_downcase.call(row['sex']).to_sym].present?
                           gender_hash[@value_downcase.call(row['sex']).to_sym]
                         elsif row['sex'].present?
                           create_associated_model_and_values('Gender', row['sex'], gender_hash)&.id
                         end

    employee.shield_number = row['shield_number'] || ''
    employee.previous_shield_number = row['model'] || ''
    employee.maiden_name = row['fire_arm'] || ''
    employee.prescription = row['serial'] || ''
    employee.member_start_date = Date.parse(row['original_date_of_appt']) if row['original_date_of_appt'].present? && row['original_date_of_appt'] != '0000-00-00'
    employee.affiliation_id = if row['pension_tier'].present? && affiliation_hash[@value_downcase.call(row['pension_tier']).to_sym].present?
                                affiliation_hash[@value_downcase.call(row['pension_tier']).to_sym]
                              elsif row['pension_tier'].present?
                                create_associated_model_and_values('Affiliation', row['pension_tier'], affiliation_hash)&.id
                              end

    if employee.save
      home_phone_hash = { contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE, value: '' }
      work_phone_hash = { contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE, value: '' }
      personal_phone_hash = { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE, value: '' }
      personal_email_hash = { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL, value: '' }

      home_phone_hash[:value] = nyscca_parse_phone(row['home_phone']) || ''
      work_phone_hash[:value] = nyscca_parse_phone(row['work_phone']) || ''
      personal_phone_hash[:value] = nyscca_parse_phone(row['cell_phone']) || ''
      personal_email_hash[:value] = row['email_address'] || ''

      contacts_hash1 = []
      contacts_hash1 << [contacts_hash, home_phone_hash, work_phone_hash, personal_phone_hash, personal_email_hash]

      # CONTACTS
      employee.contacts.import contacts_hash1.flatten

      # Employment Status
      status = row['status']
      status = 'Out of Union' if status == 'Other'
      status = 'Deceased' if status == 'Abated by Death'

      employment_status_id = if status.present? && employment_status_hash[@value_downcase.call(status).to_sym].present?
                               employment_status_hash[@value_downcase.call(status).to_sym]
                             elsif status.present?
                               create_associated_model_and_values('EmploymentStatus', status, employment_status_hash)&.id
                             end

      if employment_status_id.present?
        employee_employment_statuses = employee.employee_employment_statuses.new(employment_status_id: employment_status_id)
        employee_employment_statuses.start_date = Date.parse(row['promotion_dt_retirement']) if status.downcase == 'retired' && row['promotion_dt_retirement'].present? && row['promotion_dt_retirement'] != '0000-00-00'
        employee_employment_statuses.start_date = Date.parse(row['date_of_death']) if status.downcase == 'deceased' && row['date_of_death'].present? && row['date_of_death'] != '0000-00-00'
        employee_employment_statuses.save!(validate: false)
      end

      # COURT and COUNTY
      county_id = if row['county'].present? && county_hash[@value_downcase.call(row['county']).to_sym].present?
                    county_hash[@value_downcase.call(row['county']).to_sym]
                  elsif row['county'].present?
                    create_associated_model_and_values('Department', row['county'], county_hash)&.id
                  end

      court_id = if row['court'].present? && court_hash[@value_downcase.call(row['court']).to_sym].present?
                   court_hash[@value_downcase.call(row['court']).to_sym]
                 elsif row['court'].present?
                   create_associated_model_and_values('Section', row['court'], rank_hash, county_id)&.id
                 end

      employee_sections = employee.employee_sections.new(section_id: court_id, department_id: county_id)
      employee_sections.save!(validate: false)

      # TITLES

      title_hash = { "senior_count_clerk": row['promotion_dt_senior_court_clerk'], "associate_court_clerk": row['promotion_dt_associate_court_clerk'], "court_clerk_trainer": row['promotion_dt_court_clerk_trainer'],
                     "principle_court_clerk": row['promotion_dt_principle_court_clerk'], "assistant_dept_chief_clerk": row['promotion_dt_asst_dept_chief_clerk'],
                     "court_clerk_specialist": row['promotion_dt_court_clerk_specialist'], "retirement": row['promotion_dt_retirement'] }

      title_hash = title_hash.sort_by { |k, v| v.split('-') }.reverse
      previous_start_date = ''
      title_hash.each do |title, date|
        next if date == '0000-00-00'

        title_name = title.to_s.titleize
        title_id = if title_name.present? && rank_hash[@value_downcase.call(title_name).to_sym].present?
                     rank_hash[@value_downcase.call(title_name).to_sym]
                   elsif title_name.present?
                     create_associated_model_and_values('Rank', title_name, rank_hash)&.id
                   end

        employee_ranks = employee.employee_ranks.new(rank_id: title_id)
        employee_ranks.start_date = Date.parse(date)
        employee_ranks.end_date = Date.parse(previous_start_date) if previous_start_date.present?
        employee_ranks.save!(validate: false)

        previous_start_date = date
      end

      benefit_id = if row['health_plan'].present? && benefit_hash[@value_downcase.call(row['health_plan']).to_sym].present? && row['health_plan'] != 'None'
                     benefit_hash[@value_downcase.call(row['health_plan']).to_sym]
                   elsif row['health_plan'].present? && row['health_plan'] != 'None'
                     create_associated_model_and_values('Benefit', row['health_plan'], benefit_hash)&.id
                   end

      dental_benefit_id = if row['dental_plan'].present? && dental_benefit_hash[@value_downcase.call(row['dental_plan']).to_sym].present?
                            dental_benefit_hash[@value_downcase.call(row['dental_plan']).to_sym]
                          elsif row['health_plan'].present?
                            create_associated_model_and_values('Benefit', row['dental_plan'], dental_benefit_hash)&.id
                          end
      dental_benefit_start_date = Date.parse(row['dental_date']) if row['dental_date'].present? && row['dental_date'] != '0000-00-00'
      employee.employee_benefits.create(benefit_id: benefit_id)
      employee.employee_benefits.create(benefit_id: dental_benefit_id, start_date: dental_benefit_start_date)

    else
      @errors[@row_number] = employee.errors.full_messages
    end
  rescue => e
    p @row_number, e.message
    nyscca_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_members_import_errors.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']
    @errors.each do |error|
      csv << error
    end
  end

  Employee.set_callback :save, :after, :update_benefit_address
  if ENV['APP_ENV'] == 'development'
    Employee.set_callback :commit, :after, :update_legislative_detail
  else
    Employee.set_callback :save, :after, :update_legislative_detail
  end
  EmployeeEmploymentStatus.set_callback :save, :after, :update_expiry_status
end

task :nyscca_import_history, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  file_name = args[:file_path]
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  # MemberID,fname,lname,SSN,idf,tablename,person_idf,entered,mimetype,size,note,filename

  csv_file.each do |row|
    first_name = row['fname'] || ''
    last_name = row['lname'] || ''
    ssn = row['SSN'] || ''
    @row_number = [first_name, last_name]

    if first_name.blank? || last_name.blank?
      next if (@row_number = row['SSN']).blank?

      nyscca_feed_errors('Mandatory details not present')
      next
    end

    employees = Employee.kept.where("lower(first_name) = ? and lower(last_name) = ?", first_name.downcase, last_name.downcase)

    if employees.blank? || employees.count > 1
      employees = Employee.kept.where(social_security_number: ssn)
      if employees.blank?
        nyscca_feed_errors('Member not found')
        next
      elsif employees.count > 1
        nyscca_feed_errors('More than One employee found')
        next
      end
    end

    employee = employees.first
    row_date = row['entered'] || row['comment_time']
    created_date = DateTime.parse(row_date) if row_date.present?
    version_ids = employee.versions.pluck(:id)
    notes = row['note'] || row['comment']
    employee.update(notes: notes) if notes.present?
    after_version_update_ids = employee.versions.pluck(:id)
    new_version_ids = after_version_update_ids - version_ids
    PaperTrail::Version.where(id: new_version_ids).update_all(created_at: created_date) if created_date.present?

  rescue => e
    p @row_number, e.message
    nyscca_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_#{file_name}_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end

task :nyscca_import_titles, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @value_downcase = ->(x) { x&.downcase&.delete(' ') }
  @errors = {}
  rank_hash = {}
  total_employee_ranks = []
  count = 0

  csv_file.each do |row|
    first_name = row['first_name'] || ''
    last_name = row['last_name'] || ''
    ssn = row['ssn'] || ''
    @row_number = [first_name, last_name]

    if first_name.blank? || last_name.blank?
      next if (@row_number = row['ssn']).blank?

      nyscca_feed_errors('Mandatory details not present')
      next
    end

    employees = Employee.kept.where("lower(first_name) = ? and lower(last_name) = ?", first_name.downcase, last_name.downcase)

    if employees.blank? || employees.count > 1
      employees = Employee.kept.where(social_security_number: ssn)
      if employees.blank?
        nyscca_feed_errors('Member not found')
        next
      elsif employees.count > 1
        nyscca_feed_errors('More than One employee found')
        next
      end
    end

    employee = employees.first
    title_name = row['title_on_promotion_to_clerk'] || ''
    title_id = if title_name.present? && rank_hash[@value_downcase.call(title_name).to_sym].present?
                 rank_hash[@value_downcase.call(title_name).to_sym]
               elsif title_name.present?
                 create_associated_model_and_values('Rank', title_name, rank_hash)&.id
               end
    next if title_name.blank?

    nyscca_start_date = row['original_date_of_appt'] || ''
    employee_ranks = employee.employee_ranks.pluck(:start_date).sort
    nyscca_end_date = employee_ranks&.first
    if (nyscca_start_date.present? && nyscca_start_date == '0000-00-00') || nyscca_start_date.blank?
      nyscca_start_date = nyscca_end_date
    end

    if nyscca_start_date.blank? || nyscca_end_date.blank?
      nyscca_feed_errors('Start date or End date is missing')
      next
    end

    employee_ranks_new = employee.employee_ranks.new(rank_id: title_id)
    employee_ranks_new.start_date = Date.parse(nyscca_start_date.to_s)
    employee_ranks_new.end_date = Date.parse(nyscca_end_date.to_s)

    total_employee_ranks << employee_ranks_new
    if count == 500
      EmployeeRank.import total_employee_ranks.flatten
      total_employee_ranks = []
      count = 0
    end
    count += 1

  rescue => e
    p @row_number, e.message
    nyscca_feed_errors(e.message)
  end
  EmployeeRank.import total_employee_ranks.flatten

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_nyscca_titles_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end

task :nyscca_import_beneficiaries, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  csv_file.each do |row|
    first_name = row['fname'] || ''
    last_name = row['lname'] || ''
    ssn = row['SSN'] || ''
    @row_number = [first_name, last_name]

    if first_name.blank? || last_name.blank?
      next if (@row_number = row['ssn']).blank?

      nyscca_feed_errors('Mandatory details not present')
      next
    end

    employees = Employee.kept.where("lower(first_name) = ? and lower(last_name) = ?", first_name.downcase, last_name.downcase)

    if employees.blank? || employees.count > 1
      employees = Employee.kept.where(social_security_number: ssn)
      if employees.blank?
        nyscca_feed_errors('Member not found')
        next
      elsif employees.count > 1
        nyscca_feed_errors('More than One employee found')
        next
      end
    end

    employee = employees.first

    employee_beneficiries = employee.beneficiaries.new
    name = row['ben_first_name']
    name += " #{row['ben_last_name']}" if row['ben_last_name'].present? && row['ben_last_name'] != '[ARCHIVE]'

    next if name.blank?

    employee_beneficiries.name = name
    employee_beneficiries.relationship = row['ben_relation'] || ''
    employee_beneficiries.beneficiary_type = 'Primary'
    employee_beneficiries.birthday = Date.parse(row['ben_dob']) if row['ben_dob'].present? && row['ben_dob'] != '0000-00-00'
    employee_beneficiries.save(validate: false)

  rescue => e
    p @row_number, e.message
    nyscca_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_nyscca_import_beneficiaries.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

def nyscca_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end

def create_associated_model_and_values(model_name, value, hash, second_value = nil)
  return unless value.present?
  model_value = if second_value.blank?
                  model_name.constantize.where('lower(name) = ?', value&.downcase)&.first
                else
                  model_name.constantize.where('lower(name) = ? and department_id = ?', value&.downcase, second_value)&.first
                end

  if model_value.blank?
    model_value = if second_value.blank?
                    model_name.constantize.create!(name: value)
                  else
                    model_name.constantize.create!(name: value, department_id: second_value)
                  end
  end
  hash[@value_downcase.call(value).to_sym] = model_value.id if hash[value.to_sym].blank? || (hash[value.to_sym].present? && hash[value.to_sym] != model_value.id)
  model_value
end

def nyscca_parse_phone(phone)
  return nil unless phone.present?
  return nil if phone.length < 10

  phone.remove!('(', ')', ' ', '-')

  phone_number = '(' + phone.first(3) + ') ' + phone[3..5] + ' - ' + phone.last(4)

  phone_number

rescue => e
  nyscca_feed_errors('PHONE ' + e.message)
end

# bundle exec rake "nyscca_members_import[nyscca, nyscca_member.csv]"
# bundle exec rake "nyscca_import_history[nyscca, nyscca-history.csv]"
# bundle exec rake "nyscca_import_history[nyscca, nyscca-comment.csv]"
# bundle exec rake "nyscca_import_titles[nyscca, nyscca_member.csv]"
# bundle exec rake "nyscca_import_beneficiaries[nyscca, nyscca-beneficiary.csv]"
