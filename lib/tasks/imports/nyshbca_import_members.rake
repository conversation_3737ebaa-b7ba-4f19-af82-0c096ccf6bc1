require 'csv'

desc 'import data'
task :nyshbca_import_members, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  csv_file.each do |row|
    name = row['NAME'].split(' ')
    first_name = name[0].last == ',' ? name[0][0...-1] : name[0]
    last_name = name.drop(1).join(' ')
    @row_number = first_name + ' ' + last_name
    apartment = row['ADDRESS'] if row['ADDRESS'].present?
    city_state_zip = row['City, State, ZIP'].split(',') if row['City, State, ZIP'].present?
    if city_state_zip.present?
      city = city_state_zip[0].strip if city_state_zip[0].present?
      state = city_state_zip[1].strip if city_state_zip[1].present?
      zipcode = city_state_zip[2].split('-')[0].strip if city_state_zip[2].present?
    end
    cell_phone = nyshbca_parse_phone(row['CELL']) if row['CELL'].present? && row['CELL'] != 'NO PHONE'
    personal_email = row['EMAIL'].split('/')[0].strip || '' if row['EMAIL'].present? && row['EMAIL'] != 'NO EMAIL'
    employment_status = check_employment_status('Active')

    employees = Employee.kept.where('last_name ilike ? and first_name ilike ?', ('%' + last_name), (first_name + '%')) if last_name.present? && first_name.present?

    if employees.present?
      feed_errors('Member was already created')
    else
      employee = Employee.new
      employee['first_name'] = first_name
      employee['last_name'] = last_name
      employee['apartment'] = apartment
      employee['city'] = city
      employee['state'] = state
      employee['zipcode'] = zipcode

      if employee.save
        personal_phone_contact = employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE)
        personal_phone_contact.value = cell_phone if cell_phone
        personal_phone_contact.save!

        employee.contacts.new(contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE).save!
        employee.contacts.new(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE).save!

        personal_email_contact = employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL)
        personal_email_contact.value = personal_email if personal_email.present?
        personal_email_contact.save!

        employee.contacts.new(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL).save!

        employee.employee_employment_statuses.new(employment_status_id: employment_status.id).save(validate: false) if employment_status
      else
        @errors[@row_number] = employee.errors.full_messages
      end
    end

  rescue => e
    p @row_number, e.message
    feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_member_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

task :nyshbca_update_contact_details, [:account] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  employees = Employee.kept
  @errors = {}

  if employees.present?
    employees.each do |employee|
      @row_number = employee
      contacts = employee.contacts

      if contacts.present?
        work_phone = contacts.find_by(contact_type: "phone", contact_for: "work")
        personal_phone = contacts.find_by(contact_type: "phone", contact_for: "personal")
        work_email = contacts.find_by(contact_type: "email", contact_for: "work")
        personal_email = contacts.find_by(contact_type: "email", contact_for: "personal")

        if work_phone.present? && work_phone.value.present?
          personal_phone.update_columns(value: work_phone.value) if personal_phone.present? && personal_phone.value.blank?
        end

        if work_email.present? && work_email.value.present?
          personal_email.update_columns(value: work_email.value) if personal_email.present? && personal_email.value.blank?
        end
      end

    rescue => e
      p @row_number, e.message
      feed_errors(e.message)
    end
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_member_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

def check_employment_status(employment_status_type)
  return nil unless employment_status_type.present?

  employment_status = EmploymentStatus.where(name: employment_status_type).first_or_create

  employment_status

rescue => e
  feed_errors('EMPLOYMENT_STATUS ' + employment_status.errors.full_messages)
end

def nyshbca_parse_phone(phone)
  return nil unless phone.present?
  return nil if phone.length < 10

  phone.remove!(/\D/).first(10)

  phone_number = '(' + phone.first(3) + ') ' + phone[3..5] + ' - ' + phone.last(4)

  phone_number

rescue => e
  feed_errors('PHONE ' + e.message)
end

def feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end
