# frozen_string_literal: true

desc 'Split first_name initial to middle'
task :nyccoba_split_first_name_initial_to_middle, [:account] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  errors = {}

  Employee.kept.find_each do |employee|
    name = employee.first_name.split # Sample: VANEL J ABBOTT

    if name.length < 2 || employee.middle_name.present?
      employee.update_column(:middle_name, employee.middle_name.delete('.')) if employee.middle_name&.include?('.')
      next
    end

    middle_initials = name.grep(/^[A-Za-z]\.?$/)

    employee.update_columns(first_name: (name - middle_initials).join(' '), middle_name: middle_initials.map { |i| i.delete('.') }.join(' ')) if middle_initials.present? && employee.middle_name.blank?
  rescue StandardError => e
    errors[employee.first_name] = e.message
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Time.now.to_i}name_split_errors_.csv", 'w') do |csv|
    csv << %w[Employee Error]
    errors.each { |id, message| csv << [id, message] }
  end
end

task :nyccoba_add_benefits_and_members_into_benefit_coverages, %i[account file_path type] => :environment do |_t, args|
  # type = 1 -> add benefits to members
  # type = 2 -> add members to benefits_coverages
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  @errors = {}
  benefits_start_date = Date.new(2025, 5, 30)
  benefits = Benefit.kept.where('lower(name) IN (?)', %w[dental optical prescription])
  csv_file.each do |row|
    name = row['Member Name']
    @row_name = "#{name}"
    first_name, last_name, middle_name = split_name_nyccoba(name.split)
    if first_name.blank? || last_name.blank?
      @errors[@row_name] = 'first_name or last_name is blank'
      next
    end
    if middle_name.present?
      employees = Employee.kept.where('lower(first_name) LIKE ? AND lower(last_name) LIKE ? AND lower(middle_name) LIKE ?', "%#{first_name.downcase}%", "%#{last_name.downcase}%", "%#{middle_name&.downcase}%")
      employees = Employee.kept.where('lower(first_name) LIKE ? AND lower(last_name) LIKE ?', "%#{first_name.downcase} #{middle_name&.downcase}%", "%#{last_name.downcase}%") if employees.empty?
      employees = Employee.kept.where('lower(first_name) LIKE ? AND lower(last_name) LIKE ?', "%#{first_name.downcase}%", "%#{middle_name&.downcase} #{last_name.downcase}%") if employees.empty?
    else
      employees = Employee.kept.where('lower(first_name) = ? and lower(last_name) = ?', first_name.downcase, last_name.downcase)
    end

    if employees.many?
      store_error('More than one employee found')
      next
    elsif employees.none?
      store_error('Employee not found')
      next
    else
      employee = employees.first
    end
    if args[:type] == '1'
      create_benefits(benefits, employee, benefits_start_date)
    elsif args[:type] == '2'
      create_benefit_coverages(benefits, employee, benefits_start_date)
    end
  rescue StandardError => e
    p @row_name, e.message
    @errors[@row_name] = e.message
  end
  CSV.open("#{Rails.root}/#{args[:account]}_#{Time.now.to_i}_benefits_errors.csv", 'w') do |csv|
    csv << %w[Employee Error]
    @errors.each { |id, message| csv << [id, message] }
  end
end

def create_benefits(benefits, employee, appointment_date)
  benefits.each do |benefit|
    return employee.employee_benefits.create!(benefit_id: benefit.id, start_date: appointment_date) unless employee.employee_benefits.kept.where(benefit_id: benefit.id).present?
  end
rescue StandardError => e
  p @row_name, e.message
  store_error(e.message)
end

def create_benefit_coverages(benefits, employee, benefits_start_date)
  benefits.each do |benefit|
    employee_benefit = employee.employee_benefits.kept.where(benefit_id: benefit.id).first
    employee_benefit = create_benefits([benefit], employee, benefits_start_date) unless employee_benefit.present?

    next if employee_benefit.benefit_coverages.kept.where(employee_id: employee.id, first_name: employee.first_name, last_name: employee.last_name, relationship: 'member', dependent: 0).first
    employee.benefit_coverages.create!(employee_benefit_id: employee_benefit.id, first_name: employee.first_name, last_name: employee.last_name, suffix: employee&.suffix, relationship: 'member',
                                       dependent: 0, birthday: employee&.birthday, gender_id: employee&.gender_id, effective_date: employee&.start_date)
  end
rescue StandardError => e
  p @row_name, e.message
  store_error(e.message)
end

def split_name_nyccoba(name)
  if name.length == 2
    first_name = name[0]
    last_name = name[1]
  elsif name.length == 3
    middle_name = name[1]
    first_name = name[0]
    last_name = name[2]
  else
    middle_name = name[1..-2].join(' ')
    first_name = name[0]
    last_name = name.last
  end
  [first_name, last_name, middle_name]
end

def store_error(message)
  if @errors[@row_name]
    @errors[@row_name] << message
  else
    @errors[@row_name] = [message]
  end
end

task :nyccoba_get_employee_stauts_count_and_dependents_count, %i[account] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  end_date = Date.new(2024, 12, 31)

  results = []
  employees = Employee.kept.joins(employee_employment_statuses: :employment_status).where(
    '(employee_employment_statuses.end_date is NULL OR employee_employment_statuses.end_date <= ?) AND employee_employment_statuses.created_at <= ?', end_date, end_date
  )

  dependents = BenefitCoverage.kept.joins(employee_benefit: { employee: { employee_employment_statuses: :employment_status } })
                              .where('benefit_coverages.created_at <= ?', end_date)
                              .where('(employee_benefits.end_date IS NULL OR employee_benefits.end_date <= ?) AND employee_benefits.created_at <= ?', end_date, end_date)
                              .where('benefit_coverages.employee_id IN (?)', employees.pluck(:id).uniq)
  ignore_dependent_ids = []
  user_audit_data = UserAudit.where(item_type: 'BenefitCoverage')
                             .where('created_at > ?', end_date)
                             .where("object_changes LIKE '%expires_at%'")
                             .where(item_id: dependents.pluck(:id))
                             .order(created_at: :asc)

  user_audit_data.each do |audit|
    previous_state = YAML.safe_load(audit.object,permitted_classes: [Time, Date, Symbol], aliases: true)
    previous_expires_at = previous_state['expires_at']
    ignore_dependent_ids << audit.item_id if previous_expires_at && previous_expires_at <= end_date
  end

  # Remove dependents from the list who have been expired before end_date
  dependents = dependents.where.not(id: ignore_dependent_ids.uniq) if ignore_dependent_ids.any?

  active_dependents = dependents.where('lower(employment_statuses.name) = ?', 'active')
  results << "Total no of Active Members: #{employees.where('lower(employment_statuses.name) = ?', 'active').count}"
  results << "Total no of Active Members Spouses: #{active_dependents.where('LOWER(benefit_coverages.relationship) = ?', 'spouse').group(:first_name, :last_name, :employee_id).count.size}"
  results << "Total no of Active Members Domestic Partners: #{active_dependents.where('LOWER(benefit_coverages.relationship) = ?', 'domestic_partner').group(:first_name, :last_name, :employee_id).count.size}"
  results << "Total no of All Type of Active Members Child: #{active_dependents.where('LOWER(benefit_coverages.relationship) in (?)', %w[child disabled_child]).group(:first_name, :last_name, :employee_id).count.size}"
  results << "Total no of Active Members Child: #{active_dependents.where('LOWER(benefit_coverages.relationship) = ?', 'child').group(:first_name, :last_name, :employee_id).count.size}"
  results << "Total no of Active Members Disabled Child: #{active_dependents.where('LOWER(benefit_coverages.relationship) = ?', 'disabled_child').group(:first_name, :last_name, :employee_id).count.size}"
  results << "Total no of Active Members Students: #{active_dependents.where('benefit_coverages.student = ?', true).group(:first_name, :last_name, :employee_id).count.size}"

  retired_dependents = dependents.where('lower(employment_statuses.name) = ?', 'retired')
  results << "Total no of Retired members : #{employees.where('lower(employment_statuses.name) = ?', 'retired').count}"
  disability_retired_dependents = dependents.where('lower(employment_statuses.name) = ?', 'disability retired')
  results << "Total no of Disability Retired Members: #{employees.where('lower(employment_statuses.name) = ?', 'disability retired').count}"
  results << "Total no of Retired Members Spouses: #{retired_dependents.where('LOWER(benefit_coverages.relationship) = ?', 'spouse').group(:first_name, :last_name, :employee_id).count.size}"
  results << "Total no of Retired Members Domestic Partners: #{retired_dependents.where('LOWER(benefit_coverages.relationship) = ?', 'domestic_partner').group(:first_name, :last_name, :employee_id).count.size}"
  results << "Total no of All Type of Retired Members Child: #{retired_dependents.where('LOWER(benefit_coverages.relationship) in (?)', %w[child disabled_child])
                                                                                 .group(:first_name, :last_name, :employee_id).count.size}"
  results << "Total no of Retired Members Child: #{retired_dependents.where('LOWER(benefit_coverages.relationship) = ?', 'child').group(:first_name, :last_name, :employee_id).count.size}"
  results << "Total no of Retired Members Disabled Child: #{retired_dependents.where('LOWER(benefit_coverages.relationship) = ?', 'disabled_child').group(:first_name, :last_name, :employee_id).count.size}"
  results << "Total no of Retired Members Students: #{retired_dependents.where('benefit_coverages.student = ?', true).group(:first_name, :last_name, :employee_id).count.size}"
  results << "Total no of Disability Retired Members Spouses: #{disability_retired_dependents.where('LOWER(benefit_coverages.relationship) = ?', 'spouse').group(:first_name, :last_name, :employee_id).count.size}"
  results << "Total no of Disability Retired Members Domestic Partners: #{disability_retired_dependents.where('LOWER(benefit_coverages.relationship) = ?', 'domestic_partner')
                                                                                                       .group(:first_name, :last_name, :employee_id).count.size}"
  results << "Total no of All Type of Disability Retired Members Child: #{disability_retired_dependents.where('LOWER(benefit_coverages.relationship) in (?)', %w[child disabled_child])
                                                                                                       .group(:first_name, :last_name, :employee_id).count.size}"
  results << "Total no of Disability Retired Members Child: #{disability_retired_dependents.where('LOWER(benefit_coverages.relationship) = ?', 'child').group(:first_name, :last_name, :employee_id).count.size}"
  results << "Total no of Disability Retired Members Disabled Child: #{disability_retired_dependents.where('LOWER(benefit_coverages.relationship) = ?', 'disabled_child')
                                                                                                    .group(:first_name, :last_name, :employee_id).count.size}"
  results << "Total no of Disability Retired Members Students: #{disability_retired_dependents.where('benefit_coverages.student = ?', true).group(:first_name, :last_name, :employee_id).count.size}"

  CSV.open("#{Rails.root}/nyccoba_employment_status_count.csv", 'w') do |csv|
    results.each do |result|
      csv << [result]
    end
  end
end

# bundle exec rake 'nyccoba_add_benefits_and_members_into_benefit_coverages[nyccoba,nyccoba_benefits.csv,2]'
# bundle exec rake 'nyccoba_get_employee_stauts_count_and_dependents_count[nyccoba]'
