# frozen_string_literal: true

task :nyccoba_get_non_retiree, %i[account file_path] => :environment do |t,args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  @errors = {}
  new_csv_data = []
  csv_headers = csv_file.headers
  csv_file.each do |row|
    first_name = row['First Name']
    last_name = row['Last Name']
    @row_number = first_name + "_" + last_name
    shield_number = row['Shield #'].split(//).map {|x| x[/\d+/]}.compact.join("") if row['Shield #'].present?
    employees = []

    employees = Employee.kept.where('shield_number = ?', shield_number) if shield_number.present?
    employees = Employee.kept.where("lower(first_name) = ? AND lower(last_name) = ?", first_name&.downcase, last_name&.downcase) if employees.empty?

    if employees.count > 1
      nyccoba_feed_errors('More than One Employee Found')
      next
    end
    if employees.empty?
      nyccoba_feed_errors('Employee not found')
      next
    end
    employee = employees.first.employment_statuses&.joins(:employee_employment_statuses).where("LOWER(employment_statuses.name) = ? and ( employee_employment_statuses.end_date IS NULL or employee_employment_statuses.end_date  > ?)", "retired", Date.today)
    if employee.empty?
      updated_row = row.to_h
      new_csv_data << updated_row
    end
  rescue StandardError => e
    p @row_number, e.message
    nyccoba_feed_errors(e.message)
  end
  generate_csv_report_errors(t.name + '_')

  new_file_path = "#{Rails.root}/nyccoba_non_retiree_errors#{Time.new.strftime('%d-%m-%Y-%H:%M')}.csv"
  CSV.open(new_file_path, 'w', write_headers: true, headers: csv_headers) do |csv|
    new_csv_data.each { |row| csv << csv_headers.map { |header| row[header] } }
  end
  puts "CSV file generated at: #{new_file_path}"
end

task :nyccoba_update_ref_num_and_union_status, %i[account file_path] => :environment do |t,args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  @errors = {}

  csv_file.each do |row|
    name = row['Member Name']
    ssn = nyccoba_parse_ssn(row['SSN']) if row['SSN'].present?
    ref_number = row['Reference #']
    status_name = row['Union Status']&.strip
    if status_name.present?
      union_status = EmploymentStatus.where('lower(name) = ?', status_name.downcase).first_or_create!(name: status_name)
    end

    @row_number = ssn
    employees = Employee.kept.where('social_security_number = ?', ssn)
    if employees.count > 1 || employees.empty?
      first_name, last_name = split_employee_name(name)
      employees = Employee.kept.where("lower(first_name) = ? AND lower(last_name) = ?", first_name&.downcase, last_name&.downcase)
    end

    if employees.count > 1 || employees.empty?
      nyccoba_feed_errors('More than One Employee Found') if employees.count > 1
      nyccoba_feed_errors('Employee not found') if employees.empty?
      next
    end

    employee = employees.first
    if ref_number.present?
      employee.update_columns(a_number: ref_number)
      next
    end

    if row['Union Status Date'].present?
      if row['Union Status Date'] == "1"
        union_status_date = employee.start_date
      else
        union_status_date = nyccoba_parse_date(row['Union Status Date'])
      end
    end

    if union_status.blank? || union_status_date.blank?
      nyccoba_feed_errors('Union Status is blank') if union_status.blank?
      nyccoba_feed_errors('Union Status Date is blank') if union_status_date.blank?
      next
    end

    if employee.employee_employment_statuses.where(employment_status_id: union_status.id).blank?
      last_status = EmployeeEmploymentStatus.kept.where!(employee_id: employee.id, end_date: nil).last
      last_status.update!(end_date: union_status_date) if last_status.present?

      if last_status.present? && last_status.errors['Date range'].present?
        nyccoba_feed_errors('Date range is invalid', " - could not update the end date of the status(#{last_status.name})")
      end

      employee.employee_employment_statuses.create!(employment_status_id: union_status.id, start_date: union_status_date)
    else
      nyccoba_feed_errors('Union Status already present')
    end

  rescue => e
    p @row_number, e.message
    nyccoba_feed_errors(e.message)
  end
  generate_csv_report_errors(t.name + '_')
end

def nyccoba_parse_date(date)
  return nil unless date.present?

  parsed_date = nil

  begin
    date_array = date.split('/')
    day = date_array[1].to_i
    month = date_array[0].to_i
    year = date_array[2].to_i

    if year.to_s.length == 2
      year = ('20' + year.to_s).to_i
    elsif year.to_s.length == 1
      year = ('200' + year.to_s).to_i
    elsif year.to_s.length > 4
      raise 'Invalid Year'
    else
      year = year.to_i
    end

    parsed_date = Date.new(year, month, day)
  rescue
    parsed_date = Date.parse(date)
  end

  parsed_date

rescue => e
  nyccoba_feed_errors('DATE - ' + " #{date} " + e.message)
end

# bundle exec rake 'nyccoba_get_non_retiree[nyccoba,nyccoba_data_file_path.csv]'
# bundle exec rake 'nyccoba_update_ref_num_and_union_status[nyccoba,update_ref_nyccoba.csv]'