# frozen_string_literal: true

require 'csv'

task :nyccoba_sms_opt_out, %i[account file_path] => :environment do |t, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  @errors = {}

  csv_file.each do |row|
    @row_number = row['Member Name'] || ''
    name = @row_number.split

    first_name = name.first&.downcase
    last_name = nil
    middle_name = nil

    case name.length
    when 2
      last_name = name.last.downcase
    when 3
      if name[1].length == 1
        middle_name = name[1].downcase
        last_name = name[2].downcase
      else
        last_name = name[1..].join(' ').downcase
      end
    when 4
      if name.any? { |part| %w[JR JR. III COOPER].include?(part.upcase) }
        if name.length >= 4
          middle_name = name[1].downcase
          last_name = name[2..].join(' ').downcase
        end
      else
        middle_name = name[1].downcase
        last_name = name[2..].join(' ').downcase
      end
    end

    employees = Employee.kept.where('lower(first_name) = ? AND lower(last_name) = ?', first_name, last_name)

    if employees.count > 1 && middle_name
      employees = Employee.kept.where('lower(first_name) = ? AND lower(last_name) = ? AND lower(middle_name) = ?', first_name, last_name, middle_name)
    end

    if employees.blank?
      nyccoba_feed_errors('Employee not Found')
      next
    elsif employees.count > 1
      nyccoba_feed_errors('More than One Employee Found')
      next
    end
    employee = employees.first
    employee.update_columns(sms_opt_out: true, updated_at: Time.now) if employee
  rescue StandardError => e
    p @row_number, e.message
    nyccoba_feed_errors(e.message)
  end
  generate_csv_report_errors(t.name + '_')
end

task :nyccoba_sms_and_email_opt_out, %i[account] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  Employee.joins(:employment_statuses).where("LOWER(employment_statuses.name) IN (?)", [ "active deceased", "deceased", "retired deceased", "terminated", "resigned", "promoted" ]).find_each do |employee|
    employee.update_columns(sms_opt_out: true, email_opt_out: true)
  end
end

task :nyccoba_schedule_sms, %i[account file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  @errors = {}
  employees_ids = []

  csv_file.each do |row|
    first_name = row['FIRST NAME'] || ''
    last_name = row['LAST NAME'] || ''
    cell_phone = row['CELLPHONE'] || ''
    city = row['CITY'] || ''
    zip = row['ZIP'] || ''
    email = row['EMAIL'] || ''
    @row_number = "#{first_name} #{last_name}"
    next if first_name.blank? || last_name.blank?

    employees = Employee.kept.where("REPLACE(LOWER(first_name), ' ', '') = ? AND REPLACE(LOWER(last_name), ' ', '') = ?", first_name.downcase.delete(' '), last_name.downcase.delete(' '))
    employees = employees.where("REPLACE(LOWER(city), ' ', '') = ? AND REPLACE(LOWER(zipcode), ' ', '') = ?", city.downcase.delete(' '), zip.downcase.delete(' ')) if employees.count > 1

    if employees.blank? || employees.count > 1
      @errors[@row_number] = "Employee not found" if employees.blank?
      @errors[@row_number] = "More than one employee found" if employees.count > 1
      next
    end
    contacts_phone = employees.first.contacts.where(contact_for: 'work', contact_type: 'phone').first
    contacts_email = employees.first.contacts.where(contact_for: 'work', contact_type: 'email').first

    if contacts_phone.present? && cell_phone.present?
      parsed_phone = nyccoba_parse_phone(cell_phone)
      contacts_phone.update_columns(value: parsed_phone) if parsed_phone.present?
    end
    if contacts_email.present? && email.present? && email.match?(URI::MailTo::EMAIL_REGEXP)
      contacts_email.update_columns(value: email)
    end
    employees_ids << employees.first.id
  end

  subject = "IMMEDIATE ACTION REQUIRED:  YOU STILL HAVE NOT SIGNED REQUIRED FORMS FOR FLSA LAWSUIT (HARRIS V NYC), 8/22/25 DEADLINE APPROACHING"

  email_message = %(
<div style="font-family: Arial, sans-serif; font-size: 14px; line-height: 1.6; color: #000;">
  <p>YOU ARE RECEIVING THIS MESSAGE ONLY BECAUSE YOU:<br>
  - Signed up for Stokes v. NYC, and<br>
  - Failed to sign up for Harris v. NYC</p>

  <p>All Stokes COs have been merged into the overtime pay lawsuit for Correction Officers, Harris v. City of New York, filed by COBA’s selected firm, McGillivary Steele Elkin LLP (MSE).</p>

  <p>Under the stipulation entered by the Parties and ordered by the Court, Plaintiffs must provide either their SSN or Employee ID to the City by 8/22/25.</p>

  <p>The City has informed your attorneys that it will not retrieve the data necessary to compute your back pay unless your attorneys provide your Employee ID number or SSN. Please complete the attached retainer form by clicking the link below ASAP.</p>

  <p>The form includes space to provide your SSN and/or Employee ID number and will officially retain the law firms on the same terms as the other 2,500+ COs who have already signed up for the litigation.</p>

  <p>Your Employee ID number is NOT your shield number — it is the number listed on your pay stub as "Reference #".</p>

  <p>Failure to provide this information means the City can refuse to produce the data necessary to compute damages for you. You could lose out on settlement monies if the parties reach a settlement. The parties are currently working toward settlement, and your lawyers do not want to exclude you based on missing information.</p>

  <p><strong>Link:</strong> <a href="https://bit.ly/cobadoc2">https://bit.ly/cobadoc2</a></p>

  <p>Sincerely,<br>
  Benny Boscio<br>
  President</p>
</div>
)


  message_to = "YOU ARE RECEIVING THIS MESSAGE ONLY BECAUSE YOU: \r\n
    - Signed up for Stokes v. NYC, and\r\n
    - Failed to sign up for Harris v. NYC\r\n
    All Stokes COs have been merged into the overtime pay lawsuit for Correction Officers, Harris v. City of New York, filed by COBA’s selected firm, McGillivary Steele Elkin LLP (MSE).\r\n
    Under the stipulation entered by the Parties and ordered by the Court, Plaintiffs are required to provide either their SSNs or Employee IDs to the City by 8/22/25
    The City has informed your attorneys that it will not pull the data necessary to compute your backpay if your attorneys do not provide your Employee ID number or SSN. Please complete the attached retainer form by clicking the link below ASAP.\r\n
    The form includes a space for you to provide your SSN and/or EE ID Number, and officially retains the law firms on the same terms as the other 2500 (plus) COs who have signed up for the litigation.\r\n
    Your Employee ID number is NOT your shield number — it is the number listed on your pay stub as Reference #. \r\n
    Failure to provide the information means that the City can refuse to produce the data necessary to compute damages for you. You could lose out on settlement monies if the parties reach a settlement. The parties are in the process of trying to settle the case and your lawyers do not want to have to exclude you from the settlement based on your failure to provide necessary information\r\n
    Link: https://bit.ly/cobadoc2\r\n\r\n
    Sincerely,\r\n
    Benny Boscio\r\n
    President"


  scheduled_at = Time.use_zone("America/New_York") do
    Time.zone.local(2025, 8, 14, 9, 0, 0)
  end

  notification = Notification.create!(
    subject: subject,
    email: true,
    email_message: email_message,
    sms: true,
    sms_message: message_to,
    is_scheduled: true,
    sms_to: "work",
    email_to: "work",
    scheduled_date: scheduled_at.to_date,
    scheduled_time: scheduled_at.strftime("%H:%M:%S"),
    filters: { "employee_ids": employees_ids }
  )
  scheduled_at = Notification.utc_time(notification.scheduled_date, notification.scheduled_time)
  NotificationJob.perform_later(notification.id, args[:account])


  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_nyccoba_schedule_sms.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]
    @errors.each do |error|
      csv << error
    end
  end
  rescue => e
    p @row_number, e.message
end


# bundle exec rake 'nyccoba_sms_opt_out[nyccoba, nyccoba_sms_opt_out_file.csv]'
# bundle exec rake 'nyccoba_sms_and_email_opt_out[nyccoba]'
# bundle exec rake 'nyccoba_schedule_sms[nyccoba, nyccoba_sms.csv]'
