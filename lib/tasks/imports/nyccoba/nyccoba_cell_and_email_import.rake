# frozen_string_literal: true

require 'csv'

desc 'import Cell and Email'

task :nyccoba_cell_and_email_update, [:account, :file_path, :cell_or_email] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  cell_or_email = args[:cell_or_email] || ''

  @errors = {}
  @value_downcase = ->(x) { x&.downcase&.delete(' ') }
  contacts_hash = [
    { contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE, value: '' },
    { contact_for: Contact::ContactFor::COLLEAGUE, contact_type: Contact::ContactType::EMERGENCY, value: '' },
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMERGENCY, value: '' },
    { contact_for: Contact::ContactFor::PERSO<PERSON><PERSON>, contact_type: Contact::ContactType::PHONE, value: '' },
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL, value: '' },
    { contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE, value: '' },
    { contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL, value: '' }
  ]

  csv_file.each do |row|
    more_employee_found = false
    first_name = row['First Name'] || ''
    last_name = row['Last Name'] || ''
    street = row['Address'] || ''
    city = row['City'] || ''
    state = row['State'] || ''
    zip = row['Zip'] || ''

    @row_number = "#{first_name} #{last_name}"
    employees = Employee.kept.includes(:contacts).where("lower(first_name) LIKE ?  and lower(last_name) LIKE ?", "%#{first_name.downcase}%", "%#{last_name.downcase}%")
    if employees.blank? && last_name.split.count > 1
      last_name = last_name.split.join('-')
      employees = Employee.kept.includes(:contacts).where("lower(first_name) LIKE ?  and lower(last_name) LIKE ?", "%#{first_name.downcase}%", "%#{last_name.downcase}%")

    elsif employees.count > 1
      more_employee_found = true
      employees = Employee.kept.includes(:contacts).where("lower(first_name) LIKE ?  and lower(last_name) LIKE ? and
                                  lower(street) LIKE ? and lower(city) LIKE ? and  lower(state) LIKE ? and zipcode = ?", "%#{first_name.downcase}%",
                                                          "%#{last_name.downcase}%", "%#{street.downcase}%", "%#{city.downcase}%", "%#{state.downcase}%", "#{zip}")
    end

    if employees.blank?
      nyccoba_feed_errors('More than one employee found with name.Then no member found with name and address') if more_employee_found == true
      nyccoba_feed_errors('No member found') if more_employee_found == false
      next
    elsif employees.count > 1
      nyccoba_feed_errors('More than One employee found')
      next
    end

    employee = employees.first
    if employee.contacts.blank?
      contacts_hash.last[:value] = row['Email Address'] || '' if cell_or_email == 'email' && row['Email Address'].present?
      contacts_hash.last(2).first[:value] = nyccoba_parse_phone(row['Cellphone']) || '' if cell_or_email == 'cell' && row['Cellphone'].present?
      # CONTACTS
      employee.contacts.import contacts_hash
    else
      if cell_or_email == 'email' && row['Email Address'].present?
        work_email = employee.contacts.where(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL).first_or_initialize
        work_email.value = row['Email Address'] || ''
        work_email.save
      elsif cell_or_email == 'cell' && row['Cellphone'].present?
        work_phone = employee.contacts.where(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE).first_or_initialize
        work_phone.value = nyccoba_parse_phone(row['Cellphone']) || ''
        work_phone.save
      end
    end

  rescue => e
    p @row_number, e.message
    nyccoba_feed_errors(e.message)
  end

  # Contact.import total_arr.flatten, on_duplicate_key_update: [:value]
  CSV.open("#{Rails.root}/nyccoba_#{cell_or_email}_update_errors_#{Time.new.strftime("%d-%m-%Y-%H:%M:%S")}.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']
    @errors.each do |error|
      csv << error
    end
  end
end

task :nyccoba_personal_phone_and_cell_update_to_work_phone_and_cell, [:account] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  @errors = {}
  Employee.kept.includes(:contacts).find_each do |employee|
    @row_number = "#{employee.first_name} #{employee.last_name}"

    contacts = employee.contacts
    personal_phone = contacts.select { |x| x.contact_for == 'personal' && x.contact_type == 'phone' }.first&.value
    personal_email = contacts.select { |x| x.contact_for == 'personal' && x.contact_type == 'email' }.first&.value
    work_phone = contacts.select { |x| x.contact_for == 'work' && x.contact_type == 'phone' }.first&.value
    work_email = contacts.select { |x| x.contact_for == 'work' && x.contact_type == 'email' }.first&.value

    if personal_phone.present? && work_phone.blank?
      orig_work_phone = employee.contacts.where(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE).first_or_initialize
      orig_work_phone.value = personal_phone
      orig_work_phone.save!
    end

    if personal_email.present? && work_email.blank?
      orig_work_email = employee.contacts.where(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL).first_or_initialize
      orig_work_email.value = personal_email
      orig_work_email.save!
    end

  rescue => e
    p @row_number, e.message
    nyccoba_feed_errors(e.message)
  end

  # Contact.import total_arr.flatten, on_duplicate_key_update: [:value]
  CSV.open("#{Rails.root}/nyccoba_personal_phone_and_cell_update_to_work_phone_and_cell_errors_#{Time.new.strftime("%d-%m-%Y-%H:%M:%S")}.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']
    @errors.each do |error|
      csv << error
    end
  end
end

task :nyccoba_claims_optical_usage_import, [:file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!('nyccoba')

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  benefit_disbursements_data = []
  count = 0

  csv_file.each do |row|
    optical_usage_date = row['OptionalUsage'] || ''
    next if optical_usage_date.blank?

    first_name = row['MemberFirstName'] || ''
    last_name = row['MemberLastName'] || ''
    dependent_f_name = row['FirstName'] || ''
    dependent_l_name = row['LastName'] || ''
    relationship = employee_relationship(row['Relation']) || ''
    ssn = parse_ssn(row['SSN']) || ''
    next if first_name.blank? || last_name.blank? || dependent_f_name.blank? || dependent_l_name.blank? || optical_usage_date.blank? || relationship.blank?

    @row_number = first_name + ' ' + last_name

    employees = Employee.kept.where(social_security_number: ssn) if ssn.length == 11
    employees = Employee.kept.where('lower(first_name) = ? and lower(last_name) = ?', first_name&.downcase, last_name&.downcase) if employees.nil?

    benefit_disbursement_data = validate_employee_count(employees, optical_usage_date, relationship, dependent_f_name, dependent_l_name)

    benefit_disbursements_data << benefit_disbursement_data if benefit_disbursement_data

    if count == 500
      BenefitDisbursement.import benefit_disbursements_data.flatten
      benefit_disbursements_data = []
      count = 0
    end
    count += 1
  rescue StandardError => e
    p @row_number, e.message
    nyccoba_feed_errors(e.message)
  end

  BenefitDisbursement.import benefit_disbursements_data.flatten
  generate_csv_report_errors('nyccoba_claims_optical_usage_import_errors')
end
# rubocop:disable Lint/SymbolConversion, Metrics/BlockLength
task :nyccoba_commands_cleanup, [:account] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  @errors = {}

  # changes_hash = { 'AMKC': 'AMKC - ANNA M. KROSS CENTER', 'BHPW': 'BHPW - BELLEVUE HOSPITAL PRISON WARD', 'BKCTS': 'BKCTS - BROOKLYN COURTS',
  #                  'BKDC': 'BKDC - BROOKLYN DETENTION COMPLEX', 'BXCTS': 'BXCTS - BRONX COURTS', 'CA': 'CA - CORRECTION ACADEMY',
  #                  'CIB & CIB - CORRECTION INTELLIGENCE BUREAU': 'CIB - CENTRAL INTELLIGENCE BUREAU', 'CID': 'CID - CORRECTION INDUSTRIES DIVISION',
  #                  'DCJC & DCJC - DONALD J. CRANSTON CENTER': 'DCJC - DONALD CRANSTON JUDICIAL CENTER', 'EHPW': 'EHPW - ELMHURST HOSP',
  #                  'EMTC': 'EMTC - ERIC M. TAYLOR CENTER', 'ESU': 'ESU - EMERGENCY SERVICE UNIT', 'FMRD': 'FMRD - FACILITY MAINTENANCE REPAIR DIVISION',
  #                  'GMDC': 'GMDC - GEORGE MOTCHAN DETENTION CENT', 'GRVC': 'GRVC - GEORGE R VIERNO CENTER', 'HDQTRS': 'HDQTRS - HEADQUARTERS',
  #                  'HDQTRS/AIU': 'HDQTRS/AIU - APPLICANT INVESTIGATION UNIT', 'HMD': 'HMD - HEALTH MANAGEMENT DIVISION', 'ID': 'ID - INVESTIGATION DIVISION',
  #                  'IG': 'IG - INSPECTOR GENERAL', 'K-9': 'K-9 - CANINE UNIT', 'MDC': 'MDC - MANHATTAN DETENTION COMPLEX', 'MDC/CTS': 'MDC/CTS - MANHATTAN DETENTION CENTER/CTS',
  #                  'NIC': 'NIC - NORTH INFIRMARY COMMAND', 'OBCC': 'OBCC - OTIS BANTUM CORR CENTER', 'QDC': 'QDC - QUEENS DETENTION COMPLEX', 'QNCTS': 'QNCTS - QUEENS COURTS',
  #                  'RMSC': 'RMSC - ROSE M SINGER CENTER', 'RNDC': 'RNDC - ROBERT N. DAVOREN CENTER' }

  # changes_hash =
  #   {
  #     'DCJC': 'DCJC - DONALD CRANSTON JUDICIAL CENTER',
  #     'DCJC - DONALD J. CRANSTON CENTER': 'DCJC - DONALD CRANSTON JUDICIAL CENTER',
  #     'SOD': 'SOD - SPECIAL OPERATIONS DIVISION',
  #     'EHPW': 'EHPW - ELMHURST HOSP',
  #     'TRANS': 'TRANS - TRANSPORTATION DIVISION',
  #     'VCBC': 'VCBC - VERNON C BAIN CENTER',
  #     'WF': 'WF - WEST FACILITY'
  #   }

  # changes_hash = {
  #   'AMKC': 'AMKC - ANNA M. KROSS CENTER',
  #   'BHPW': 'BHPW - BELLEVUE HOSPITAL PRISON WARD',
  #   'BKDC': 'BKDC - BROOKLYN DETENTION COMPLEX',
  #   'BXCTS': 'BXCTS - BRONX COURTS',
  #   'CID': 'CID - CORRECTION INDUSTRIES DIVISION',
  #   'DCJC': 'DCJC - DONALD CRANSTON JUDICIAL CENTER',
  #   'EHPW': 'EHPW - ELMHURST HOSP',
  #   'EMTC': 'EMTC - ERIC M. TAYLOR CENTER',
  #   'ESU': 'ESU - EMERGENCY SERVICE UNIT',
  #   'FRMD': 'FMRD - FACILITY MAINTENANCE REPAIR DIVISION',
  #   'GMDC': 'GMDC - GEORGE MOTCHAN DETENTION CENTER',
  #   'GRVC': 'GRVC - GEORGE R VIERNO CENTER',
  #   'HDQTS': 'HDQTRS - HEADQUARTERS',
  #   'HMD': 'HMD - HEALTH MANAGEMENT DIVISION',
  #   'K-9': 'K-9 - CANINE UNIT',
  #   'MDC': 'MDC - MANHATTAN DETENTION COMPLEX',
  #   'NIC': 'NIC - NORTH INFIRMARY COMMAND',
  #   'OBCC': 'OBCC - OTIS BANTUM CORR CENTER',
  #   'QDC': 'QDC - QUEENS DETENTION COMPLEX',
  #   'QNCTS': 'QNCTS - QUEENS COURTS',
  #   'RMSC': 'RMSC - ROSE M SINGER CENTER',
  #   'RNDC': 'RNDC - ROBERT N. DAVOREN CENTER',
  #   'SOD': 'SOD - SPECIAL OPERATIONS DIVISION',
  #   'TRANS': 'TRANS - TRANSPORTATION DIVISION',
  #   'VCBC': 'VCBC - VERNON C BAIN CENTER'
  # }

  changes_hash = {
    'EHPW': 'EHPW - ELMHURST HOSP',
    'FMRD': 'FMRD - FACILITY MAINTENANCE REPAIR DIVISION'
  }

  whodunnit_present_users = UserAudit.where('whodunnit IS NOT NULL and item_type = ?', 'EmployeeOffice').pluck(:item_id)

  changes_hash.each do |key, value|
    old_commands = Office.kept.where(name: key.to_s)
    new_commands = Office.kept.where(name: value.to_s)
    @row_number = old_commands.first&.name || key

    old_command = old_commands.first
    new_command = new_commands.first

    if old_commands.count > 1 || new_commands.count > 1
      @row_number = new_command&.name if new_command.count > 1
      nyccoba_feed_errors('More than one command found with the same name')
      next
    elsif old_commands.blank? || new_commands.blank?
      @row_number = value if new_command.blank?
      nyccoba_feed_errors('No command found')
      next
    end

    employee_offices = old_command.employee_offices.where.not(office_id: whodunnit_present_users)
    employee_offices&.update_all(office_id: new_command&.id)

    if old_command.employee_offices.present? || old_command.delegate_assignments.present? || old_command.lodis.present? ||
      old_command.peshes.where('discarded_at IS NULL').present?
      nyccoba_feed_errors('There are associated values to the members, So ignoring the command deletion')
      next
    end
    old_command.discard if old_command.employee_offices.blank? && old_command.delegate_assignments.blank? && old_command.lodis.blank? && old_command.peshes.where('discarded_at IS NULL').blank?
  end

  generate_csv_report_errors('commands_cleanup_errors')
end

task :nyccoba_commands_import, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  offices_hash = {}
  inactive_members_hash = {}
  employee_ids = []
  active_status_id = EmploymentStatus.kept.where('lower(name) = ?', 'active').first.id
  a = ["CANDECIA M AIKEN", "MARVIN BIDO", "BRENDA N BYRNE", "KELLEY COTTON", "JOEL A CRUZ RAMIREZ", "JOHNPAUL A FAGBEMI", "SYED M IMRAN", "LATWANN JACKSON BLACK", "ALI REZA KALHOR-MOHAMADI", "THEOPHILUS OWUSU BEMPAH", "SEAN A PERVIS", "WILKENS PHEBE", "CHRISSDENICE RODRIGUEZ ARVEL", "ALEXANDER SANCHEZ GALINDO", "WILLIAM SIFONTE JR", "VITO SUMMA I I I", "ASHANTI N TERRELL", "SHEKIYA R DONEGAN", "JUSTIN N GALEAS", "ASHRAF HOSSAIN", "SAMUEL O OTOO", "JOHNNY A RAMOS HIDALGO", "DOUGLAS M ROBINSON", "KEILA Y BROWN", "KIM GANNAWAY", "CAMINA N JONES", "CHERYL AUSTIN", "CHARLES A GRANT", "SOORIYAKUMAR PONNUSAMY", "JOE V STANISLAUS", "SHAMEEKA STYLES", "DARREN WISHER", "CASANDRA CARABALLO", "SAMANTHA FRASER", "DEAN B BAHMASSEY JR", "JANELLE ABEL AYARZA", "LATISHA L BUNN", "DEBRA K ALBERT-COLLINS", "THOMAS J ALTIERI JR", "IVAN R BERMUDEZ", "ROBERT BOYSA", "LAMAR DAWKINS", "LLOYD EDMONDSON", "DAVID LAROSE", "WINSTON A MCLEOD JR", "JACLYN MONTERROSO", "LERON MORGAN", "CARLOS OLIVAR", "ERNESTINE PEPPERS", "LYSTRA REYES", "D'ANGELO RICHARDSON JR", "SHANENE TAYLOR", "SHARLENE E WHILBY", "DWAYNE WORRELL", "ONEIL J WRIGHT", "JOSEPH B ABRAHAM JR.", "AJA AUGUSTE", "AKERI CAMMOCK", "ALISHA L FOX", "MONIQUE A GOODE", "YOLANDA GOODWIN - WILLI", "MARSHA N MANGAROO-JOHNSO", "SEAN MCCULLOUGH", "MICHAEL MININNI", "SYRENA MITCHELL", "HERIBERTO PAGAN, JR.", "APRIL F QUICK", "CARRIE A SAMPSON", "RENARTA D WARREN", "MORRIS F WILSON JR", "ANTHONY D WORTHY SR", "SUSAN M BECKFORD", "CLARICE L BLACKWELL", "LTANYA K CHARLES", "NEFERTITI DUNLAP", "REGINA T ELLIS", "REGINALD L FORDE JR", "EBONY FORT", "EMIEL HENRY", "ROBERT HEYWARD III", "RAYNELL JACOBS", "O'NEIL M LAWSON", "LISONYA NICHOLAS", "LEROY TOLLIVER JR", "EUGENIA TORRES", "MICHAEL VASQUEZ", "HENRY J RAFFERTY IV", "JOSEPH WONG", "ANNE-MARIE CRONIN", "SHERRIE S PATTERSON-WIGGI", "JI M LI", "IDONNA T AVENT", "FEDE J BARTHELEMY", "TANISHA BELL", "PERAKYAH I BOZEMAN", "YADALIS F BURROWES BECKFO", "ALEXIS CALHOUN", "MICHAEL CAMACHO JR", "SKYE CANNON", "MARIA T CHAVEZ-DAMSKY", "EDGARDO CLAUDIO", "COURTNEY Q COLBERT-DAVIS", "RUSSELL D COLEMAN", "JAMES COSTANTINO JR", "TRUDY ANN V DAVIS", "CHELIN J DE LOS SANTOS", "ELSA EVANS-CINTRON", "RUTH FANDAL HODGE", "DANIELLE GUASE", "JELYNA D GUTIERREZ", "LAZETTE F HARRIS", "ESSENCE HUNT", "NICOLE S JONES-STURDIVAN", "NGINA LAWRENCE", "DAVID P LINDSAY", "SIMMONE R MANIGAULT-HARRI", "MONICA M MATIS", "KEOSHA C MAYNARD", "LAURIE MESTRE", "STEVIE MUNLYN", "ERIC CLAUDE H NELSON", "DANIELLE T ONEAL", "ERICA A OSAFO", "FANNY OSBORNE", "ADALGISA R PIMENTEL", "JOSE PUMAREJO", "EDDIE RICHBURG IV", "JOSUE SAINT VIL", "RICHARD S SISTI JR", "XAVIER SMITH", "TAMIA O ST. HILAIRE", "THURMAN C THOMPSON JR", "RICARDO D VENDRYES FLEMIN", "SAMANTHA J HICKMAN", "KEVIN REYNOLDS", "LORENA ARCE", "KASIA BENOIT--JONES", "CAMERON H CALLENDER JR", "IRWIN DE LEON NUNEZ", "STACY M DESOUZA", "HECTOR H ESTRELLA JR", "CHARLENE FERRERAS", "ELIZABETH FLORES-CRUZ", "DONNA FRANCOIS", "HECTOR L GONZALEZ JR", "DAVID L GRANT", "ROMARIO S GRANT", "SHAWN HARRIS", "YONEL S JASMIN JR", "JONATHAN JEAN BAPTISTE", "QUALEIK L JOHN", "ASHAKIE P KING", "BARRY E LEE, JR.", "JOYMARIE LEWISHOLLIDAY", "ATHENA C LINTONMCDERMOTT", "DAVID LOPEZ", "KHAIM RAJ MANGROO", "CHRISTINA MC SWEEN", "NIKITA MORRIS", "LINDA NOEL", "DERICK E OKVIST JR", "ANTHONY OLIVIER", "ERIC OREILLY", "JOSHUA I PITA CACPATA", "RICK E PURNHAGEN III", "OSVALDO M REYES CRESPO", "FRANCKY J RIBOUL", "O'ZELLE D RICKETTS", "PAOLA RIQUELME", "ANDREW ROBERTS BOLAN", "RICHARD SABBAT", "HAL N SCHOLNICK", "NEHRU SMITH", "MARIA C C SOTO", "JOVAN ST HILAIRE", "JONATHON A SUTHERLAND", "KEYON D SYLVESTER", "BENJAMIN F WALKER JR", "KEVIN T WHITE JR", "NICKY-LEE WILLIAMS", "CASSANDRA ZEPHIR-HOPKINS", "ELISSIA S BROWN", "AIME ACEVEDO NUNEZ", "TIFFANI A HOPE", "PASCAL A LUGO", "SATHARON RAMSANIE", "KENNETH HOOD_II", "JAIVON R MARTIN SR", "CHRISTINE J TORRES", "KATRINA J THOMAS", "KRISTLE N PHILLIP", "ASHANTE O JAMES", "MARILYN M CALLEJA", "LARHONDA N GREEN", "TRACY M WILLIAMS-JOHNSO", "SHAMICA A CHARLES", "SHENEKE N COFIELD", "DAVID W MCGIBBON JR", "NOVLET A THOMPSON", "PHILLIP WHITE", "CLIFTON A BROOKS, JR.", "SHYREMIA MARTIN", "EMMA WILLIAMS", "CORRIN N ALEXIS", "ANTOINETTE ANDERSON", "JOSE DE JESUS", "ANDREW K HAMER", "ROBERT G OBRIEN", "AMADO RICO", "KEISHA R WILLIAMS", "MALGORZATA HERNANDEZ", "JENNIE THOMPSON", "CORLIN VALDEZ SALDIVAR", "LESLIE BRUNSON", "CHRISTOPHER DALEY", "LANEL S HARDY", "ISAURA LEITES", "PAULETTE A RUDDER", "JORGE TORRES JR", "TORY WHITE", "RAYMOND ROSA", "ENELYNE BEAUTE", "MELVIN HERNANDEZ", "SHAKUWRA MALCOLM", "MELONY J PEREZ", "KENYETTA U DAVIS", "KARY GUTIERREZ", "RICHARD L HENRY II", "DEION E ISLAR", "JOHNATHAN K NEWTON", "KATHRINA RICHARDSON", "JAMES E ROBINSON", "CHIMERE L SPAULDING", "MAGDERLENE A SPENCER", "LASHA C TABBS", "JESSICA A ANTENOR", "ANGEL L BERNARD II", "JAMES S BRITO SR", "KYANNA BROWNE", "ARIANNE M BRYAN", "JOHN CASTILLO", "KHALILAH COOPER", "NIURCA DE LA ROSA", "KELLEY FERGUSON", "ROSALYN GLOVER", "LE SHELLE G HALLETT", "BRITT'NEE K HOLLOWAY", "CHRISTOPHER T JACKSON JR", "ANTWAN L KING", "ALEKSANDR LISICHKIN", "JULIO A LOZADA", "ANDRES PORTES JR", "THALIA PRESCOTT", "CELSO RAMOS", "BRIAN SMITH JR", "FRANCISCO TORRES", "GLENN L TRAMMELL JR.", "STEVEN WALKER", "WICKLIFFE WILKINS", "E. ALEXANDER C BAEZ", "PHILIP AGER", "RENE ALVAREZ JR.", "MARIA AUDELO", "CAVOHDYAH BEN-LEVI", "WILLIE BILLINGSLEA", "TARNIA D BURRELL", "VANESSA CASTILLO-ESTEVE", "TRAMEL CLARKE", "ODALIS COLON RODRIGUEZ", "TANOYA COPELAND", "ROSEMARY E COWAN", "LUZ M CRUZ", "ANTHONY DAPOLITO", "GIANA P DISLA", "ROBERT L DUNBAR III", "GARY S DYER SR", "OZANIROGHOMWEN EDORISIAGBON", "L' TAYA FAMBLE-GRIER", "MONIQUE G GLAIZE-BAILEY", "LATESSHA GOVAN-CRUZ", "DAVID L HAYNIE JR.", "NAOMIE HILAIRE", "PHILLIPE JACKSON", "BERTHONY JEANLOUIS", "ANTHONY JONES", "NATALIE JONES", "LESLIE-ANN LAWRENCE", "INGRID Y MACHADO", "NATIAH MAYO", "ALONSO V MORGAN JR", "HUMPHREY PEREZ JR", "QUEEN R PETRUS", "DIANNE PETTIFORD", "MOISES A QUEZADA ALCANTA", "KENYWN RICHARDS", "MERCEDES ROMERO LINO", "IMELDA RUVALCABA-MARIN", "DENETRA RYANS", "THOMAS SINGLETON III", "LASHON SINGLETON WILLI", "YAMILE ST FLEUR", "LUIS SUAREZ", "AJANA E TYSON", "MD ZIHAD Z UDDIN", "KENNETH D WILLIAMS", "TEKOA WILLIAMS", "JAMES WILLIAMS JR", "TONIANN WYMAN", "URELLA P ALEXANDER KING", "JASON SKEET", "SHANA HARRISON", "DIONNE D LASSITER", "JOHANIS M MOLINA RODRIGUE", "TAMMIE ARCHIBALD", "CETSHWAYO MARS-D'AUGUSTIN", "JAMAL ABEL", "SHALENA ALLEN", "YASMIN ALONZO", "RODNEY D ANDERSON JR", "JOHN ANTONETTI", "HARLAND A ARISTDE", "MARCELIS ASENCIO DE CARO", "ROSE D ATTIMY LAFORTUN", "MOHAMMAD AZAD", "SHAULETTA BAPTISTE", "TANYA G BLACKWOOD-WATKI", "DOMINIQUE BROCKMAN", "WILLIAM COLCLOUGH", "ROBERT R CROCKER III", "SALVATORE DAMICO", "CESAR DELACRUZ", "KOJO F DENTE", "DONALD DERRICK JR", "ZEILA A FERRERAS FELIX", "VINCENT J FIRSCHING JR", "PROCULO F FUENTES IV", "AMIYRAH O GAY FELICIAN", "MICHAEL J GIOVANNIELLO JR", "SHIRLONDRA T GOLDEN", "GERALDINE L GOMEZ", "JAMILLIAH GORDON", "KHADIJA O GRENION GROSSET", "ALEXANDRE J GUIRAND JR", "LADREAMA M HUMBERT", "SHONTA L JOHNSON", "KIMBERLLY KNUPP CID", "FRANKLIN M LEGER JR", "SHARELLA S LESLIE", "KEITH LEWIS", "FELIPE A MENDEZ RODRIGUE", "CLIFTON L MIGNOTT, JR", "JHULIAN K NEWELL-LITTLE", "SHANTE A ORR", "LORENZO OVERTON-JEFFERS", "NORDIA L POWELL", "ASHLEY RANDOLPH", "MONICA P RECALDE - ANASI", "KIMBERLY L STEPHENS", "MALIKAH W STEPNEY", "TAYLOR A THOMAS- GASPAR", "TAMIKA J THOMPSON", "ALCIBIADES VARGAS MEDINA", "ISAIAH A WITHERSPOON-BRO", "VANEL ABBOTT", "PETER GERESTANT", "JOHN R CRAWLEY JR.", "STACEY JOHNSON", "APRIL KIPP", "ONIEL D LINTON", "MIRIAM V MCCLEAN-DAVIDSO", "SUSAN N MOHR", "MORRIS SMITH", "SHENIQUA SMITH", "SHAQUANA THOMAS", "TE-NESSIA CASTILLO-RICHMO", "ERICA CORULLA", "EDITH I DE LA ROSA", "LEONCIO DIAZ JR", "ISAAC O DILBERT JR", "RUBEN D ENCARNACION-ROD", "TYNESHA M GRAY", "ANTHONY MORALES JR", "SAYQUAN SELBY", "TYREE SHAW", "JEROME WILLIAMS-TUTHIL", "JULIANNA AGOSTINELLI", "NATASHA AGU", "TASHA M ANDERSON", "JENNIFER J AZCONA", "CAROLINE BOLANOS-MATTHEW", "SHERIKA BROWN", "VIOLETA A CHILLOUS", "SCHIVAUGHN CONNOR", "EVA COOK", "TAMARA A DAVIS", "MELODY ENCARNACION ROD", "JASMINE J GOSS", "HOLDYA A GRANT PAUL", "MICHELLE D HALL", "TRACY-ANN M HARRIS", "NEKSHA C JOHNSON", "NANCY LALANNE-RAMDAWA", "STEPHANIE LAMOTHE-AUGUSTI", "LAMONT LOWRY", "NISHAUN MCCALL", "EILEEN MCKENNA", "SHERIKA MCKENZIE-CAMPBE", "LAWRENCE N MEDAS JR.", "JACOB MOORE", "HELENA J OKVIST", "MARIA PEREZ", "BIANCA R RICHBERG-HILL", "SHANTAL T ROONEY", "JOSEPH ROSSI", "KISHA SAMUELS", "ADRIENNE A THOMAS", "STEPHANIE M THOMAS", "CHAUNTEL L WALTERS", "REMONA WARREN", "CHISA T WHITEHURSTMCCOY", "DEANNA WOLFE", "SANTOS BARBOSA JR", "SYDNEY BARRINGTON III", "TERRI BARTHOLOMEW-SMA", "RODRICK BEAU", "MICHELLE BEERSINGH", "CHRYSTAL J BEESON", "ANITA BROOKS", "TERRELL P BROOKS-MCEACHER", "JACKIE BURRELL", "TROHN G CARSWELL SR", "ANGELA L CENTENO", "JARED A CHAVEZ", "DWAYNE C COCKBURN", "ANGELA CUNNINGHAM", "AVERILL E DEFOUR", "KERRI A DEJESUS", "STEPHANIE C DOMINICI-ROSARI", "GARRICK B ELLISTON JR", "CATHERINE S FLOYD-DIAZ", "SAMANTHA D GAMBINO", "JOHALYS C GOMEZ RODRIGUEZ", "CHRISTINA GREGORY", "DAKENYA M HARRIS", "APRIL HEFNEY KOSAKOWS", "AUDREY JOHNSON", "LATEAISHA K JOHNSON", "KEVIN JONES", "SHARON M JORDON", "STEPHON M LEONARD JR", "ANNY C MARTE CUELLO", "RICHARD J MCCONICO", "AARON MCLEAN", "STJOHN MCMILLAN", "MICHAEL MODESTE", "HANIF MOLLAH", "ROBERT PARCHMENT JR", "ALVARINE A PATTERSON-BRYAN", "MELISSA C PAUL", "ASIA POWELL", "EVELYN RAMOS", "SADE Y RATLIFF MCLEOD", "OTIVEAN RILES", "REINALDO RIVERA, JR", "LISSETTE RODRIGUEZ", "AAKAWENE P RUDDERHENRY", "HICKMAN SIMMONS", "MIKAL ST HILAIRE", "JAMES STANTON", "EDWARD STONE", "ROBERT J WALTERS", "KAMIYIHA S WEST PHILLIPS", "NICOLE M WHYTE", "SIJIAN WU", "DRAKE E ATKINS II", "JOHN D'ANTONIO", "DEANA S HICKS", "CRYSTAL JONES", "ALEKSANDR GALUZEVSKIY", "ERASMO ALMANZAR", "CHRISTIAN BACIGALUPO", "AMBROSE BENAIN JR", "MICHAEL A BRENNAN JR.", "DIANNE S BROWN", "GARY CLARKE SR", "JOYCE DAVIS", "WILSON DELOSSANTOS", "WILLIAM DRIVERBENJAMIN", "RUBENS FAYETTE", "ADAM FIER", "ERIC FRAZIER", "WILNER GUILLAUME JR", "JACQUELINE HEWITT-SANTANA", "KIRK HYLTON", "DANIEL JOHNSON", "JASON JOSEPH-PAULINE", "JOHN KROHLEYIII", "FELIX A LOPEZ JR", "PATRICK LOUIS JEAN", "LAWRENCE J MCARDLE JR.", "ROBERT MCDONALD JR", "MALCOLM MCQUILLAR", "FRANTZLINE MOISE", "JEFFREY D NOAILLES", "NOREEN OCONNOR", "NISHANDA L PEGUES", "LLOYD ROBERTS JR", "CECILIA M ROLLISON-TAYLOR", "HECTOR ROMAN", "MATEO RUIZ JR", "JEAN R SOUFFRANT", "WILLIAM R WILLIAMS III", "LAKESHIA Y STUART", "FATEMA L ALVAREZ", "NOVELETTE ASTWOOD", "JEAN-ROODLEY BARON", "BYRON BROWN", "ANTONIO CASILLAS JR", "GABRIEL CHERY JR", "TIANA K EPPS", "JUAN GONZALEZ JR", "RONALD E HUTCHISON", "KERLY JEANLOUIS", "ONEIL JOHNSON", "JOSE L MAISONET", "MICHAEL MCCABE", "SEAN C NIXON II", "MOSES N PETERS", "ADAM RAMIREZ", "NELSON RAMOS JR.", "STEVEN RENTAS JR", "DAVID SALA DELLA CUNA", "STACEY SHILLINGFORD", "LESLIE SIMMONS", "CHRISTIAN WALTON", "KENYETTA J WILLIAMS", "JOHN R WISTI JR", "KEITH G DAWKINS", "CARLOS G DIAZ JR", "MARSHA A CUSAAC GRANT", "KRZYSZTOF P JEDNAK"]

  @value_downcase = ->(x) { x&.downcase&.delete(' ') }

  csv_file.each do |row|
    next if row['FIRST NAME'].blank? || row['LAST NAME'].blank?

    first_name = (row['FIRST NAME']).strip || ''
    last_name = (row['LAST NAME']).strip || ''
    @row_number = "#{first_name} #{last_name}"

    employees = Employee.kept.includes(:employee_offices).where('lower(first_name) = ? and lower(last_name) = ?', first_name.downcase, last_name.downcase)

    if employees.blank? || employees.count > 1
      first_name_with_dot = "#{first_name}."
      dob = Date.parse(row['DATE OF BIRTH'])
      shield_number = row['SHIELD NO']
      first_name = first_name.split.first
      employees = Employee.kept.includes(:employee_offices).where('(lower(first_name) = ? and lower(last_name) = ?) OR
                                  (lower(first_name) = ? and lower(last_name) = ?)', first_name.downcase, last_name.downcase,
                                                                  first_name_with_dot.downcase, last_name.downcase)

      employees = Employee.kept.includes(:employee_offices).where('(lower(first_name) = ? and lower(last_name) = ? and birthday = ?) OR
                                  (lower(first_name) = ? and lower(last_name) = ? and birthday = ?)', first_name.downcase, last_name.downcase, dob,
                                                                  first_name_with_dot.downcase, last_name.downcase, dob) if employees.blank? || employees.count > 1

      employees = Employee.kept.includes(:employee_offices).where('shield_number = ?', shield_number) if employees.blank? || employees.count > 1
      employees = Employee.kept.includes(:employee_offices).where('shield_number = ? and birthday = ?', shield_number, dob) if employees.count > 1 || employees.blank?
    end

    if employees.blank?
      nyccoba_feed_errors('No member found')
      next
    elsif employees.count > 1
      nyccoba_feed_errors('More than one members found')
      next
    end
    employee = employees.first
    employee_ids << employee.id

    next unless a.include?(@row_number)

    # COMMANDS
    command_id = if row['CITY-TIME LOCATION ASSIGNED'].present? && offices_hash[@value_downcase.call(row['CITY-TIME LOCATION ASSIGNED']).to_sym].present?
                   offices_hash[@value_downcase.call(row['CITY-TIME LOCATION ASSIGNED']).to_sym]
                 elsif row['CITY-TIME LOCATION ASSIGNED'].present?
                   create_offices_hash('Office', row['CITY-TIME LOCATION ASSIGNED'], offices_hash)&.id
                 end
    if command_id.present?
      employee_office_start_date = Date.parse(row['DOC HIRE DATE']) if row['DOC HIRE DATE'].present?
      employee_office = employee.employee_offices.new(office_id: command_id, start_date: employee_office_start_date)
      employee_office.save!(validate: false)
      employee.employee_offices.where.not(id: employee_office.id).update_all(discarded_at: Time.now) if employee_office.id.present?
    end
    inactive_members_hash[employee.id] = employee.social_security_number unless employee.employment_status_name.downcase.split(', ').include?('active')
  end

  all_active_members = Employee.kept.joins(:employee_employment_statuses).where('employee_employment_statuses.employment_status_id = ?
                              AND (employee_employment_statuses.end_date is NULL OR employee_employment_statuses.end_date > ?)', active_status_id, Date.today).select(:id, :first_name, :last_name, :social_security_number)
  all_active_members_id = all_active_members.pluck(:id)
  active_members_not_file_id = all_active_members_id - employee_ids
  active_members_ids_and_file_ids = (all_active_members_id + employee_ids).uniq
  active_members_ids_and_file_ids -= inactive_members_hash.keys

  CSV.open("#{Rails.root}/active_members_not_present_in_the_file.csv", 'w') do |csv|
    csv << ['Employee Name', 'SSN']

    all_active_members.where(id: active_members_not_file_id).each do |member|
      csv << ["#{member.first_name} #{member.last_name}", member.social_security_number]
    end
  end

  CSV.open("#{Rails.root}/inactive_members_in_the_file.csv", 'w') do |csv|
    csv << ['Employee Name', 'SSN']

    Employee.kept.where(id: inactive_members_hash.keys).each do |member|
      csv << ["#{member.first_name} #{member.last_name}", member.social_security_number]
    end
  end

  # EmployeeOffice.kept.where.not(employee_id: active_members_ids_and_file_ids).where('start_date IS NULL and end_date is NULL').update_all(start_date: Date.yesterday, end_date: Date.yesterday)
  # EmployeeOffice.kept.where.not(employee_id: active_members_ids_and_file_ids).where('start_date IS NOT NULL and end_date is NULL').update_all(end_date: Date.yesterday)

  generate_csv_report_errors('commands_import_errors_')
end

task :nyccoba_commands_import_new_members, %i[account file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  offices_hash = {}
  employee_ids = []
  inactive_members_hash = {}
  active_status_id = EmploymentStatus.kept.where('lower(name) = ?', 'active').first.id

  @value_downcase = ->(x) { x&.downcase&.delete(' ') }

  csv_file.each do |row|
    next if row['Shield #'].blank? || (row['First Name'].blank? && row['Last Name'].blank?)

    first_name = (row['First Name']).strip || ''
    last_name = (row['Last Name']).strip || ''
    @row_number = "#{first_name} #{last_name}"
    shield_number = row['Shield #'] || ''
    city = row['City'] || ''
    state = row['State'] || ''
    zip = row['Zip'] || ''
    employees = Employee.kept.includes(:employee_employment_statuses).where(shield_number: shield_number)

    if check_any_errors(employees)
      first_name_last_name_subquery = 'lower(first_name) = ? and lower(last_name) = ?'
      employees = Employee.kept.includes(:employee_offices).where(first_name_last_name_subquery.to_s, first_name.downcase, last_name.downcase)
      employees = Employee.kept.includes(:employee_offices).where("#{first_name_last_name_subquery} and lower(city) = ? and lower(state) = ? and zipcode = ?",
                                                                  first_name.downcase, last_name.downcase, city.downcase, state.downcase, zip) if check_any_errors(employees)
    end

    next if check_any_errors(employees, true)

    employee = employees.first
    employee_ids << employee.id
    inactive_members_hash[employee.id] = employee.social_security_number unless employee.employment_status_name.downcase.split(', ').include?('active')

    # COMMANDS
    command_id = if row['Command'].present? && offices_hash[@value_downcase.call(row['Command']).to_sym].present?
                   offices_hash[@value_downcase.call(row['Command']).to_sym]
                 elsif row['Command'].present?
                   create_offices_hash('Office', row['Command'], offices_hash)&.id
                 end
    next if command_id.blank?

    nyccoba_feed_errors('More than one Command Found') if employee.employee_offices.count > 1
    nyccoba_feed_errors('No commands found') if employee.employee_offices.blank?
    next if employee.employee_offices.count > 1 || employee.employee_offices.blank?

    old_command = employee.employee_offices.first
    employee_office = employee.employee_offices.new(office_id: command_id, start_date: old_command.start_date)
    employee_office.save!(validate: false)
    employee.employee_offices.where.not(id: employee_office.id).update_all(discarded_at: Time.now) if employee_office.id.present?
  end
  all_active_members = Employee.kept.joins(:employee_employment_statuses).where('employee_employment_statuses.employment_status_id = ?
                              AND (employee_employment_statuses.end_date is NULL OR employee_employment_statuses.end_date > ?)', active_status_id, Date.today).select(:id, :first_name, :last_name, :social_security_number)
  all_active_members_id = all_active_members.pluck(:id)
  active_members_not_file_id = all_active_members_id - employee_ids

  CSV.open("#{Rails.root}/active_members_not_present_in_the_file.csv", 'w') do |csv|
    csv << ['Employee Name', 'SSN']

    all_active_members.where(id: active_members_not_file_id).each do |member|
      csv << ["#{member.first_name} #{member.last_name}", member.social_security_number]
    end
  end

  CSV.open("#{Rails.root}/inactive_members_in_the_file.csv", 'w') do |csv|
    csv << ['Employee Name', 'SSN']

    Employee.kept.where(id: inactive_members_hash.keys).each do |member|
      csv << ["#{member.first_name} #{member.last_name}", member.social_security_number]
    end
  end

  generate_csv_report_errors('commands_import_errors_')
end

task :nyccoba_shield_number_import, %i[account file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  csv_file.each do |row|
    next if row['First Name'].blank? || row['Last Name'].blank?

    first_name = (row['First Name']).strip || ''
    last_name = (row['Last Name']).strip || ''
    @row_number = "#{first_name} #{last_name}"
    shield_number = row['Shield #'] || ''
    city = row['City'] || ''
    state = row['State'] || ''
    zip = row['Zip'] || ''

    first_name_last_name_subquery = 'lower(first_name) = ? and lower(last_name) = ?'
    employees = Employee.kept.where(first_name_last_name_subquery.to_s, first_name.downcase, last_name.downcase)
    employees = Employee.kept.where("#{first_name_last_name_subquery} and lower(city) = ? and lower(state) = ? and zipcode = ?",
                                                                first_name.downcase, last_name.downcase, city.downcase, state.downcase, zip) if check_any_errors(employees)

    next if check_any_errors(employees, true)

    employee = employees.first
    employee.update_columns(shield_number: shield_number)

  end
  generate_csv_report_errors('shield_number_import_errors_')
end

# rubocop:enable Lint/SymbolConversion, Metrics/BlockLength

task :nyccoba_benefits_cleanup, [:account] => [:environment] do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  @errors = {}

  benefit_hash = employee_benefit_hash

  ActiveRecord::Base.transaction do
    benefit_hash.each do |new_benefit_name, existing_benefits|
      if new_benefit_name == 'REMOVE'
        existing_benefits.each do |existing_benefit|
          @row_number = existing_benefit
          old_benefit = Benefit.kept.find_by(name: existing_benefit)
          if old_benefit.blank?
            nyccoba_feed_errors('Benefit not found')
            next
          end

          discard_benefit_with_conditions(old_benefit)
        end
      else
        new_benefit = Benefit.kept.find_or_create_by(name: new_benefit_name)
        process_existing_benefits(existing_benefits, new_benefit)
      end
    end
  rescue StandardError => e
    p @row_number, e.message
    nyccoba_feed_errors(e.message)
  end

  report_name = "#{args[:account]}_benefits_cleanup_"
  generate_csv_report_errors(report_name)
end
def employee_benefit_hash
  {
    'AUDIOLOGY' => ['ADIOLOGY', 'AUD', 'AUDILOGY', 'AUDIO', 'AUDIOLGY', 'AUDIOLLOGY', 'H. AID', 'HEAR AID', 'HEARING AI', 'HEARINGAID', 'HEARX'],
    'AMBULANCE' => %w[AM AMBULATORY AMBULENCE AMULANCE],
    'ANESTHESIA' => ['ANAETHESIA', 'ANALGESIA', 'ANASTHESIA', 'ANASTHIA', 'ANATHESIA', 'ANES', 'ANES DENT', 'ANESETHIA', 'ANESRHESIA', 'ANES SON', 'ANES/SON', 'ANEST', 'ANEST DENT', 'ANESTH', 'ANESTHEIA', 'ANESTHESA',
                     'anesthetist', 'ANESTHIA', 'ANESTHISIA', 'ANESTHRSIA', 'ANESTHSIA', 'ANES UNPD', 'ANETHESIA', 'ANSTHESIA', 'ANTHESIA'],

    'Dental' => ['ARTHO', 'ARTHO DEN', 'DANTAL', 'DENAL', 'DEN APP', 'DEN/CHILD', 'DEN/CHILD.', 'DEN-ELAINE', 'DEN EST', 'DEN FAX', 'DEN-MONIQ', 'DEN PRE', 'DENRAL', 'DEN RE', 'DEN/SON', 'DEN/SOUSE', 'DEN SP',
                 'DEN/SPOUS', 'DENSPOUSE', 'DEN-SPOUSE', 'DEN/SPOUSE', 'DENT', 'DENT.', 'DENTA;', 'DENTAL;', 'DENTAL.', 'DENTAL\'', 'DENTAL]', 'DENTAL\\', 'DENTAL+', 'DENTAL APP', 'DENTAL CARD', 'DENTAL-CHR',
                 'DENTAL-CJR', 'DENTAL-DAN', 'DENTAL DUP', 'DENTAL-MAR', 'DENTAL-MIC', 'DENTAL-PJR', 'DENTAL PRE', 'DENTAL PRT', 'DENTAL SO', 'DENTAL-SO', 'DENTAL-SON', 'DENTAL SP', 'DENTAL-SP', 'DENTAL(SP)',
                 'DENTAL SR', 'PRE-DEN', 'PRE DENT', 'PRE DENTAL', 'PRE-DENTAL', 'PRE/DENTAL', 'PRE\DENTAL', 'DENTAL S.R', 'DENTAL TR', 'DENT ANEST', 'DENT ARTHO', 'DENT/CHILD', 'DENTEL', 'DENTIST', 'DENTL',
                 'DENT ORTH', 'DENT ORTHO', 'DENT PRE', 'DENTSAL', 'DEN XRAYS', 'DEN X-RAYS', 'DENYAL', 'DEP/CHILD', 'DERNTAL', 'DE/SPOUSE', 'DETAL', 'DETNAL', 'DNETAL', 'DN/SPOUSE', 'DNTAL', 'DRE DENT', 'DRNTAL',
                 'DWNTAL', 'ORTHO', 'ORTHOTICS', 'RESUB DENT', 'RESUB-DENT', 'RESUBMIT D'],
    'Prescription' => ['DRG/CHILD', 'DRG/SON', 'DRG/SPOUSE', 'DRUG', 'DRUGH', 'DRUG PLAN', 'DRUGS', 'DRUGS/MED.', 'DRU/SPOUSE', 'MEDICATION', 'PRE. DRUG', 'PRES.', 'PRESC', 'PRESC.', 'PRES. CARD', 'PRESCIPT',
                       'PRESCRIP', 'PRESCRIP.', 'PRESCRIPT', 'PRESCRIPT.', 'PRESCRIPTI', 'PRES. DRUG', 'RX', 'RX********'],
    'XRAY' => ['FULL MT XR', 'X-RAY', 'X-RAYS'],
    'HOSPITAL' => ['HOS BILL', 'HOSP BILL', 'HOSP CLAIM'],
    'NURSERY' => ['NUR BILL', 'NURSEY', 'NURSEY CLAIM', 'NURSURY'],
    'Optical' => ['OOPTICAL', 'OPICAL', 'OPITCAL', 'OP ND REQ', 'OPOTICAL', 'OPRICAL', 'OPT', 'OPT.', 'OPTCAL', 'OPT-CHERYL', 'OPT/CHILD', 'OPT CLAIM', 'OPT DUP', 'OPT-FRANCI', 'OPT-GLADYS', 'OPT-HEATHE', 'OPTIAL',
                  'OPTICA', 'OPTICA;', 'OPTICAL;', 'OPTICAL.', 'OPTICAL RE', 'OPTICAL-SO', 'OPTICAL VO', 'OPTICLAL', 'OPTI/SPOUS', 'OPT-KATHY', 'OPT-LENORE', 'OPT-MIKE', 'OPT-NANCY', 'OPT REIM', 'OPT SECREQ', 'OPT-SP',
                  'OPT-SPOUSE', 'OPT/SPOUSE', 'OPT-TONY', 'OPYICAL', 'OPY-NICK', 'OTICAL', 'OTPICAL', 'POTICAL', 'CONT.LENSE'],
    'PRE-AUTHO' => ['PRE.', 'PRE APR', 'PRE ARTHO', 'PRE-AUTH', 'PRE/AUTH', 'PRE-AUTH D', 'PRE AUTHO', 'PRE AUTO', 'PRE-AUTO', 'PRECERT', 'PRE-CERT', 'PRECERT-SP', 'PRE-DET', 'PREDETER', 'PRE-DETER', 'PRE-EST',
                    'PRE- OP', 'PRE-OPT', 'PRE ORTHO', 'PRE OUTHO', 'PRE-TREAT', 'PRE-TREAT.'],
    'REMOVE' => ['ALUMINUM', 'APPROVAL', 'CATHERINE', 'CLAIM', 'CLAIMS', 'DUP', 'ESTIMATE', 'FDNY/EMS', 'HEALTH', 'NANCY', 'NO #', 'PART OFNUR', 'PEDIATRICS', 'PRIPHY', 'PROPH', 'PROPHY', 'RCT', 'REIM OPT', 'REISE',
                 'RE-TREAT.', 'SPOOUSE', 'SPOUSE', 'STOCKING', 'TERREL', 'TOTAL TO', 'WILLIAM']
  }
end

def discard_benefit_with_conditions(benefit)
  @row_number = benefit.name
  employee_benefits = benefit.employee_benefits.kept
  if employee_benefits.blank?
    benefit.discard
    return
  end

  can_discard_benefit = true

  employee_benefits.each do |employee_benefit|
    if employee_benefit.benefit_disbursements.exists? || employee_benefit.benefit_coverages.exists?
      nyccoba_feed_errors('Not deleted as Benefit coverages or Benefit disbursements associated with the benefit')
      can_discard_benefit = false
    else
      employee_benefit.discard
    end
  end

  benefit.discard if can_discard_benefit
end

def process_existing_benefits(existing_benefits, new_benefit)
  existing_benefits.each do |existing_benefit|
    @row_number = existing_benefit
    old_benefit = Benefit.kept.find_by(name: existing_benefit)
    if old_benefit.blank?
      nyccoba_feed_errors('Benefit not found')
      next
    end

    EmployeeBenefit.kept.where(benefit_id: old_benefit.id).update_all(benefit_id: new_benefit.id)
    old_benefit.discard
  end
end

def parse_ssn(ssn)
  return nil unless ssn.present?

  ssn.remove!('-', ' ')

  ssn_number = ssn.first(3) + '-' + ssn[3..4] + '-' + ssn.last(4) if ssn.length == 9

  ssn_number
end

def employee_relationship(relation)
  return nil unless relation.present?

  lowercase_relation = relation&.downcase
  case lowercase_relation
  when 'member', 'self'
    'self'
  when 'son', 'daughter'
    'child'
  else
    lowercase_relation = lowercase_relation.split(' ').join('_') if lowercase_relation.include?(' ')
    lowercase_relation
  end
end

def validate_employee_count(employees, optical_usage_date, relationship, dependent_f_name, dependent_l_name)
  if employees.count == 1
    employee = employees.first
    benefit_disbursement_hash(employee, optical_usage_date, relationship, dependent_f_name, dependent_l_name)
  elsif employees.count > 1
    nyccoba_feed_errors('More than one Employee Found')
    false
  else
    nyccoba_feed_errors('Invalid Employee or Employee was missing')
    false
  end
end

def benefit_disbursement_hash(employee, optical_usage_date, relationship, dependent_f_name, dependent_l_name)
  employee_benefit_id = employee.employee_benefits.joins(:benefit).kept.where(benefits: { name: 'Optical' }).pluck(:id).first
  if employee_benefit_id.present?
    benefit_coverage_id = employee.benefit_coverages.kept.where('lower(first_name) = ? and lower(last_name) = ? and employee_benefit_id = ?', dependent_f_name&.downcase, dependent_l_name&.downcase,
                                                                employee_benefit_id).pluck(:id).first
  end
  if benefit_coverage_id.present?
    benefit_disbursement_data = {
      employee_id: employee.id,
      date: optical_usage_date,
      employee_benefit_id: employee_benefit_id,
      relationship: relationship,
      benefit_coverage_id: benefit_coverage_id
    }
  end
  benefit_disbursement_data
end

def generate_csv_report_errors(name)
  CSV.open("#{Rails.root}/#{name}#{Time.new.strftime('%d-%m-%Y-%H:%M')}.csv", 'w') do |csv|
    csv << ['Employee Name', 'Errors']

    @errors.each do |error|
      csv << error
    end
  end
end

def nyccoba_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end

def create_offices_hash(model_name, value, hash)
  return unless value.present?

  if (model_value = model_name.constantize.kept.where('lower(name) = ?', value&.downcase)&.first).present?
    model_value
  else
    model_value = model_name.constantize.create!(name: value)
  end
  hash[@value_downcase.call(value).to_sym] = model_value.id if hash[value.to_sym].blank? || (hash[value.to_sym].present? && hash[value.to_sym] != model_value.id)
  model_value
end

def check_any_errors(employees, throw_errors_to_file = nil)
  any_errors = false
  if employees.blank?
    nyccoba_feed_errors('No member found') if throw_errors_to_file
    any_errors = true
  elsif employees.count > 1
    nyccoba_feed_errors('More than one members found') if throw_errors_to_file
    any_errors = true
  end
  any_errors
end

# # bundle exec rake "nyccoba_cell_and_email_update[nyccoba, nyccoba_missing_email_import.csv, email]"
# # bundle exec rake "nyccoba_cell_and_email_update[nyccoba, nyccoba_missing_cell_import.csv, cell]"
# bundle exec rake "nyccoba_personal_phone_and_cell_update_to_work_phone_and_cell[nyccoba]"
# bundle exec rails 'nyccoba_claims_optical_usage_import[nyccoba_optical_usage_claims_import.csv]'
# bundle exec rake 'nyccoba_commands_cleanup[nyccoba]'
# bundle exec rake 'nyccoba_commands_import_new_members[nyccoba, commands_import_may_24.csv]'