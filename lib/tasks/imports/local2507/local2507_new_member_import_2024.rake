# frozen_string_literal: true

require 'csv'

desc 'import data'

task :local2507_new_member_import_2024, %i[account file_path has_duplicates] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  active_status = EmploymentStatus.kept.where('lower(name) = ?', 'active').first
  employee_ids = []

  @errors = {}
  @contacts_hash = [
    { contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE, value: '' },
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE, value: '' },
    { contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE, value: '' },
    { contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL, value: '' },
    { contact_for: Contact::ContactFor::COLLEAGUE, contact_type: Contact::ContactType::EMERGENCY, value: '' },
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMERGENCY, value: '' },
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL, value: '' }
  ]
  Employee.skip_callback :save, :after, :update_benefit_address
  Employee.skip_callback :commit, :after, :update_legislative_detail
  EmployeeEmploymentStatus.skip_callback :commit, :after, :update_expiry_status

  csv_file.each do |row|
    first_name = row['First Name'] || ''
    last_name = row['Last Name'] || ''
    middle_name = row['Middle Name'] || ''
    city = row['City'] || ''
    state = row['State'] || ''
    zipcode = row['Zip'] || ''
    rank_id_excel = row['Titles'] || ''
    city_state_present = city.present? && state.present? && zipcode.present?

    @row_number = "#{first_name} #{last_name}"

    if first_name.blank? || last_name.blank?
      local2507_feed_errors('Mandatory Details Not present')
      next
    end

    employees = Employee.kept.where('lower(first_name) = ? and lower(last_name) = ?', first_name.downcase, last_name.downcase)
    employees = employees.where('lower(middle_name) = ?', middle_name.downcase) if middle_name.present?
    employees = employees.where('lower(city) = ? and lower(state) = ? and zipcode = ?', city.downcase, state.downcase, zipcode) if city_state_present && employees.count > 1

    if employees.blank?
      employees = [local2507_create_new_employee(row)]
    elsif employees.count > 1
      local2507_feed_errors('More than one employee found')
    end

    check_active_status(employees, active_status.id)
    check_employee_titles(employees, rank_id_excel)

    employee_ids << employees.pluck(:id)
  rescue => e
    p @row_number, e.message
    local2507_feed_errors(e.message)
  end

  export_names_of_other_employees(employee_ids.flatten, active_status)
  remove_duplicate_statuses if args[:has_duplicates] == 'true'

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_new_members_import_2024_errors.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']

    @errors.each do |error|
      csv << error
    end
  end

  Employee.set_callback :save, :after, :update_benefit_address
  Employee.set_callback :commit, :after, :update_legislative_detail
  EmployeeEmploymentStatus.set_callback :commit, :after, :update_expiry_status
end

task :local2507_deleting_duplicate_records, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  @errors = {}
  employee_ids = []
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  csv_file.each do |row|
    first_name = row['First Name'] || ''
    last_name = row['Last Name'] || ''
    city = row['City'] || ''
    state = row['State'] || ''
    zipcode = row['Zipcode'] || ''

    @row_number = "#{first_name} #{last_name}"

    employees = Employee.kept.where('lower(first_name) = ? and lower(last_name) = ? and lower(city) = ?
                              and lower(state) = ? and zipcode = ?', first_name.downcase, last_name.downcase, city.downcase, state.downcase, zipcode)
    next unless employees.count > 1

    count = 0
    employees.each do |member|
      contacts_value = member.contacts.pluck(:value)
      if contacts_value.any? { |x| x.present? }
        count += 1
        local2507_feed_errors('Both duplicated members populated Email or Cell') if count >= 2
        next
      else
        employee_ids << member.id
      end
    end
  end
  Employee.kept.where(id: employee_ids).discard_all

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_local2507_deleting_duplicate_records_errors.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']

    @errors.each do |error|
      csv << error
    end
  end
end

task :local2507_import_email_details, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  @errors = {}
  total_arr = []
  @contacts_hash = [
    { employee_id: '', contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE, value: '' },
    { employee_id: '', contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE, value: '' },
    { employee_id: '', contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE, value: '' },
    { employee_id: '', contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL, value: '' },
    { employee_id: '', contact_for: Contact::ContactFor::COLLEAGUE, contact_type: Contact::ContactType::EMERGENCY, value: '' },
    { employee_id: '', contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMERGENCY, value: '' },
    { employee_id: '', contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL, value: '' }
  ]
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  Employee.kept.includes(:contacts).where(contacts: { id: nil }).find_each do |employee|
    next if employee.contacts.present?

    contacts_hash_clone = @contacts_hash.deep_dup
    contacts_hash_clone.each { |x| x['employee_id'.to_sym] = employee.id }
    total_arr << contacts_hash_clone
  end

  Contact.import total_arr.flatten

  total_arr = []

  Employee.kept.joins(:contacts).group('employees.id').having('COUNT(contacts.id) < ?', 7).find_each do |employee|
    contacts_hash_clone = @contacts_hash.deep_dup
    contacts_hash_clone.each { |x| x['employee_id'.to_sym] = employee.id }
    contacts_hash_clone.each do |y|
      total_arr << y if employee.contacts.select { |x| x['contact_type'.to_sym] == y['contact_type'.to_sym] && x['contact_for'.to_sym] == y['contact_for'.to_sym] }.blank?
    end
  end

  Contact.import total_arr.flatten

  csv_file.each do |row|

    first_name = check_spaces(row['Name_First']) || ''
    first_name = first_name.split.first if first_name.split.count > 1
    last_name = check_spaces(row['Name_Last']) || ''

    @row_number = "#{first_name} #{last_name}"

    employees = Employee.kept.where("lower(first_name) LIKE ?  and lower(last_name) LIKE ?", "%#{first_name.downcase}%", "%#{last_name.downcase}%")
    next if row['Email'].blank?

    if employees.count > 1
      local2507_feed_errors('More than one Member Found')
      next
    elsif employees.blank?
      local2507_feed_errors('No Member Found')
      next
    end

    employee = employees.first
    contacts = employee.contacts.where(contact_type: 'email', contact_for: 'personal')
    contact = contacts.first

    next if contact.value.present?

    email = row['Email'] || ''
    contact.update_columns(value: email) if email.present?
  rescue => e
    p @row_number, e.message
    local2507_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_local2507_import_email_details_errors.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']

    @errors.each do |error|
      csv << error
    end
  end
end

task :local2507_import_cell_phone_details, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  @errors = {}
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  csv_file.each do |row|
    first_name = check_spaces(row['Name_First']) || ''
    first_name = first_name.split.first if first_name.split.count > 1
    last_name = check_spaces(row['Name_Last']) || ''

    @row_number = "#{first_name} #{last_name}"

    employees = Employee.kept.where("lower(first_name) LIKE ?  and lower(last_name) LIKE ?", "%#{first_name.downcase}%", "%#{last_name.downcase}%")
    next if row['Phone1'].blank?

    if employees.count > 1
      local2507_feed_errors('More than one Member Found')
      next
    elsif employees.blank?
      local2507_feed_errors('No Member Found')
      next
    end

    employee = employees.first
    contacts = employee.contacts.where(contact_type: 'phone', contact_for: 'personal')
    contact = contacts.first

    next if contact.value.present?

    phone = row['Phone1'] || ''
    parsed_phone = local2507_parse_phone(phone)
    contact.update_columns(value: parsed_phone) if parsed_phone.present?
  rescue => e
    p @row_number, e.message
    local2507_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_local2507_import_cell_phone_details_errors.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']

    @errors.each do |error|
      csv << error
    end
  end
end

task :local2507_update_change_request, [:account] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  ChangeRequest.where(status: 'pending', request_type: 'employee_section').find_each do |change_req|
    if change_req.requested_changes.any? { |change| change.key?('errors') }
      change_req.status = 'completed'
      change_req.requested_changes = change_req.requested_changes.map do |change|
        change.key?('errors') ? change.except('errors').merge('status' => 'completed') : change.merge('status' => 'completed')
      end
      change_req.save!
    end
  end
end

def check_employee_titles(members, rank_id_excel)
  rank_name = if %w[10024 31661 31662].include?(rank_id_excel)
                'FIRE INSPECTOR'
              elsif %w[53052 53053].include?(rank_id_excel)
                'EMT'
              elsif %w[53054].include?(rank_id_excel)
                'PARAMEDIC'
              end
  employee_rank_id = Rank.kept.where('lower(name) = ?', rank_name.downcase).first&.id
  employee_rank_id = Rank.create(name: rank_name).id if employee_rank_id.blank?
  members.each do |employee|
    employee_rank_ids = employee.employee_ranks.pluck(:rank_id)
    next if employee_rank_ids.include?(employee_rank_id) && employee_rank_ids.count == 1

    employee.employee_ranks.new(rank_id: employee_rank_id).save!
    employee.employee_ranks.where.not(rank_id: employee_rank_id).discard_all
  end
end

def check_active_status(members, active_status_id)
  members.each do |employee|
    employment_status_ids = employee.employee_employment_statuses.pluck(:employment_status_id, :end_date)
    next if employment_status_ids.include?([active_status_id, nil])

    employee.employee_employment_statuses.new(employment_status_id: active_status_id).save!
    local2507_feed_errors('Other than Active Statuses are present for this employee') if employment_status_ids.present?
    # employee.employee_employment_statuses.where.not(employment_status_id: active_status_id).discard_all
  end
end

def local2507_create_new_employee(row)
  employee = Employee.new(first_name: row['First Name'], last_name: row['Last Name'])
  employee.middle_name = row['Middle Name'] || ''
  employee.street = row['Address'] || ''
  employee.city = row['City'] || ''
  employee.state = row['State'] || ''
  employee.zipcode = row['Zip'] || ''
  employee.save!

  employee.contacts.import @contacts_hash

  employee
end

def local2507_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end

def discard_all_employees(employee_ids)
  ## Here we are updating the discarded_at manually because of the employee.discard_all deletes the record one by one and it takes more time.

  %w[Award Contact DelegateAssignment Discipline EmployeeEmploymentStatus EmployeeRank MailingAddress Lodi EmployeeDisciplineSetting Totality EmployeeFacility
     EmployeeGrievance EmployeeMeetingType EmployeeGrievance EmployeePacf EmployeeDepartment EmployeeSection EmployeePosition].each do |model|
    model.constantize.where.not(employee_id: employee_ids).update_all(discarded_at: Time.current)
  end
  Assault.where.not(employee_id: employee_ids).discard_all
  EmployeeDisciplineSetting.where.not(employee_id: employee_ids).discard_all
  Upload.where.not(employee_id: employee_ids).destroy_all
  Employee.where.not(id: employee_ids).update_all(discarded_at: Time.current, username: nil)
end

def remove_duplicate_statuses
  statuses = EmployeeEmploymentStatus.kept.where(end_date: nil).order(:employee_id, :created_at).to_a

  statuses.group_by { |status| [status.employee_id, status.employment_status_id] }.each do |_key, grouped_statuses|
    grouped_statuses.sort_by(&:created_at).reverse.drop(1).each(&:discard)
  end
end

def export_names_of_other_employees(employee_ids, active_status_id)
  employees = Employee.kept.where.not(id: employee_ids)
  inactive_status_id = EmploymentStatus.kept.where('lower(name) = ?', 'inactive').first.id
  EmployeeEmploymentStatus.where(employee_id: employees.pluck(:id), employment_status_id: active_status_id.id).update_all(employment_status_id: inactive_status_id)
  CSV.open("#{Rails.root}/local2507_#{Date.today}_members_present_in_the_fuse_other_than_file.csv", 'w') do |csv|
    csv << %w[MemberName UnionStatus]

    employees.each do |employee|
      csv << [employee.full_name, employee.employment_status_name]
    end
  end
end

def local2507_parse_phone(phone)
  return nil unless phone.present?

  return nil if phone.length < 10

  phone.remove!('(', ')', ' ', '-')

  phone_number = '(' + phone.first(3) + ') ' + phone[3..5] + ' - ' + phone.last(4)

  phone_number

rescue => e
  local2507_feed_errors('PHONE ' + e.message)
end

def check_spaces(name)
  return '' if name.blank?
  name.strip
end

# bundle exec rake 'local2507_new_member_import_2024[local2507, local2507_member_run_october.csv]'
# bundle exec rake 'local2507_import_email_details[local2507, local2507_members_import.csv]'
# bundle exec rake 'local2507_import_cell_phone_details[local2507, local2507_active_members.csv]'
# bundle exec rake 'local2507_update_change_request[local2507]'
