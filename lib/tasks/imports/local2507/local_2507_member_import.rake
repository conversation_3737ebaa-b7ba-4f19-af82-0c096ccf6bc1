require 'csv'

desc 'import data'

task :local2507_import_members, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  csv_file.each do |row|

    @row_number = row['Name_Full']
      unless row['Name_First'].present? && row['Name_Last'].present?
        local2507_feed_errors("Mandatory details aren't present")
        next
      end
      @row_number = row['Name_First'] + ' ' + row['Name_Last'] if @row_number.blank?
    if Employee.kept.where(first_name: row['Name_First'], last_name: row['Name_Last']).present?
      next
    end

    employee = Employee.new
    employee.first_name = row['Name_First'] || ''
    employee.last_name = row['Name_Last'] || ''
    employee.street = row['Street1'] || ''
    employee.apartment = row['Street2'] || ''
    employee.city = row['City1'] || ''
    employee.state = row['State_Province1'] || ''
    employee.zipcode = row['Postal_Code1'] || ''
    employee.shield_number = row['Shield Number'] || ''
    employee.maiden_name = row['Car Color'] || ''
    employee.payroll_id = row['Car Year']
    employee.title_code = row['Car_plate number'] || ''
    employee.rdo = row['car_make'] || ''
    employee.placard_number = "#{row['Placard Number']},," || ''
    marital_status = MaritalStatus.where("lower(name) = ?", row['Status'].downcase).first if row['Status'].present?
    marital_status = MaritalStatus.create(name: row['Status']) if row['Status'].present? && marital_status.blank?
    employee.marital_status_id = marital_status&.id || ""

    if employee.save!
      member_title = Rank.where("lower(name) = ?", row['Title'].downcase).first if row['Title'].present?
      member_title = Rank.create(name: row['Title']) if row['Title'].present? && member_title.blank?
      employee.employee_ranks.create(rank_id: member_title.id) if member_title.present?
      phone_number = row['Phone1'].gsub("(", "").gsub(")", "").gsub(" ", "").gsub('-', "") if row['Phone1'].present?
      phone_number = phone_number&.gsub(/(\d{3})(\d{3})(\d{4})/, '(\1) \2 - \3') || nil

      employee.contacts.new(contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE).save!
      employee.contacts.new(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE).save!
      employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE, value: phone_number).save!
      email = row['Email'] || nil
      employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL, value: email).save!
      employee.contacts.new(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL).save!
      employee.contacts.new(contact_for: Contact::ContactFor::COLLEAGUE, contact_type: Contact::ContactType::EMAIL).save!
    else
      @errors[@row_number] = employee.errors.full_messages
    end

  rescue => e
    p @row_number, e.message
    local2507_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_member_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end

task :local2507_import_active_members, [:file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!('local2507')

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  employment_status = EmploymentStatus.kept.where(name: "Active").first_or_create!
  inactive_employment_status = EmploymentStatus.kept.where(name: "Inactive").first_or_create!
  employee_id = []
  csv_file.each do |row|
    first_name = row['FirstName']
    last_name = row['LastName']
    @row_number = first_name + " " + last_name
    if first_name.blank? && last_name.blank?
      local2507_feed_errors("Mandatory Details Not Present")
      next
    end

    employees = Employee.kept.where('lower(first_name) = ? and lower(last_name) = ?', first_name.downcase, last_name.downcase)
    if employees.blank?
      employee = Employee.new
      employee.first_name = check_spaces(row['FirstName']) || ''
      employee.last_name = check_spaces(row['LastName']) || ''
      employee.suffix = check_spaces(row['Suffix']) || ''
      employee.middle_name = check_spaces(row['MiddleName']) || ''
      employee.street = check_spaces(row['Street1']) || ''
      employee.city = check_spaces(row['City1']) || ''
      employee.state = check_spaces(row['State']) || ''
      employee.zipcode = check_spaces(row['Zipcode']) || ''

      if employee.save!
        rank_id = get_rank_id(row['Column2'])
        employee.employee_ranks.new(rank_id: rank_id).save! if rank_id.present?
      end
      employee.employee_employment_statuses.new(employment_status_id: employment_status.id).save!
    elsif employees.count > 1
      local2507_feed_errors("More than one Member found")
      next
    else
      employee = employees.first
      if employee.employee_employment_statuses.present?
        employee.employee_employment_statuses.first.update!(employment_status_id: employment_status.id) if employee.employee_employment_statuses.first.employment_status_id != employment_status.id
      else
        employee.employee_employment_statuses.new(employment_status_id: employment_status.id).save!
      end
    end
    employee_id << employee.id

  rescue => e
    p @row_number, e.message
    local2507_feed_errors(e.message)
  end
  Employee.kept.where.not(id: employee_id.uniq).each do |employee|
    if employee.employee_employment_statuses.present?
      employee.employee_employment_statuses.first.update!(employment_status_id: inactive_employment_status.id) if employee.employee_employment_statuses.first.employment_status_id != inactive_employment_status.id
    else
      employee.employee_employment_statuses.new(employment_status_id: inactive_employment_status.id).save!
    end
  end

  CSV.open("#{Rails.root}/#{Date.today}_local2507_active_members_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

task :local2507_fdny_reference_number, [:file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!('local2507')

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  employee_id = []
  csv_file.each do |row|
    first_name = check_spaces(row['First Name'])
    last_name = check_spaces(row['Last Name'])
    next if first_name.blank? && last_name.blank? || first_name.blank? || last_name.blank?
    @row_number = first_name + " " + last_name

    employee = Employee.where("first_name LIKE ?  and last_name LIKE ?", "%#{first_name}%", "%#{last_name}%")
    if employee.blank?
      local2507_feed_errors("Member not found")
      next
    elsif employee.count > 1
      local2507_feed_errors("More than one Member found")
      next
    end
    employee = employee.first
    employee.a_number = row['Employee Number'] || ''
    employee.start_date = local2507_parse_date(row['City Start Date']) || ''
    employee.save!

  rescue => e
    p @row_number, e.message
    local2507_feed_errors(e.message)
  end

  file_name = args[:file_path].split('.').first

  CSV.open("#{Rails.root}/#{Date.today}_#{file_name}_local2507_FDNY_reference_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

task :local2507_import_car_year, [:file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!('local2507')

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  csv_file.each do |row|
    first_name = row['Name_First'] || ''
    last_name = row['Name_Last'] || ''
    @row_number = first_name + " " + last_name

    employee = Employee.where('lower(first_name) = ? and lower(last_name) = ?', first_name&.downcase, last_name&.downcase)
    if employee.blank?
      next
    elsif employee.count > 1
      local2507_feed_errors("More than one Member found")
      next
    end
    employee = employee.first
    employee.payroll_id = row['Car Year'] || ''
    employee.save!

  rescue => e
    p @row_number, e.message
    local2507_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{Date.today}_local2507_import_car_year.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

task :local2507_split_address_apartment, [] => :environment do |_t, args|
  Apartment::Tenant.switch!('local2507')

  @errors = {}
  members = Employee.where("lower(street) like (?)", "%apt%")
  members.each do |member|
    street = member.street
    next if street.blank? || street.downcase.exclude?("apt")
    position = street.downcase.index("apt")
    member.street = street.slice(0, position - 1)
    member.apartment = street.slice(position + 3, street.length)
    member.save!

  rescue => e
    p @row_number, e.message
    local2507_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{Date.today}_local2507_split_address_apartment_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

task :local2507_import_active_members_not_found_import, [:file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!('local2507')

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  active_status = EmploymentStatus.where(name: "Active").first_or_create!
  active_status_id = active_status.id

  csv_file.each do |row|
    first_name = row['FirstName']
    last_name = row['LastName']
    employees = Employee.where("first_name LIKE ?  and last_name LIKE ?", "%#{first_name}%", "%#{last_name}%")
    if employees.present?
      next
    end

    employee = Employee.new
    employee.first_name = check_spaces(row['FirstName']) || ''
    employee.last_name = check_spaces(row['LastName']) || ''
    employee.suffix = check_spaces(row['Suffix']) || ''
    employee.street = check_spaces(row['Street1']) || ''
    employee.city = check_spaces(row['City1']) || ''
    employee.state = check_spaces(row['State']) || ''
    employee.zipcode = check_spaces(row['Zipcode']) || ''

    if employee.save!
      employee.employee_employment_statuses.new(employment_status_id: active_status_id).save!
      rank_id = get_rank_id(row['Column 2'])
      employee.employee_ranks.new(rank_id: rank_id).save! if rank_id.present?
    end

  rescue => e
    p @row_number, e.message
    local2507_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{Date.today}_local2507_import_active_members_not_found_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

task :local2507_import_work_locations, [:file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!('local2507')

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  csv_file.each do |row|
    first_name = check_spaces(row['Name_First']) || ''
    last_name = check_spaces(row['Name_Last']) || ''
    @row_number = first_name + " " + last_name

    employee = Employee.where('lower(first_name) = ? and lower(last_name) = ?', first_name&.downcase, last_name&.downcase)
    if employee.blank?
      local2507_feed_errors("Employee not found")
      next
    elsif employee.count > 1
      local2507_feed_errors("More than one Member found")
      next
    else
      employee = employee.first
      work_location_name = row['Work Location']
      if work_location_name.present? && work_location_name != '0'
        department_name = "Station #{work_location_name}"
        department = Department.find_or_create_by(name: department_name)
        employee.employee_departments.create(department_id: department.id) if department.present?
        employee.save!
      end
    end
  rescue => e
    p @row_number, e.message
    local2507_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{Date.today}_local2507_import_work_locations_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

task :local2507_update_placard_numbers, [:account] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  @errors = {}
  Employee.kept.find_each do |employee|
    @row_number = employee.full_name
    placard_number_parts = employee.placard_number&.split(',', -1)
    next if placard_number_parts.blank?

    if placard_number_parts.count == 3
      placard1 = placard_number_parts.first || ''
      placard2 = placard_number_parts.second || ''
      placard3 = placard_number_parts.last || ''
    else
      local2507_feed_errors("Placard numbers count less than 3")
      next
    end

    employee.update_columns(placard_number: ",#{placard1.presence || placard2},#{placard3}")
  rescue StandardError => e
    p @row_number, e.message
    local2507_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{Date.today}_local2507_update_placard_number_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

def local2507_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end

def check_spaces(name)
  return '' if name.blank?
  name.strip
end

def local2507_parse_date(date)
  return nil unless date.present?

  parsed_date = nil

  begin
    date_array = date.split('/')
    day = date_array[1].to_i
    month = date_array[0].to_i
    year = date_array[2].to_i

    if year.to_s.length == 2
      if year > 20 && year <= 99
        year = ('19' + year.to_s).to_i
      else
        year = ('20' + year.to_s).to_i
      end
    elsif year.to_s.length == 1
      year = ('200' + year.to_s).to_i
    elsif year.to_s.length > 4
      raise 'Invalid Year'
    else
      year = year.to_i
    end

    parsed_date = Date.new(year, month, day)
  rescue
    parsed_date = Date.parse(date)
  end

  parsed_date

rescue => e
  local2507_feed_errors("DATE -  #{date} " + e.message)
end

def get_rank_id(rank)
  return nil if rank.blank?
  if ['10024', '31661', '31662'].include?(rank)
    rank_name = Rank.kept.where("lower(name) = ?", "fire inspector")
    value = "fire inspector"
  elsif ['53052', '53053'].include?(rank)
    rank_name = Rank.kept.where("lower(name) = ?", "emt")
    value = "emt"
  elsif ['53054'].include?(rank)
    rank_name = Rank.kept.where("lower(name) = ?", "paramedics")
    value = "paramedics"
  end
  return Rank.create!(name: value.upcase).id if rank_name.blank?
  rank_name.first.id
end

# bundle exec rake "local2507_import_members[local2507, local2507_members_import.csv]"
# bundle exec rake "local2507_import_active_members[local2507_active_members.csv]"
# bundle exec rake "local2507_fdny_reference_number[local2507-FDNY-1-Table 1.csv]"
# bundle exec rake "local2507_fdny_reference_number[local2507-FDNY-2-Table 1.csv]"
# bundle exec rake "local2507_fdny_reference_number[local2507-FDNY-3-Table 1.csv]"
# bundle exec rake "local2507_split_address_apartment"
# bundle exec rake "local2507_import_active_members_not_found_import[local2507_active_members.csv]"
# bundle exec rake "local2507_update_placard_numbers[local2507]"
