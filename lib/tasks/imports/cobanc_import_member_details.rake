# frozen_string_literal: true

require 'csv'

desc 'import data'
task :cobanc_import_member_details, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  # Last Name,First Name,MI,Street,Apt/Suite,Town/City,ST,Zip Code,Cell #,Home #,eMail Address,Notes,Title Date,TITLE,Shield #,Grade,Court Location,COBANC Date,Hplx ID #,DOB,Marital Status,SEX,Job Status,UCS Start Date,Tier,Employment Status

  csv_file.each do |row|

    first_name = row['First Name'] || ''
    last_name = row['Last Name'] || ''
    mi = row['MI'] || ''

    @row_number = first_name + ' ' + last_name + ' ' + mi
    street = row['Street'] || ''
    apt = row['Apt/Suite'] || ''
    city = row['Town/City'] || ''
    state = row['ST'] || ''
    zipcode = row['Zip Code'].present? ? row['Zip Code'].split('-').first.rjust(5, '0') : ''
    cell_phone = parse_phone(row['Cell #'])
    home_phone = parse_phone(row['Home #'])
    email = row['eMail Address'] || ''
    notes = row['Notes'] || ''
    title_date = parse_date(row['Title Date'], 'title_date')
    title = check_rank(row['TITLE'])
    shield_number = row['Shield #'] || ''
    grade_number = row['Grade'] || ''
    court_location = check_office(row['Court Location'])
    cobanc_date = parse_date(row['COBANC Date'], 'cobanc_date')
    healthplex = row['Hplx ID #'] || ''
    dob = parse_date(row['DOB'], 'dob')
    marital_status = check_marital_status(row['Marital Status'])
    sex = check_gender(row['SEX'])
    job_status = row['Job Status'] || ''
    usc_date = parse_date(row['UCS Start Date'], 'usc_date')
    tier = check_affiliation(row['Tier'])
    employment_status = check_employment_status(row['Employment Status'])

    @current_account = Account.find_by(subdomain: Apartment::Tenant.current)

    employee = Employee.new

    employee.first_name = first_name
    employee.last_name = last_name
    employee.middle_name = mi
    employee.street = street
    employee.apartment = apt
    employee.city = city
    employee.state = state
    employee.zipcode = zipcode
    employee.notes = notes
    employee.shield_number = shield_number
    employee.placard_number = grade_number
    employee.start_date = cobanc_date
    employee.a_number = healthplex
    employee.birthday = dob
    employee.gender_id = sex.id if sex
    employee.ncc_date = usc_date
    employee.marital_status_id = marital_status.id if marital_status
    employee.affiliation_id = tier.id if tier

    if employee.save
      employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE, value: cell_phone).save! if cell_phone
      employee.contacts.new(contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE, value: home_phone).save! if home_phone
      employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL, value: email).save! if email.present?

      employee.employee_ranks.new(rank_id: title.id, start_date: title_date).save(validate: false) if title
      employee.employee_offices.new(office_id: court_location.id).save(validate: false) if court_location
      employee.employee_employment_statuses.new(employment_status_id: employment_status.id).save(validate: false) if employment_status
    else
      @errors[@row_number] = employee.errors.full_messages
    end

  rescue => e
    p @row_number, e.message
    feed_errors(e.message)
  end

  file_name = args[:file_path].split('.').first

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_#{file_name}_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end

def check_gender(gender_type)
  return nil unless gender_type.present?

  gender = Gender.where(name: gender_type).first_or_create

  gender

rescue => e
  feed_errors('GENDER ' + gender.errors.full_messages)
end

def check_affiliation(affiliation_type)
  return nil unless affiliation_type.present?

  affiliation = Affiliation.where(name: affiliation_type).first_or_create

  affiliation

rescue => e
  feed_errors('AFFILIATION ' + affiliation.errors.full_messages)
end

def check_employment_status(employment_status_type)
  return nil unless employment_status_type.present?

  employment_status = Affiliation.where(name: employment_status_type).first_or_create

  employment_status

rescue => e
  feed_errors('EMPLOYMENT_STATUS ' + employment_status.errors.full_messages)
end

def check_marital_status(marital_status_name)
  return nil unless marital_status_name.present?

  marital_status = MaritalStatus.where(name: marital_status_name).first_or_create

  marital_status

rescue => e
  feed_errors('MARITAL STATUS ' + marital_status.errors.full_messages)
end

def check_office(office_name)
  return nil unless office_name.present?

  office = Office.where(name: office_name).first_or_create

  office

rescue => e
  feed_errors('OFFICE ' + office.errors.full_messages)
end

def check_rank(rank_name)
  return nil unless rank_name.present?

  rank = Rank.where(name: rank_name).first_or_create

  rank

rescue => e
  feed_errors('RANK ' + rank.errors.full_messages)
end

def parse_date(date, type)
  return nil unless date.present?

  parsed_date = nil

  begin
    date_array = date.split('/')
    day = date_array[1].to_i
    month = date_array[0].to_i
    year = date_array[2].to_i

    if year.to_s.length == 2
      if year > 20 && year <= 99
        year = ('19' + year.to_s).to_i
      else
        year = ('20' + year.to_s).to_i
      end
    elsif year.to_s.length == 1
      year = ('200' + year.to_s).to_i
    elsif year.to_s.length > 4
      raise 'Invalid Year'
    else
      year = year.to_i
    end

    parsed_date = Date.new(year, month, day)
  rescue
    parsed_date = Date.parse(date)
  end

  parsed_date

rescue => e
  feed_errors('DATE - ' + type + " #{date} " + e.message)
end

def parse_phone(phone)
  return nil unless phone.present?

  phone.remove!('(', ')', ' ', '-')

  phone_number = '(' + phone.first(3) + ') ' + phone[3..5] + ' - ' + phone.last(4)

  phone_number

rescue => e
  feed_errors('PHONE ' + e.message)
end

def feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end
