# frozen_string_literal: true

require 'csv'

desc 'import data'

task :nyscoa_attach_avatar, [:account, :dir_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  target_folder_path = File.join(Rails.root, args[:dir_path])

  attachment_names = Dir.children(target_folder_path)

  @errors = {}

  attachment_names.each do |attachment|
    @row_number = attachment
    member_name = attachment.split('.')[0]
    date_value = member_name.split('_').last
    year = date_value[-4..-1]
    day = date_value[-6..-5]
    month = date_value[-8..-7]

    dob = "#{year}-#{month}-#{day}"
    last_name = member_name.split('_').first
    first_name = member_name.split('_')[1]

    employees = Employee.kept.where('lower(last_name) = ?', last_name) if last_name.present?

    if employees.present?
      if employees.count > 1
        employees = employees.kept.where('lower(first_name) = ?', first_name) if first_name.present?
        if employees.present? && employees.count > 1
          feed_errors('Multiple occurrences for member')
        elsif employees.present?
          employee = employees.first
        else
          employees = Employee.kept.where(birthday: dob) if dob.present?
          if employees.present? && employees.count > 1
            feed_errors('Multiple occurrences for member')
          elsif employees.present?
            employee = employees.first
          else
            feed_errors('Member not found')
          end
        end
      else
        employee = employees.first
      end
    else
      employees = Employee.kept.where(birthday: dob) if dob.present?
      if employees.present? && employees.count > 1
        feed_errors('Multiple occurrences for member')
      elsif employees.present?
        employee = employees.first
      else
        feed_errors('Member not found')
      end
    end

    if employee.present?
      employee.avatar.attach(io: File.open("#{target_folder_path}/#{attachment}"), filename: attachment)
    end

  end

  file_name = args[:dir_path].split('.').first

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_#{file_name}_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end

task :nyscoa_sms_email_opt_out, [:account] => :environment do |_t, args|
    Apartment::Tenant.switch!(args[:account])

    officer_statuses = OfficerStatus.where("lower(name) IN (?)", ["deceased", "janus", "resigned", "terminated"]).pluck(:id)
    Employee.kept.joins(:employee_officer_statuses).where('(employee_officer_statuses.end_date is NULL or employee_officer_statuses.end_date > ?) and employee_officer_statuses.officer_status_id in (?)',Date.today, officer_statuses).each do |employee|
      employee.update_columns(sms_opt_out: true, email_opt_out: true)
    end
end

task :update_member_start_date, [:account] => :environment do |_t, args|
    Apartment::Tenant.switch!(args[:account])
    @errors = {}

    officer_statuses = OfficerStatus.where("lower(name) IN (?)", ["full-time", "part-time"]).pluck(:id)
    EmployeeOfficerStatus.kept.where('(employee_officer_statuses.end_date is NULL or employee_officer_statuses.end_date > ?) and employee_officer_statuses.officer_status_id in (?)', Date.today, officer_statuses).each do |employee_officer_status|
      if employee_officer_status&.employee&.start_date.present?
        employee_officer_status.update_columns(start_date: employee_officer_status.employee.start_date)
      else
        @row_number = employee_officer_status.employee&.first_name + " " + employee_officer_status.employee&.last_name
        feed_error('Start date is not present')
      end
    end

    CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_update_member_start_date_errors.csv", 'w') do |csv|
      csv << ["Row Number", "Errors"]
      @errors.each do |error|
        csv << error
      end
    end
end

def feed_error(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end

# bundle exec rake 'nyscoa_attach_avatar[nyscoa,nyscoa_images_directory_path]'
# bundle exec rake 'nyscoa_sms_email_opt_out[nyscoa]'
# bundle exec rake 'update_member_start_date[nyscoa]'
