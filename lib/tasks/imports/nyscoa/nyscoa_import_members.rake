# frozen_string_literal: true

require 'csv'

desc 'import data'
task :nyscoa_import_members, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @married = MaritalStatus.where(name: 'Married').first_or_create
  @single = MaritalStatus.where(name: 'Single').first_or_create
  @seperated = MaritalStatus.where(name: 'Separated').first_or_create
  @divorced = MaritalStatus.where(name: 'Divorced').first_or_create
  @widowed = MaritalStatus.where(name: 'Widowed').first_or_create

  @retired_es = EmploymentStatus.where(name: 'Retired').first_or_create
  @terminated_es = EmploymentStatus.where(name: 'Terminated').first_or_create
  @transferred_es = EmploymentStatus.where(name: 'Transferred').first_or_create
  @resigned_es = EmploymentStatus.where(name: 'Resigned').first_or_create
  @deceased_es = EmploymentStatus.where(name: 'Deceased').first_or_create
  @promoted_es = EmploymentStatus.where(name: 'Promoted').first_or_create
  @full_es = EmploymentStatus.where(name: 'Full-time').first_or_create
  @part_es = EmploymentStatus.where(name: 'Part-time').first_or_create

  @retired_os = OfficerStatus.where(name: 'Retired').first_or_create
  @terminated_os = OfficerStatus.where(name: 'Terminated').first_or_create
  @transferred_os = OfficerStatus.where(name: 'Transferred').first_or_create
  @resigned_os = OfficerStatus.where(name: 'Resigned').first_or_create
  @deceased_os = OfficerStatus.where(name: 'Deceased').first_or_create
  @promoted_os = OfficerStatus.where(name: 'Promoted').first_or_create
  @full_os = OfficerStatus.where(name: 'Full-time').first_or_create
  @part_os = OfficerStatus.where(name: 'Part-time').first_or_create

  @errors = {}

  # Last Name,First Name,MI,Address,City,State,Zip Code,Home Telephone,Cell,Work Telephone,E-mail Address,Birth date,SSN,Sex,Married,Single,Seperated,Divorced,Widowed,Shield #,Vehicle ID Current-2,Vehicle ID Current-1,Vehicle ID Current,Hire date,Nights,Criminal,Family,Civil,Supreme,County,Surrogates,Academy,NY,Kings,Queens,Bronx,Richmond,346 Bway,54th St,Bronx Housing,Red Hook,Harlem,AVU,25 BEAVER,MSP,Court Officer,CT ASSISTANT,Assistant Court Clerk,CO,SGT,LT,CAPT,MAJ,Term Date,Retired,Dental Office,Empire Plan,Direct Pay,Dependants,Disposition,ACTIVE,Vehicle ID '12,Vehicle ID Current-4,Vehicle ID Current-3,F/T P/T

  csv_file.each do |row|

    first_name = row['First Name']
    last_name = row['Last Name']
    mi = row['MI'] || ''

    @row_number = [first_name, last_name, mi]

    address = row['Address'] || ''

    apt = if address.include?('#')
            address.split('#').last
          elsif address.downcase.include?('apt')
            address.split('apt').last
          else
            ''
          end

    street = address
    apt = apt
    city = row['City'] || ''
    state = row['State'] || ''
    zipcode = row['Zip Code'].present? ? row['Zip Code'].split('-').first.first(5).rjust(5, '0') : ''
    home_phone = nyscoa_parse_phone(row['Home Telephone'])
    cell_phone = nyscoa_parse_phone(row['Cell'])
    work_phone = nyscoa_parse_phone(row['Work Telephone'])
    personal_email = row['E-mail Address'] || ''
    dob = nyscoa_parse_date(row['Birth date'], 'DOB') rescue nil
    ssn = nyscoa_parse_ssn(row['SSN']) || ''
    sex = nyscoa_check_gender(row['Sex'])
    marital_status = nyscoa_check_marital_status(row['Married'], row['Seperated'], row['Divorced'], row['Widowed'])
    shield_number = row['Shield #'] || ''
    placard_number = [row['Vehicle ID Current'], row['Vehicle ID Current-1'], row['Vehicle ID Current-2']].join(',')
    hire_date = nyscoa_parse_date(row['Hire date'], 'Hire Date') rescue nil

    # Logic to identify Commands
    nights = row['Nights'].downcase == 'true' ? true : false
    criminal = row['Criminal'].downcase == 'true' ? true : false
    family = row['Family'].downcase == 'true' ? true : false
    civil = row['Civil'].downcase == 'true' ? true : false
    supreme = row['Supreme'].downcase == 'true' ? true : false
    county = row['County'].downcase == 'true' ? true : false
    surrogates = row['Surrogates'].downcase == 'true' ? true : false
    academy = row['Academy'].downcase == 'true' ? true : false
    ny = row['NY'].downcase == 'true' ? true : false
    kings = row['Kings'].downcase == 'true' ? true : false
    queens = row['Queens'].downcase == 'true' ? true : false
    bronx = row['Bronx'].downcase == 'true' ? true : false
    richmond = row['Richmond'].downcase == 'true' ? true : false
    bway346 = row['346 Bway'].downcase == 'true' ? true : false
    st54th = row['54th St'].downcase == 'true' ? true : false
    bronxhousing = row['Bronx Housing'].downcase == 'true' ? true : false
    redhook = row['Red Hook'].downcase == 'true' ? true : false
    harlem = row['Harlem'].downcase == 'true' ? true : false
    avu = row['AVU'].downcase == 'true' ? true : false
    beaver25 = row['25 BEAVER'].downcase == 'true' ? true : false
    msp = row['MSP'].downcase == 'true' ? true : false

    office = nyscoa_check_offices(nights, criminal, family, civil, supreme, county, surrogates, academy, ny, kings, queens, bronx, richmond, bway346, st54th, bronxhousing, redhook, harlem, avu, beaver25, msp)

    rank = nyscoa_check_rank(row['CT ASSISTANT'], row['Assistant Court Clerk'], row['CO'], row['SGT'], row['LT'], row['CAPT'], row['MAJ'])
    term_date = nyscoa_parse_date(row['Term Date'], 'Term Date') rescue nil

    # Logic to find Employment Status and Officer Status
    retired = row['Retired'].present? && row['Retired'].downcase == 's' ? true : false
    disposition = row['Disposition'].present? ? row['Disposition'].downcase : nil
    full_or_part = row['F/T P/T'].present? ? row['F/T P/T'].downcase : nil

    employment_status, officer_status = nyscoa_check_other_statuses(retired, disposition, row['ACTIVE'].downcase, full_or_part)

    current_account = Account.find_by(subdomain: Apartment::Tenant.current)
    @current_account = Account.find_by(subdomain: Apartment::Tenant.current)

    employee = Employee.new

    employee.first_name = first_name
    employee.last_name = last_name
    employee.middle_name = mi
    employee.street = street
    employee.apartment = apt
    employee.city = city
    employee.state = state
    employee.zipcode = zipcode
    employee.social_security_number = ssn
    employee.shield_number = shield_number
    employee.birthday = dob
    employee.placard_number = placard_number
    employee.start_date = hire_date
    employee.gender_id = sex.id if sex
    employee.marital_status_id = marital_status.id if marital_status

    if employee.save
      personal_phone_contact = employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE)
      personal_phone_contact.value = cell_phone if cell_phone
      personal_phone_contact.save!

      home_phone_contact = employee.contacts.new(contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE)
      home_phone_contact.value = home_phone if home_phone
      home_phone_contact.save!

      work_phone_contact = employee.contacts.new(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE)
      work_phone_contact.value = work_phone if work_phone
      work_phone_contact.save!

      personal_email_contact = employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL)
      personal_email_contact.value = personal_email if personal_email.present?
      personal_email_contact.save!

      personal_email_contact = employee.contacts.new(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL)
      personal_email_contact.save!

      if employment_status
        e_status = employee.employee_employment_statuses.new(employment_status_id: employment_status.id)
        e_status.start_date = term_date if employment_status.name.downcase == 'terminated'
        e_status.save(validate: false)
      end

      if officer_status
        o_status = employee.employee_officer_statuses.new(officer_status_id: officer_status.id)
        o_status.start_date = term_date if officer_status.name.downcase == 'terminated'
        o_status.save(validate: false)
      end

      employee.employee_offices.new(office_id: office.id).save(validate: false) if office

      employee.employee_ranks.new(rank_id: rank.id).save(validate: false) if rank
    else
      @errors[@row_number] = employee.errors.full_messages
    end

  rescue => e
    p @row_number, e.message
    nyscoa_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_member_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end

task :nyscoa_import_dependents, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  csv_file.each do |row|
    member_ssn = nyscoa_parse_ssn(row['Member SSN'])
    @row_number = member_ssn
    members = Employee.where(social_security_number: member_ssn)

    if members.count > 1
      nyscoa_feed_errors("Multiple Members found for this SSN")
      next
    end

    member = members.first

    unless member.present?
      nyscoa_feed_errors("Member not found for this SSN")
      next
    end

    spouse_f_name = row['Spouse First Name'] || ''
    spouse_l_name = row['Spouse Last Name'] || ''
    spouse_dob = nyscoa_parse_date(row['Spouse Date of Birth'], 'Spouse DOB')
    spouse_ssn = nyscoa_parse_ssn(row['Spouse SSN'])

    if spouse_ssn.present?
      dependent = member.benefit_coverages.new(name: [spouse_f_name, spouse_l_name].join(' '), relationship: 'Spouse', social_security_number: spouse_ssn, address: member.employee_address, birthday: spouse_dob)
      dependent.save!
    end

    (1..9).each do |num|
      child_f_name = row["Child #{num} First Name"] || ''
      child_l_name = row["Child #{num} Last Name"] || ''
      child_dob = nyscoa_parse_date(row["Child #{num} Date of Birth"], "Child #{num} DOB")
      child_ssn = nyscoa_parse_ssn(row["Child #{num} SSN"])

      if child_ssn.present?
        dependent = member.benefit_coverages.new(name: [child_f_name, child_l_name].join(' '), relationship: 'Child', social_security_number: child_ssn, address: member.employee_address, birthday: child_dob)
        dependent.save!
      end
    end
  rescue => e
    p @row_number, e.message
    nyscoa_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_dependents_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]
    @errors.each do |error|
      csv << error
    end
  end
end

task :nyscoa_import_benefit_types, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  @vision = Benefit.where(name: 'Vision').first_or_create
  @dental_office = Benefit.where(name: 'Dental Office').first_or_create
  @empire = Benefit.where(name: 'Empire').first_or_create
  @direct_pay = Benefit.where(name: 'Direct Pay').first_or_create

  csv_file.each do |row|
    member_ssn = nyscoa_parse_ssn(row['SSN'])
    @row_number = member_ssn
    members = Employee.where(social_security_number: member_ssn)

    member = members.first

    if member.present?
      employment_statuses = member.employee_employment_statuses.where('end_date is NUll OR end_date >= ?', Date.today)
      employment_status_name = employment_statuses.first.name if employment_statuses.present?

      if %w[Full-time Part-time Retired].include?(employment_status_name)
        employee_benefit = member.employee_benefits.new(benefit_id: @vision.id) if @vision
        employee_benefit.save(validate: false) if employee_benefit.present?
      end

      benefits = nyscoa_check_benefit(row['Dental Office'], row['Empire Plan'], row['Direct Pay'])
      if benefits.present?
        benefits.each do |benefit|
          employee_benefit = member.employee_benefits.new(benefit_id: benefit.id)
          employee_benefit.save(validate: false) if employee_benefit.present?
        end
      end
    else
      nyscoa_feed_errors("Member not found for this SSN")
    end

  rescue => e
    p @row_number, e.message
    nyscoa_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_benefit_types_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end

task :nyscoa_import_benefit_forms, [:account, :dir_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  target_folder_path = File.join(Rails.root, args[:dir_path])
  attachment_names = Dir.children(target_folder_path)

  @beneficiary_form = PaymentType.where(name: 'Beneficiary').first_or_create
  @vision_form = PaymentType.where(name: 'Vision').first_or_create
  @dental_form = PaymentType.where(name: 'Dental Office').first_or_create

  @vision = Benefit.where(name: 'Vision').first_or_create
  @dental_office = Benefit.where(name: 'Dental Office').first_or_create

  @errors = {}

  begin
    attachment_names.each do |attachment_name|
      p attachment_name
      next if %w[thumbs.db thumbs(1).db .ds_store].include?(attachment_name.downcase)
      sub_attachment_names = Dir.children("#{target_folder_path}/#{attachment_name}")
      names = attachment_name.split(',')
      last_name = names[0].try(:strip)
      first_name = names[1].try(:strip)
      @row_number = attachment_name

      employees = Employee.kept.where('last_name ilike ? and first_name ilike ?', ('%' + last_name), (first_name + '%')) if last_name.present? && first_name.present?
      if employees.present?
        if employees.count > 1
          nyscoa_feed_errors('Multiple occurrences for member')
        else
          employee = employees.first
        end
      else
        nyscoa_feed_errors('Members not found')
      end

      if employee.present?
        sub_attachment_names.each do |attachment|
          @row_number = attachment
          next if %w[thumbs.db thumbs(1).db .ds_store].include?(attachment.downcase)

          file_name = attachment.split('.')[0]

          benefit_type = nil
          if file_name.downcase.include?('vision')
            document_type = @vision_form
            benefit_type = @vision
          elsif file_name.downcase.include?('dental')
            document_type = @dental_form
            benefit_type = @dental_office
          else
            document_type = @beneficiary_form
          end

          employee_benefit = nil
          if benefit_type.present?
            employee_benefits = employee.employee_benefits.where(benefit_id: benefit_type.id)
            if employee_benefits.present?
              employee_benefit = employee_benefits.first
            else
              employee_benefit = employee.employee_benefits.new(benefit_id: benefit_type.id)
              employee_benefit.save(validate: false) if employee_benefit.present?
            end
          end

          if document_type.present? && employee_benefit.present?
            benefit_disbursement = employee.benefit_disbursements.new(payment_type_id: document_type.id, employee_benefit_id: employee_benefit.id)
          elsif document_type.present? && employee_benefit == nil
            benefit_disbursement = employee.benefit_disbursements.new(payment_type_id: document_type.id)
          end

          begin
            benefit_disbursement.file.attach(io: File.open("#{target_folder_path}/#{attachment_name}/#{attachment}"), filename: attachment) if benefit_disbursement.save(validate: false)
          rescue => e
            p @row_number, e.message
            nyscoa_feed_errors(e.message)
          end

        rescue => e
          p @row_number, e.message
          nyscoa_feed_errors(e.message)
        end
      end
    end
  rescue => e
    p @row_number, e.message
    nyscoa_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}#{args[:dir_path]}_#{Date.today}_nyscoa_benefit_forms_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end

task :nyscoa_import_benefit_forms_subfolder, [:account, :dir_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  target_folder_path = File.join(Rails.root, args[:dir_path])
  attachment_names = Dir.children(target_folder_path)

  @beneficiary_form = PaymentType.where(name: 'Beneficiary').first_or_create
  @vision_form = PaymentType.where(name: 'Vision').first_or_create
  @dental_form = PaymentType.where(name: 'Dental Office').first_or_create

  @vision = Benefit.where(name: 'Vision').first_or_create
  @dental_office = Benefit.where(name: 'Dental Office').first_or_create

  @errors = {}

  begin
    attachment_names.each do |attachment_name|
      p attachment_name
      next if %w[thumbs.db thumbs(1).db .ds_store].include?(attachment_name.downcase)
      sub_attachment_names = Dir.children("#{target_folder_path}/#{attachment_name}")
      names = attachment_name.split(',')
      last_name = names[0].try(:strip)
      first_name = names[1].try(:strip)
      @row_number = attachment_name

      employees = Employee.kept.where('last_name ilike ? and first_name ilike ?', ('%' + last_name), (first_name + '%')) if last_name.present? && first_name.present?
      if employees.present?
        if employees.count > 1
          nyscoa_feed_errors('Multiple occurrences for member')
        else
          employee = employees.first
        end
      else
        nyscoa_feed_errors('Members not found')
      end

      if employee.present?
        sub_attachment_names.each do |attachment|
          @row_number = attachment
          next if %w[thumbs.db thumbs(1).db .ds_store].include?(attachment.downcase)
          next unless File.directory?("#{target_folder_path}/#{attachment_name}/#{attachment}")
          p attachment

          sub_level2_attachment_names = Dir.children("#{target_folder_path}/#{attachment_name}/#{attachment}")

          sub_level2_attachment_names.each do |sub_level2_attachment|
            p sub_level2_attachment
            file_name = sub_level2_attachment.split('.')[0]

            benefit_type = nil
            if file_name.downcase.include?('vision')
              document_type = @vision_form
              benefit_type = @vision
            elsif file_name.downcase.include?('dental')
              document_type = @dental_form
              benefit_type = @dental_office
            else
              document_type = @beneficiary_form
            end

            employee_benefit = nil
            if benefit_type.present?
              employee_benefits = employee.employee_benefits.where(benefit_id: benefit_type.id)
              if employee_benefits.present?
                employee_benefit = employee_benefits.first
              else
                employee_benefit = employee.employee_benefits.new(benefit_id: benefit_type.id)
                employee_benefit.save(validate: false) if employee_benefit.present?
              end
            end

            if document_type.present? && employee_benefit.present?
              benefit_disbursement = employee.benefit_disbursements.new(payment_type_id: document_type.id, employee_benefit_id: employee_benefit.id)
            elsif document_type.present? && employee_benefit == nil
              benefit_disbursement = employee.benefit_disbursements.new(payment_type_id: document_type.id)
            end

            begin
              benefit_disbursement.file.attach(io: File.open("#{target_folder_path}/#{attachment_name}/#{attachment}/#{sub_level2_attachment}"), filename: sub_level2_attachment) if benefit_disbursement.save(validate: false)
            rescue => e
              p @row_number, e.message, benefit_disbursement.errors
              nyscoa_feed_errors(e.message)
            end

          rescue => e
            p @row_number, e.message
            nyscoa_feed_errors(e.message)
          end

        end
      end
    end
  rescue => e
    p @row_number, e.message
    nyscoa_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}#{args[:dir_path]}_#{Date.today}_nyscoa_benefit_forms_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end

task :nyscoa_update_benefit_form_type, [:account] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  @errors = {}

  dental_office = Benefit.where(name: 'Dental Office').first_or_create
  empire = Benefit.where(name: 'Empire').first_or_create
  direct_pay = Benefit.where(name: 'Direct Pay').first_or_create

  empire_form = PaymentType.where(name: 'Empire').first_or_create
  direct_pay_form = PaymentType.where(name: 'Direct Pay').first_or_create

  dental_benefit_ids = EmployeeBenefit.where(benefit_id: dental_office.id).ids
  empire_benefit_ids = EmployeeBenefit.where(benefit_id: empire.id).ids
  direct_pay_benefit_ids = EmployeeBenefit.where(benefit_id: direct_pay.id).ids

  # Logic to move Dental Office's Benefit Disbursements to Empire if the Member has Empire and Dental Office Benefit
  p BenefitDisbursement.where(employee_benefit_id: dental_benefit_ids).count

  members = Employee.includes(:employee_benefits).where(employee_benefits: { benefit_id: empire.id })
  members.each do |member|
    member_benefits = EmployeeBenefit.where(employee_id: member.id)
    member_benefit_ids = member_benefits.pluck(:benefit_id)
    p "#{member.full_name} : #{member_benefit_ids}: #{member_benefit_ids.include?(dental_office.id)}"
    if member_benefit_ids.include?(dental_office.id)
      dental_benefit = member_benefits.where(benefit_id: dental_office.id).first
      empire_benefit = member_benefits.where(benefit_id: empire.id).first
      benefit_disbursements = dental_benefit.benefit_disbursements
      benefit_disbursements.each do |benefit_disbursement|
        benefit_disbursement.payment_type_id = empire_form.id
        benefit_disbursement.employee_benefit_id = empire_benefit.id
        benefit_disbursement.save(validate: false)
      end
    end
  end

  p BenefitDisbursement.where(employee_benefit_id: empire_benefit_ids).count

  # Logic to move Dental Office's Benefit Disbursements to Direct Pay if the Member has Direct Pay and Dental Office Benefit
  p BenefitDisbursement.where(employee_benefit_id: dental_benefit_ids).count

  members = Employee.includes(:employee_benefits).where(employee_benefits: { benefit_id: direct_pay.id })
  members.each do |member|
    member_benefits = EmployeeBenefit.where(employee_id: member.id)
    member_benefit_ids = member_benefits.pluck(:benefit_id)
    p "#{member.full_name} : #{member_benefit_ids}: #{member_benefit_ids.include?(dental_office.id)}"
    if member_benefit_ids.include?(dental_office.id)
      dental_benefit = member_benefits.where(benefit_id: dental_office.id).first
      direct_pay_benefit = member_benefits.where(benefit_id: direct_pay.id).first
      benefit_disbursements = dental_benefit.benefit_disbursements
      benefit_disbursements.each do |benefit_disbursement|
        benefit_disbursement.payment_type_id = direct_pay_form.id
        benefit_disbursement.employee_benefit_id = direct_pay_benefit.id
        benefit_disbursement.save(validate: false)
      end
    end
  end

  p BenefitDisbursement.where(employee_benefit_id: direct_pay_benefit_ids).count

  # Logic to delete Dental Office if the Member has Empire and Dental Office Benefit
  members = Employee.includes(:employee_benefits).where(employee_benefits: { benefit_id: dental_office.id })
  members.each do |member|
    @row_number = member.full_name
    member_benefits = EmployeeBenefit.where(employee_id: member.id)
    member_benefit_ids = member_benefits.pluck(:benefit_id)
    if member_benefit_ids.include?(empire.id)
      dental_benefit = member_benefits.where(benefit_id: dental_office.id).first

      if dental_benefit.benefit_disbursements.present?
        nyscoa_feed_errors('Dental Benefit Present')
      else
        dental_benefit.destroy
      end
    end
  end

  # Logic to delete Dental Office if the Member has Direct Pay and Dental Office Benefit
  members = Employee.includes(:employee_benefits).where(employee_benefits: { benefit_id: dental_office.id })
  members.each do |member|
    @row_number = member.full_name
    member_benefits = EmployeeBenefit.where(employee_id: member.id)
    member_benefit_ids = member_benefits.pluck(:benefit_id)
    if member_benefit_ids.include?(direct_pay.id)
      dental_benefit = member_benefits.where(benefit_id: dental_office.id).first

      if dental_benefit.benefit_disbursements.present?
        nyscoa_feed_errors('Dental Benefit Present')
      else
        dental_benefit.destroy
      end
    end
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_nyscoa_benefit_forms_update_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

task :nyscoa_update_placard, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])

  @errors = {}

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  csv_file.each do |row|
    @row_number = member_name = row['Member Name']
    placard_2023 = row['Placard 2023']
    first_name = member_name.split(' ').first || ''
    last_name = member_name.split(' ').last || ''

    employees = Employee.kept.where('lower(first_name) = ? and lower(last_name) = ?', first_name.downcase, last_name.downcase)

    if employees.present?
      if employees.count > 1
        nyscoa_feed_errors("Multiple occurrences for member")
      else
        employee = employees.first
      end
    else
      nyscoa_feed_errors('Member not found')
    end

    if employee.present?
      placard = [placard_2023, employee.placard_number.split(',').last(2)].flatten.first(3)
      employee.placard_number = placard.join(',')
      unless employee.save(validate: false)
        nyscoa_feed_errors('Member update error')
      end
    end

  rescue => e
    p @row_number, e.message
    nyscoa_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_nyscoa_placard_update_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

def nyscoa_parse_date(date, type)
  return nil unless date.present?
  return nil if date.to_i.zero?

  parsed_date = nil

  begin
    date_array = date.split('/')
    day = date_array[1].to_i
    month = date_array[0].to_i
    year = date_array[2].to_i

    if year.to_s.length == 2
      if year > 20 && year <= 99
        year = ('19' + year.to_s).to_i
      else
        year = ('20' + year.to_s).to_i
      end
    elsif year.to_s.length == 1
      year = ('200' + year.to_s).to_i
    elsif year.to_s.length > 4
      raise 'Invalid Year'
    else
      year = year.to_i
    end

    parsed_date = Date.new(year, month, day)
  rescue
    parsed_date = Date.parse(date)
  end

  parsed_date

rescue => e
  feed_errors('DATE - ' + type + " #{date} " + e.message)
end

def nyscoa_parse_phone(phone)
  return nil unless phone.present?
  return nil if phone.length < 10

  phone.remove!('(', ')', ' ', '-')

  phone_number = '(' + phone.first(3) + ') ' + phone[3..5] + ' - ' + phone.last(4)

  phone_number

rescue => e
  nyscoa_feed_errors('PHONE ' + e.message)
end

def nyscoa_parse_ssn(ssn)
  return nil unless ssn.present?

  ssn.remove!('(', ')', ' ', '-')

  ssn = ssn.rjust(9, '0')

  social_security = ssn.first(3) + '-' + ssn[3..4] + '-' + ssn.last(4)

  social_security

rescue => e
  nyscoa_feed_errors('SSN ' + e.message)
end

def nyscoa_check_gender(gender_type)
  return nil unless gender_type.present?

  gender = Gender.where(name: gender_type).first_or_create

  gender

rescue => e
  nyscoa_feed_errors('GENDER ' + gender.errors.full_messages)
end

def nyscoa_check_marital_status(married, seperated, divorced, widowed)
  if married.downcase == 'true'
    @married
  elsif seperated.downcase == 'true'
    @seperated
  elsif divorced.downcase == 'true'
    @divorced
  elsif widowed.downcase == 'true'
    @widowed
  else
    @single
  end
rescue => e
  nyscoa_feed_errors('MARITAL STATUS ' + marital_status.errors.full_messages)
end

def nyscoa_check_offices(nights, criminal, family, civil, supreme, county, surrogates, academy, ny, kings, queens, bronx, richmond, bway346, st54th, bronxhousing, redhook, harlem, avu, beaver25, msp)
  office = if beaver25
             Office.where(name: '25 Beaver').first_or_create
           elsif st54th
             Office.where(name: '54th St').first_or_create
           elsif avu
             Office.where(name: 'AVU').first_or_create
           elsif academy
             Office.where(name: 'Academy').first_or_create
           elsif harlem
             Office.where(name: 'Harlem').first_or_create
           elsif msp
             Office.where(name: 'MSP').first_or_create
           elsif redhook
             Office.where(name: 'Red Hook').first_or_create
           elsif bway346
             Office.where(name: '346 Bway').first_or_create
           elsif bronxhousing
             Office.where(name: 'Bronx Housing').first_or_create
           elsif ny
             if nights && criminal
               Office.where(name: 'NY Criminal Nights').first_or_create
             elsif criminal
               Office.where(name: 'NY Criminal').first_or_create
             elsif supreme
               Office.where(name: 'NY Supreme').first_or_create
             elsif county
               Office.where(name: 'NY County Clerk').first_or_create
             elsif family
               Office.where(name: 'NY Family').first_or_create
             elsif civil
               Office.where(name: 'NY Civil').first_or_create
             elsif surrogates
               Office.where(name: 'NY Surrogates').first_or_create
             end
           elsif kings
             if nights && criminal
               Office.where(name: 'Kings Criminal Nights').first_or_create
             elsif criminal
               Office.where(name: 'Kings Criminal').first_or_create
             elsif supreme
               Office.where(name: 'Kings Supreme').first_or_create
             elsif county
               Office.where(name: 'Kings County Clerk').first_or_create
             elsif family
               Office.where(name: 'Kings Family').first_or_create
             elsif civil
               Office.where(name: 'Kings Civil').first_or_create
             elsif surrogates
               Office.where(name: 'Kings Surrogates').first_or_create
             end
           elsif queens
             if nights && criminal
               Office.where(name: 'Queens Criminal Nights').first_or_create
             elsif criminal
               Office.where(name: 'Queens Criminal').first_or_create
             elsif supreme
               Office.where(name: 'Queens Supreme').first_or_create
             elsif county
               Office.where(name: 'Queens County Clerk').first_or_create
             elsif family
               Office.where(name: 'Queens Family').first_or_create
             elsif civil
               Office.where(name: 'Queens Civil').first_or_create
             elsif surrogates
               Office.where(name: 'Queens Surrogates').first_or_create
             end
           elsif bronx
             if nights && criminal
               Office.where(name: 'Bronx Criminal Nights').first_or_create
             elsif criminal
               Office.where(name: 'Bronx Criminal').first_or_create
             elsif supreme
               Office.where(name: 'Bronx Supreme').first_or_create
             elsif county
               Office.where(name: 'Bronx County Clerk').first_or_create
             elsif family
               Office.where(name: 'Bronx Family').first_or_create
             elsif civil
               Office.where(name: 'Bronx Civil').first_or_create
             elsif surrogates
               Office.where(name: 'Bronx Surrogates').first_or_create
             end
           elsif richmond
             if nights && criminal
               Office.where(name: 'Richmond Criminal Nights').first_or_create
             elsif criminal
               Office.where(name: 'Richmond Criminal').first_or_create
             elsif supreme
               Office.where(name: 'Richmond Supreme').first_or_create
             elsif county
               Office.where(name: 'Richmond County Clerk').first_or_create
             elsif family
               Office.where(name: 'Richmond Family').first_or_create
             elsif civil
               Office.where(name: 'Richmond Civil').first_or_create
             elsif surrogates
               Office.where(name: 'Richmond Surrogates').first_or_create
             end
           end

  office
end

def nyscoa_check_rank(ct_assistant, asst_ct_clerk, co, sgt, lt, capt, maj)
  rank = if ct_assistant.downcase == 'true'
           Rank.where(name: 'CT ASSISTANT').first_or_create
         elsif asst_ct_clerk.downcase == 'true'
           Rank.where(name: 'Assistant Court Clerk').first_or_create
         elsif co.downcase == 'true'
           Rank.where(name: 'CO').first_or_create
         elsif sgt.downcase == 'true'
           Rank.where(name: 'SGT').first_or_create
         elsif lt.downcase == 'true'
           Rank.where(name: 'LT').first_or_create
         elsif capt.downcase == 'true'
           Rank.where(name: 'CAPT').first_or_create
         elsif maj.downcase == 'true'
           Rank.where(name: 'MAJ').first_or_create
         end
  rank
end

def nyscoa_check_other_statuses(retired, disposition, active, full_or_part)
  statuses = if retired
               [@retired_es, @retired_os]
             elsif active == 'n'
               if disposition.present?
                 if disposition.include?('terminated')
                   [@terminated_es, @terminated_os]
                 elsif disposition.include?('deceased')
                   [@deceased_es, @deceased_os]
                 elsif disposition.include?('clerk') || disposition.include?('sco') || disposition.include?('ssc')
                   [@promoted_es, @promoted_os]
                 elsif disposition.include?('scoa union') || disposition.include?('upstate') || disposition.include?('surrogate') || disposition.include?('supreme') || disposition.include?('jd') || disposition.include?('j.d') || disposition.include?('nassau') || disposition.include?('suffolk') || disposition.include?('transferred')
                   [@transferred_es, @transferred_os]
                 elsif disposition.include?('fire department') || disposition.include?('fdny') || disposition.include?('pa pd') || disposition.include?('nypd') || disposition.include?('resigned')
                   [@resigned_es, @resigned_os]
                 end
               end
             else
               if full_or_part == 'full time'
                 [@full_es, @full_os]
               elsif full_or_part == 'part time'
                 [@part_es, @part_os]
               else
                 [nil, nil]
               end
             end

  statuses

end

def nyscoa_check_benefit(dental_office, empire, direct_pay)
  benefit = []

  benefit << @dental_office if dental_office.downcase == 'true'
  benefit << @empire if empire.downcase == 'true'
  benefit << @direct_pay if direct_pay.downcase == 'true'

  benefit
rescue => e
  nyscoa_feed_errors('BENEFIT ' + benefit.errors.full_messages)
end

def nyscoa_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end

# bundle exec rake 'nyscoa_import_members[nyscoa,nyscoa_members.csv]'
# bundle exec rake 'nyscoa_import_dependents[nyscoa,nyscoa_dependant.csv]'
# bundle exec rake 'nyscoa_import_benefit_types[nyscoa,nyscoa_benefit_types.csv]'
# bundle exec rake 'nyscoa_import_benefit_forms[nyscoa, directory_name]'
# bundle exec rake 'nyscoa_import_benefit_forms_subfolder[nyscoa, directory_name]'
# bundle exec rake 'nyscoa_update_benefit_form_type[nyscoa]'
# bundle exec rake 'nyscoa_update_placard[nyscoa,file_name.csv]'
