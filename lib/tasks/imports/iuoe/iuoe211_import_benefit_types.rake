# frozen_string_literal: true

require 'csv'

task :iuoe211_add_benefit_types_retired_union, [] => :environment do |_t|
  Apartment::Tenant.switch!('iuoe211')
  @errors = {}

  benefit_ids = []
  ['Chiropractic Benefit', 'Death Benefit', 'Dental Benefit', 'Disability Benefit', 'Hearing Benefit', 'Legal Services',
   'Optical Benefit', 'Podiatry Benefit', 'Prescription Drug Benefit', 'Benefit Screening Benefit'].each do |benefit|
    benefit = Benefit.where(name: benefit).first_or_create
    benefit_ids << benefit.id
  end
  benefit_ids_hash = benefit_ids.map { |x| { benefit_id: x } }

  employment_status_ids = EmploymentStatus.where('lower(name) in (?)', %w[retired union agency cobra leave staff])
                                          .pluck(:id)
  employees = Employee.kept.includes('employee_employment_statuses', 'employment_statuses').where('(employee_employment_statuses.end_date is NULL or employee_employment_statuses.end_date < ?)
                              AND employee_employment_statuses.employment_status_id in (?)', Date.today, employment_status_ids).references('employee_employment_statuses', 'employment_statuses')
  employees.find_each do |employee|
    @row_number = employee.full_name
    employee.employee_benefits.create!(benefit_ids_hash) ## Here importing the benefits bulk in the array, If we need any additional columns. We should add in the hash like {benefit_id: 1, start_date: Date.today}

  rescue StandardError => e
    p @row_number, e.message
    iuoe211_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/IUOE211_add_benefit_types_retired_union_#{Date.today}_errors.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']

    @errors&.each do |error|
      csv << error
    end
  end
end

task screening_benefit_cleanup: :environment do |_t|
  Apartment::Tenant.switch!('iuoe211')
  @errors = {}
  @row_number = 'Benefit Screening Benefit'
  new_benefit_id = Benefit.kept.where('lower(name) = ?', 'screening benefit').first.id

  ActiveRecord::Base.transaction do
    old_benefit = Benefit.kept.where('lower(name) = ?', @row_number.downcase).first
    if old_benefit
      EmployeeBenefit.kept.where(benefit_id: old_benefit.id).update_all(benefit_id: new_benefit_id)
      old_benefit.discard
    else
      iuoe211_feed_errors('Benefit not found')
    end
  rescue StandardError => e
    p @row_number, e.message
    iuoe211_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/screening_benefit_cleanup_#{Date.today}_errors.csv", 'w') do |csv|
    csv << ['Benefit Name', 'Errors']

    @errors&.each do |error|
      csv << error
    end
  end
end

task :iuoe211_report, %w[account file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  first_row = csv_file[0]
  included_statuses = ['agency', 'cobra', 'disability', 'employer suspension', 'iuoe suspension', 'leave', 'military duty', 'retired', 'staff', 'union', "workers'comp"]
  # employees = Employee.kept.joins(employee_employment_statuses: :employment_status)
  #                     .where('LOWER(employment_statuses.name) IN (?) AND (employee_employment_statuses.end_date IS NULL OR employee_employment_statuses.end_date > ?)', included_statuses, Date.today)
  #                     .order('employees.first_name ASC, employees.last_name ASC')

  employees = Employee.kept.includes(employee_employment_statuses: :employment_status)
                      .where('employee_employment_statuses.end_date IS NULL OR employee_employment_statuses.end_date >= ?', Date.today)
                      .where(employee_employment_statuses: { employment_status_id: EmploymentStatus.kept.where('lower(name) in (?)', included_statuses).pluck(:id) })
                      .order('employees.first_name ASC, employees.last_name ASC')
  CSV.open(args[:file_path], 'w', write_headers: true, headers: csv_file.headers) do |csv|
    csv << first_row
    employees.each do |employee|
      csv << format_employee_and_coverage_data(employee)

      coverages = employee.benefit_coverages.where('expires_at is null or expires_at > ?', Date.today)
      coverages.each do |coverage|
        next if %w[child step_child disabled_child legal_guardian].include?(coverage.relationship) && age(coverage.birthday) > 26

        row = format_employee_and_coverage_data(employee, coverage)
        csv << row
      end
    end
  end
end

task :iuoe211_effective_date_update_for_dependents, [:account] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  @errors = {}
  employees = Employee.kept.includes(:employee_benefits, :benefit_coverages)

  ActiveRecord::Base.transaction do
    employees.find_each do |employee|
      @row_number = "#{employee.first_name} #{employee.last_name}"
      benefit_coverages = employee.benefit_coverages

      effective_date = employee.employee_benefits.pluck(:start_date).compact.min || employee.start_date

      next iuoe211_feed_errors('No Effective Dates Found') if effective_date.blank?

      future_birthdate_coverage_names = benefit_coverages.where('birthday > ?', effective_date).pluck(:name)
      iuoe211_feed_errors("The birth dates of the following coverages [#{future_birthdate_coverage_names.join(', ')}] are greater than the effective date.") if future_birthdate_coverage_names.present?

      coverages_without_birthday = benefit_coverages.where(birthday: nil).pluck(:name)
      iuoe211_feed_errors("Birth dates are missing for the following coverages: [#{coverages_without_birthday.join(', ')}].") if coverages_without_birthday.present?

      benefit_coverages.where('birthday < ?', effective_date).update_all(effective_date: effective_date)
    end
  rescue StandardError => e
    p @row_number, e.message
    iuoe211_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_effective_date_update_#{Date.today}_errors.csv", 'w') do |csv|
    csv << ['Employee Name', 'Errors']

    @errors&.each do |error|
      csv << error
    end
  end
end

task :iuoe211_eligible_members_without_benefit, %i[account benefit] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  benefit_id = Benefit.kept.where('lower(name) = ?', args[:benefit].downcase).first.id
  included_statuses = ['agency', 'cobra', 'disability', 'employer suspension', 'iuoe suspension', 'leave', 'military duty', 'retired', 'staff', 'union', "workers' comp"]
  status_ids = EmploymentStatus.kept.where('lower(name) IN (?)', included_statuses).pluck(:id)

  employees = Employee.kept.includes(:employee_benefits, :employee_employment_statuses)
                      .where('(employee_employment_statuses.end_date IS NULL OR employee_employment_statuses.end_date >= ?) AND employee_employment_statuses.employment_status_id IN (?)', Date.today, status_ids)
                      .where('employee_benefits.end_date is NULL OR employee_benefits.end_date >= ?', Date.today).references(:employee_benefits, :employee_employment_statuses)

  CSV.open("#{Rails.root}/iuoe211_eligible_members_without_benefit_report_#{Time.now.strftime('%d-%m-%Y-%H:%M:%S')}.csv", 'w') do |csv|
    csv << ['Full ss#', 'First Name', 'Middle Initial', 'Last Name', 'DOB', 'Member Status']
    employees.each do |employee|
      next if employee.employee_benefits.pluck(:benefit_id).include?(benefit_id)

      csv << [employee.social_security_number, employee.first_name, employee.middle_name, employee.last_name, employee.birthday&.strftime('%d-%m-%Y') || '', employee.employment_status_name || '']
    end
  end
end

task :add_benefit_start_and_end_dates_for_eligible_members, [:account] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  @errors = {}
  included_statuses = EmployeeEmploymentStatus::CVS_ELIGIBLE_STATUSES
  excluded_statuses = %w[deceased terminated wfbenefitsuspension agencynotwf unionnotwf]
  eligible_employment_status_ids = EmploymentStatus.kept.where("lower(REPLACE(name, ' ', '')) IN (?)", included_statuses).pluck(:id)
  ineligible_employment_status_ids = EmploymentStatus.kept.where("lower(REPLACE(name, ' ', '')) IN (?)", excluded_statuses).pluck(:id)

  employees = Employee.kept.includes(:employee_employment_statuses)
                      .where('(employee_employment_statuses.end_date is NULL OR employee_employment_statuses.end_date >= ?) AND employee_employment_statuses.employment_status_id IN (?)',
                             Date.today, eligible_employment_status_ids + ineligible_employment_status_ids).references(:employee_employment_statuses)
  employees.each do |employee|
    @row_number = "#{employee.first_name} #{employee.last_name}"
    eligible_employment_statuses = employee.employee_employment_statuses.where('(start_date is NULL OR start_date <= ?) AND (end_date is NULL OR end_date >= ?) AND employment_status_id IN (?)',
                                                                               Date.today, Date.today, eligible_employment_status_ids).order(updated_at: :desc)
    ineligible_employment_statuses = employee.employee_employment_statuses.where('(end_date is NULL OR end_date >= ?) AND employment_status_id IN (?)',
                                                                                 Date.today, ineligible_employment_status_ids).order(updated_at: :desc)
    start_date = eligible_employment_statuses.first&.start_date
    end_date = ineligible_employment_statuses.first&.start_date || eligible_employment_statuses.first&.end_date
    ActiveRecord::Base.transaction do
      employee.employee_benefits.update_all(start_date: start_date)
      employee.employee_benefits.update_all(end_date: end_date)
    rescue StandardError => e
      iuoe211_feed_errors(e.message)
    end
  end

  CSV.open("#{Rails.root}/#{args[:account]}_benefit_start_date_update_#{Date.today}_errors.csv", 'w') do |csv|
    csv << ['Employee Name', 'Errors']

    @errors&.each do |error|
      csv << error
    end
  end
end

task :add_person_codes_for_cvs_eligible_coverages, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  prescription_benefit = Benefit.where(name: 'Prescription').first
  @errors = {}

  csv_file.each do |row|
    person_code = row['Member Sequence Number']&.strip
    next if person_code == '0'

    first_name = row['Member First Name']&.strip
    last_name = row['Member Last Name']&.strip
    birth_date = row['Date of Birth']&.strip || ''

    @row_number = "#{first_name} #{last_name}"

    coverages = BenefitCoverage.kept.joins(employee_benefit: :benefit).where('lower(first_name) LIKE ? AND lower(last_name) LIKE ? AND benefits.id = ?',
                                                                             "%#{first_name&.downcase}%", "%#{last_name&.downcase}%", prescription_benefit.id)
    coverages = coverages.where(birthday: Date.parse(birth_date)) if coverages.count > 1 && birth_date.present?
    if coverages.blank?
      iuoe211_feed_errors('Coverage not found')
      next
    elsif coverages.count > 1
      iuoe211_feed_errors('More than One Coverage found')
      next
    end
    coverage = coverages.first
    coverage.update_columns(person_code: person_code.to_i)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_person_code_update_#{Date.today}_errors.csv", 'w') do |csv|
    csv << ['Coverage Name', 'Errors']

    @errors&.each do |error|
      csv << error
    end
  end
end

task :iuoe211_retired_65_status_import, %i[account file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  @errors = {}
  retired65_status = EmploymentStatus.kept.where(name: 'Retired 65+').first_or_create!
  retired_status_id = EmploymentStatus.kept.where('lower(name) = ?', 'retired').first.id

  EmployeeEmploymentStatus.skip_callback :save, :after, :update_expiry_status
  EmployeeEmploymentStatus.skip_callback :save, :after, :set_coverage_person_code

  csv_file.each do |row|
    @row_number = row['Member Name']&.strip || ''
    next if @row_number.blank?

    name_split = @row_number.split
    f_name = name_split.first&.downcase
    l_name = name_split.last&.downcase
    m_name = name_split[1]&.downcase&.gsub('.', '') if name_split.size == 3
    employees = find_employee_by_name(f_name, m_name, l_name)
    if employees.count.zero?
      iuoe211_feed_errors('Invalid Member or Member was missing')
      next
    elsif employees.count > 1
      iuoe211_feed_errors('More than One Member found')
      next
    end
    employee = employees.first
    retired_status = employee.employee_employment_statuses.where('employment_status_id = ? AND (end_date IS NULL OR end_date >= ?)', retired_status_id, Date.today)
    if retired_status.blank?
      iuoe211_feed_errors('No Retired Status Found for this Member')
      next
    end
    retired_status.first.update!(employment_status_id: retired65_status.id)
  rescue StandardError => e
    iuoe211_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_retired65_status_import_#{Date.today}_errors.csv", 'w') do |csv|
    csv << ['Member Name', 'Errors']

    @errors&.each do |error|
      csv << error
    end
  end

  EmployeeEmploymentStatus.set_callback :save, :after, :update_expiry_status
  EmployeeEmploymentStatus.set_callback :save, :after, :set_coverage_person_code
end

def find_employee_by_name(f_name, m_name, l_name)
  if m_name.present?
    employees = Employee.kept.where('lower(first_name) LIKE ? AND lower(last_name) LIKE ? AND lower(middle_name) LIKE ?', "%#{f_name}%", "%#{l_name}%", "%#{m_name}%")
    employees = Employee.kept.where('lower(first_name) LIKE ? AND lower(last_name) LIKE ?', "%#{f_name} #{m_name}%", "%#{l_name}%") if employees.empty?
    employees = Employee.kept.where('lower(first_name) LIKE ? AND lower(last_name) LIKE ?', "%#{f_name}%", "%#{m_name} #{l_name}%") if employees.empty?
  else
    employees = Employee.kept.where('lower(first_name) LIKE ? AND lower(last_name) LIKE ?', "%#{f_name}%", "%#{l_name}%")
  end
  employees
end
def format_employee_and_coverage_data(employee, coverage = nil)
  if coverage.nil?
    [
      employee.first_name, '', employee.last_name, employee.street, employee.apartment, '', '',
      employee.city, employee.state, employee.zipcode, '', '', employee.birthday&.strftime('%m/%d/%Y'),
      format_gender(employee.gender.name), '', '', '', '', employee.prescription,
      '', '', '', '', '', '', '', '', '', '', '', '', '', format_phone(employee)
    ]
  else
    coverage_address = coverage.address&.gsub(/[\n\r]/, '')
    coverage_same_as_member = ['same as member', 'same as membe'].include?(coverage_address)
    address = if employee.full_address == coverage_address || coverage_same_as_member
                [employee.street, employee.apartment, employee.city, employee.state, employee.zipcode]
              else
                get_coverage_address_details(coverage_address, employee)
              end
    [
      format_coverage_name(coverage.name, 'first_name'), '', format_coverage_name(coverage.name, 'last_name'),
      address[0], address[1], '', '', address[-3], address[-2], address[-1], '', '', coverage.birthday&.strftime('%m/%d/%Y'),
      format_gender(coverage.gender&.name), '', '', '', '', employee.prescription, '', '', '', '', '', '', '', '', '', '', '', '', '', ''
    ]
  end
end

def get_coverage_address_details(coverage_address, employee)
  return [employee.street, employee.apartment, employee.city, employee.state, employee.zipcode] if coverage_address.blank?

  address_array = coverage_address.split(',')&.map(&:strip) || []
  street = address_array[0]
  apartment = address_array.size == 4 ? address_array[1] : ''
  if address_array.size >= 3
    city = address_array[-2]
    state_zip_parts = address_array.last&.split || []
    state = state_zip_parts[0]
    zipcode = state_zip_parts[1]
  else
    empty_address = ''
  end

  city.present? ? [street, apartment, city, state, zipcode] : [street, apartment, empty_address]
end

def format_coverage_name(name, field)
  name_array = name.split
  l_name = name_array.count > 3 ? name_array[-2] : name_array.last # Assuming 4th word will be the suffix
  field == 'first_name' ? name_array.first : l_name
end

def format_gender(gender)
  if gender&.downcase == 'female'
    'F'
  elsif gender&.downcase == 'male'
    'M'
  else
    'U'
  end
end

def format_phone(member)
  member.contacts.where(contact_type: 'phone', contact_for: 'personal').where.not(value: nil).first.value.delete(' ()-')
rescue StandardError
  ''
end

def age(dob)
  now = Date.today
  age = now.year - dob.year
  age = 1 if now.year == dob.year
  age
end

def iuoe211_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end

# bundle exec rake 'screening_benefit_cleanup'
# bundle exec rake 'iuoe211_report[iuoe211, VT Std address file(Yellow required)-08212024/VT Std ddress File-Table 1.csv]'
# bundle exec rake 'iuoe211_effective_date_update_for_dependents[iuoe211]'
# bundle exec rake 'iuoe211_eligible_members_without_benefit[iuoe211, Prescription Drug Benefit]'
# bundle exec rake 'add_benefit_start_and_end_dates_for_eligible_members[iuoe211]'
# bundle exec rake 'add_person_codes_for_cvs_eligible_coverages[iuoe211, ALFZ_Final.csv]'
# bundle exec rake 'iuoe211_retired_65_status_import[iuoe211, IUOE Retired 65+ (1).csv]'
# bundle exec rake 'add_person_codes_for_cvs_eligible_coverages[btoba, araya_final_file.csv]'
