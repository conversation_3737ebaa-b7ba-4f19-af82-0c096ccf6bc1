# frozen_string_literal: true

require 'csv'

# bundle exec rake 'iuoe211_import_member_details[IUOE_MEMBERS.csv]'
# bundle exec rake 'iuoe211_import_dependent_details[IUOE_DEPENDENTS.csv]'
# bundle exec rake 'iuoe211_import_beneficiaries_details[IUOE_MEMBERS.csv]'

desc 'import data'
task :iuoe211_import_member_details, [:file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!('iuoe211')

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  @union = EmploymentStatus.where(name: 'Union').first_or_create
  @agency = EmploymentStatus.where(name: 'Agency').first_or_create
  @cobra = EmploymentStatus.where(name: 'COBRA').first_or_create
  @deceased = EmploymentStatus.where(name: 'Deceased').first_or_create
  @retired = EmploymentStatus.where(name: 'Retired').first_or_create
  @suspended = EmploymentStatus.where(name: 'Suspended').first_or_create
  @terminated = EmploymentStatus.where(name: 'Terminated').first_or_create
  @leave = EmploymentStatus.where(name: 'Leave').first_or_create

  @errors = {}
  EMAIL_REGEX = Devise.email_regexp

  # Last Name,First Name,MIDDLE_INIT,Address,City,ST,ZIP,SSN,PHONE,E-MAIL,CELL_PHONE,GENDER,Ethnic Origin,County,Prescription_ID,
  # Beneficiary,Relationship,BENEFICIARY_ADD,BENEFICIARY_ADD2,BENEFICIARY_SSN,INIT_DATE,DOB,RETIREMENT_DATE,Classification,
  # ACTIVE_COD,NOTES,Agency,RegistrationNumber

  csv_file.each do |row|

    next if row['ACTIVE_COD'].present? && %w[t d].include?(row['ACTIVE_COD'].downcase)

    first_name = row['First Name'] || ''
    last_name = row['Last Name'] || ''
    mi = row['MIDDLE_INIT'] || ''

    @row_number = first_name + ' ' + last_name + ' ' + mi

    street = row['Address'] || ''
    apt = ''
    city = row['City'] || ''
    state = row['ST'] || ''
    zipcode = row['ZIP'].present? ? row['ZIP'].split('-').first.rjust(5, '0').first(5) : ''
    ssn = iuoe211_parse_ssn(row['SSN'])
    home_phone = parse_phone(row['PHONE'])
    cell_phone = parse_phone(row['CELL_PHONE'])
    email = row['E-MAIL'] || ''
    gender = iuoe211_check_gender(row['GENDER'])
    notes = [row['Ethnic Origin'], row['County'], row['INIT_DATE'], row['NOTES']]
    prescription_id = row['Prescription_ID'] || ''

    beneficiary_name = row['Beneficiary'] || ''
    beneficiary_relation = row['Relationship'] || ''
    beneficiary_address = [row['BENEFICIARY_ADD'], row['BENEFICIARY_ADD2']].reject(&:blank?).join(', ') || ''
    beneficiary_ssn = iuoe211_parse_ssn(row['BENEFICIARY_SSN'])

    dob = iuoe211_parse_date(row['DOB'], 'DOB')
    retired_date = iuoe211_parse_date(row['RETIREMENT_DATE'], 'RETIREMENT_DATE')
    registration = row['RegistrationNumber'] || ''

    title = iuoe211_check_rank(row['Classification'])
    employment_status = iuoe211_check_employment_status(row['ACTIVE_COD'])
    officer_status = iuoe211_check_officer_status(row['Agency'])

    @current_account = Account.find_by(subdomain: Apartment::Tenant.current)

    employee = Employee.new

    employee.first_name = first_name
    employee.last_name = last_name
    employee.middle_name = mi
    employee.street = street
    employee.apartment = apt
    employee.city = city
    employee.state = state
    employee.zipcode = zipcode
    employee.notes = notes.reject(&:blank?).join('. ')
    employee.social_security_number = ssn
    employee.prescription = prescription_id
    employee.a_number = registration
    employee.birthday = dob
    employee.gender_id = gender.id if gender

    if employee.save
      employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE, value: cell_phone).save! if cell_phone
      employee.contacts.new(contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE, value: home_phone).save! if home_phone
      employee.contacts.new(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE).save!

      if email.present? && EMAIL_REGEX.match(email)
        employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL, value: email).save!
      else
        employee.contacts.new(contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL).save!
        iuoe211_feed_errors("Invalid Email address")
      end
      employee.contacts.new(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL).save!

      employee.employee_ranks.new(rank_id: title.id).save(validate: false) if title
      employee.employee_officer_statuses.new(officer_status_id: officer_status.id).save(validate: false) if officer_status

      if employment_status
        employee_employment_status = employee.employee_employment_statuses.new(employment_status_id: employment_status.id)
        employee_employment_status.start_date = retired_date if retired_date.present? && row['ACTIVE_COD'] == 'R'
        employee_employment_status.save(validate: false)
      end

      employee.beneficiaries.new(address: beneficiary_address, name: beneficiary_name, relationship: beneficiary_relation, social_security_number: beneficiary_ssn).save(validate: false)
    else
      @errors[@row_number] = employee.errors.full_messages
    end

  rescue => e
    p @row_number, e.message
    iuoe211_feed_errors(e.message)
  end

  file_name = args[:file_path].split('.').first

  CSV.open("#{Rails.root}/IUOE211_#{Date.today}_#{file_name}_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end

task :iuoe211_import_dependent_details, [:file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!('iuoe211')

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  # MainID,FirstName,LastName,Relationship,DateofBirth,Notes,Gender

  csv_file.each do |row|
    ssn = row['MainID']
    member_ssn = iuoe211_parse_ssn(ssn)
    @row_number = ssn

    employee = Employee.find_by(social_security_number: member_ssn)

    unless employee.present?
      iuoe211_feed_errors("Member Not found")
      next
    end

    coverage = employee.benefit_coverages.new
    coverage.name = [row['FirstName'], row['LastName']].reject(&:blank?).join(' ')
    coverage.relationship = row['Relationship'] || ''
    coverage.social_security_number = '***********'
    coverage.birthday = iuoe211_parse_dependent_dob(row['DateofBirth'])
    sex = nil
    if row['Gender'] == 'M'
      sex = iuoe211_check_gender('Male')
    elsif row['Gender'] == 'F'
      sex = iuoe211_check_gender('Female')
    end
    coverage.dependent = row['Notes'] || ''
    coverage.gender_id = sex.id if sex
    coverage.save

  rescue => e
    p @row_number, e.message
    iuoe211_feed_errors(e.message)
  end

  file_name = args[:file_path].split('.').first

  CSV.open("#{Rails.root}/IUOE211_#{Date.today}_#{file_name}_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end

task :iuoe211_import_beneficiaries_details, [:file_path] => :environment do  |_t, args|
  Apartment::Tenant.switch!('iuoe211')

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  csv_file.each do |row|
    ssn = row['SSN']
    member_ssn = iuoe211_parse_ssn(ssn)
    @row_number = ssn

    employee = Employee.find_by(social_security_number: member_ssn)

    unless employee.present?
      iuoe211_feed_errors("Member Not found")
      next
    end

    beneficiary_name = row['Beneficiary'] || ''
    beneficiary_relation = row['Relationship'] || ''
    beneficiary_address = [row['BENEFICIARY_ADD'], row['BENEFICIARY_ADD2']].reject(&:blank?).join(', ') || ''
    beneficiary_ssn = iuoe211_parse_ssn(row['BENEFICIARY_SSN'])

    employee.beneficiaries.new(address: beneficiary_address, name: beneficiary_name, relationship: beneficiary_relation, social_security_number: beneficiary_ssn).save(validate: false)
  rescue => e
    p @row_number, e.message
    iuoe211_feed_errors(e.message)
  end

  file_name = args[:file_path].split('.').first

  CSV.open("#{Rails.root}/IUOE211_#{Date.today}_#{file_name}_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

def iuoe211_parse_ssn(ssn)
  return nil unless ssn.present?

  ssn.remove!('(', ')', ' ', '-')

  ssn = ssn.rjust(9, '0')

  social_security = ssn.first(3) + '-' + ssn[3..4] + '-' + ssn.last(4)

  social_security

rescue => e
  nyscoa_iuoe211_feed_errors('SSN ' + e.message)
end

def iuoe211_check_gender(gender_type)
  return nil unless gender_type.present?

  gender = Gender.where(name: gender_type.titleize).first_or_create

  gender

rescue => e
  iuoe211_feed_errors('GENDER ' + gender.errors.full_messages)
end

def iuoe211_check_employment_status(employment_status_type)
  return nil unless employment_status_type.present?

  case employment_status_type
  when 'A'
    employment_status = @union
  when 'N'
    employment_status = @agency
  when 'C'
    employment_status = @cobra
  when 'D'
    employment_status = @deceased
  when 'R'
    employment_status = @retired
  when 'S'
    employment_status = @suspended
  when 'T'
    employment_status = @terminated
  when 'L'
    employment_status = @leave
  else
    employment_status = nil
    iuoe211_feed_errors('EMPLOYMENT_STATUS - Invalid status' + employment_status_type)
  end

  employment_status

rescue => e
  iuoe211_feed_errors('EMPLOYMENT_STATUS ' + employment_status.errors.full_messages)
end

def iuoe211_check_officer_status(officer_status_name)
  return nil unless officer_status_name.present?

  officer_status = OfficerStatus.where(name: officer_status_name).first_or_create

  officer_status

rescue => e
  iuoe211_feed_errors('OFFICER STATUS ' + officer_status.errors.full_messages)
end

def iuoe211_check_rank(rank_name)
  return nil unless rank_name.present?

  rank = Rank.where(name: rank_name).first_or_create

  rank

rescue => e
  iuoe211_feed_errors('RANK ' + rank.errors.full_messages)
end

def iuoe211_parse_date(date, type)
  return nil unless date.present?

  parsed_date = nil

  begin
    date_array = date.split('/')
    day = date_array[1].to_i
    month = date_array[0].to_i
    year = date_array[2].to_i

    if year.to_s.length == 2
      if year > 20 && year <= 99
        year = ('19' + year.to_s).to_i
      else
        year = ('20' + year.to_s).to_i
      end
    elsif year.to_s.length == 1
      year = ('200' + year.to_s).to_i
    elsif year.to_s.length > 4
      raise 'Invalid Year'
    else
      year = year.to_i
    end

    parsed_date = Date.new(year, month, day)
  rescue
    parsed_date = Date.parse(date)
  end

  parsed_date

rescue => e
  iuoe211_feed_errors('DATE - ' + type + " #{date} " + e.message)
end

def iuoe211_parse_dependent_dob(date)
  return nil unless date.present?

  parsed_date = nil

  begin
    date_array = date.split('-')
    day = date_array[0].to_i
    month = date_array[1]
    year = date_array[2].to_i

    if year.to_s.length == 2
      if year > 22 && year <= 99
        year = ('19' + year.to_s).to_i
      else
        year = ('20' + year.to_s).to_i
      end
    elsif year.to_s.length == 1
      year = ('200' + year.to_s).to_i
    elsif year.to_s.length > 4
      raise 'Invalid Year'
    else
      year = year.to_i
    end

    parsed_date = Date.parse("#{day}-#{month}-#{year}")
  rescue
    parsed_date = Date.parse(date)
  end

  parsed_date

rescue => e
  iuoe211_feed_errors('DATE - DEPENDENT DOB' + " #{date} " + e.message)
end

def iuoe211_parse_phone(phone)
  return nil unless phone.present?

  phone.remove!('(', ')', ' ', '-')

  phone_number = '(' + phone.first(3) + ') ' + phone[3..5] + ' - ' + phone.last(4)

  phone_number

rescue => e
  iuoe211_feed_errors('PHONE ' + e.message)
end

def iuoe211_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end
