# frozen_string_literal: true

require 'csv'

desc 'import data'

task :nyspec_import_member_details, [:account, :file_path, :type] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  type = args[:type]
  @errors = {}

  contacts_hash = [
    { contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE, value: '' },
    { contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE, value: '' },
    { contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL, value: '' },
    { contact_for: Contact::ContactFor::COLLEAGUE, contact_type: Contact::ContactType::EMERGENCY, value: '' },
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMERGENCY, value: '' },
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL, value: '' },
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE, value: '' }
  ]
  rank = Rank.where('lower(name)=?', 'president').first_or_create!(name: 'President') if type == 'president'
  csv_file.each do |row|
    first_name = row['First Name'] || ''
    last_name = row['Last Name'] || ''
    @row_number = [first_name, last_name]
    nyspec_feed_errors("first name or last name is blank") if first_name.blank? || last_name.blank?
    email = row['Email Address'] || ''
    phone = row['Phone Number'] || ''
    position_name = row['Union Name'] || ''
    position_name = Position.where('lower(name) = ?', position_name.downcase.strip).first_or_create!(name: position_name.strip) if position_name.present?
    office_name = row['Company Name'] || ''
    office_name = Office.where('lower(name) = ?', office_name.downcase.strip).first_or_create!(name: office_name.strip) if office_name.present?

    employees = Employee.kept.where('lower(first_name) = ? and lower(last_name) = ?', first_name.downcase.strip, last_name.downcase.strip)
    if email.present?
      if email.present?
        employees = employees.joins(:contacts).where("lower(contacts.value) = ?", email.strip.downcase).where(contacts: { contact_for: "personal", contact_type: "email" })
      end
    end
    if employees.count > 1
      nyspec_feed_errors("More than one employee found")
    end

    if employees.present?
      employee = employees.first
      nyspec_feed_errors("Employee already exists")
    else
      employee = Employee.create!(first_name: first_name.strip, last_name: last_name.strip)
      employee.contacts.import contacts_hash
    end

    phone_number = employee.contacts.where(contact_type: 'phone', contact_for: 'personal').first
    email_contact = employee.contacts.where(contact_type: 'email', contact_for: 'personal').first
    phone_number.update_columns(value: nyspec_parse_phone(phone)) if phone.present?

    email_contact.update_columns(value: email.strip) if email.present?
    
    if office_name.present?
      employee.employee_offices.where(office_id: office_name.id).first_or_create!
    elsif position_name.present?
      employee.employee_positions.where(position_id: position_name.id).first_or_create!
      employee.employee_ranks.where(rank_id: rank.id).first_or_create! if rank.present?
    end
  rescue => e
    p @row_number, e.message
    nyspec_feed_errors(e.message)
  end


  CSV.open("#{Rails.root}/#{args[:account]}_#{Time.now.strftime('%Y%m%d%H%M%S')}_nyspec_import_#{type}_details.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

      @errors.each do |error|
        csv << error
      end
  end
end

task :nyspec_add_active_status, [:account, :file_path, :type] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  type = args[:type]
  active_status = EmploymentStatus.kept.where('lower(name) = ?', 'active').first_or_create!(name: 'Active')
  director = Rank.kept.where('lower(name)=?', 'director').first_or_create!(name: 'Director')
  sponsor = Rank.kept.where('lower(name)=?', 'sponsor').first_or_create!(name: 'Sponsor')
  @errors = {}

  csv_file.each do |row|
    first_name = row['First Name'] || ''
    last_name = row['Last Name'] || ''
    @row_number = [first_name, last_name]
    email = row['Email Address'] || ''

    employees = Employee.kept.where('lower(first_name) = ? and lower(last_name) = ?', first_name.downcase.strip, last_name.downcase.strip)
    if email.present?
      if email.present?
        employees = employees.joins(:contacts).where("lower(contacts.value) = ?", email.strip.downcase).where(contacts: { contact_for: "personal", contact_type: "email" })
      end
    end
    if employees.count > 1
      nyspec_feed_errors("More than one employee found")
    end

    if employees.present?
      employee = employees.first
      employee.employee_employment_statuses.where(employment_status_id: active_status.id).first_or_create!

      employee.employee_ranks.kept.where(rank_id: director.id).first_or_create! if type == 'president'

      employee.employee_ranks.kept.where(rank_id: sponsor.id).first_or_create! if type == 'providers'
    end
  rescue => e
    p @row_number, e.message
    nyspec_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Time.now.strftime('%Y%m%d%H%M%S')}_nyspec_add_active_status.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

      @errors.each do |error|
        csv << error
      end
  end
end

task :nyspec_update_contacts, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  @errors = {}
  csv_file.each do |row|
    first_name = row['First Name'] || ''
    last_name = row['Last Name'] || ''
    @row_number = [first_name, last_name]
    email = row['E-mail'] || ''
    employees = Employee.kept.where('lower(first_name) = ? and lower(last_name) = ?', first_name.downcase.strip, last_name.downcase.strip)
    if employees.count > 1 || employees.blank?
      nyspec_feed_errors("More than one employee found") if employees.count > 1
      nyspec_feed_errors("Employee not found") if employees.blank?
      next
    end
    employee = employees.first
    required_contacts = [
      { contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE },
      { contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE },
      { contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL },
      { contact_for: Contact::ContactFor::COLLEAGUE, contact_type: Contact::ContactType::EMERGENCY },
      { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMERGENCY },
      { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL },
      { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE }
    ]

    if employee&.contacts&.count < required_contacts.count
      required_contacts.each do |contact_attrs|
        employee.contacts.find_or_create_by( contact_for: contact_attrs[:contact_for], contact_type: contact_attrs[:contact_type])
      end
    end

    if email.present?
      email_contact = employee.contacts.where(contact_type: 'email', contact_for: 'personal').first
      email_contact.update_columns(value: email.strip) if email_contact.present?
    end

  rescue => e
    p @row_number, e.message
    nyspec_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Time.now.strftime('%Y%m%d%H%M%S')}_nyspec_update_contacts.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

      @errors.each do |error|
        csv << error
      end
  end
end

def nyspec_parse_phone(phone)
  return nil unless phone.present?
  return nil if phone.length < 10

  phone.remove!(/\D/).first(10)

  phone_number = '(' + phone.first(3) + ') ' + phone[3..5] + ' - ' + phone.last(4)

  phone_number

rescue => e
  feed_errors('PHONE ' + e.message)
end


def nyspec_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end

# bundle exec rake 'nyspec_import_member_details[nyspec,NYSPEC_PRESIDENTS.csv,president]'
# bundle exec rake 'nyspec_import_member_details[nyspec,NYSPEC_Providers.csv,providers]'
# bundle exec rake 'nyspec_add_active_status[nyspec,NYSPEC_PRESIDENTS.csv,president]'
# bundle exec rake 'nyspec_add_active_status[nyspec,NYSPEC_Providers.csv,providers]'
# bundle exec rake 'nyspec_update_contacts[nyspec,NYSPEC_import_contact.csv]'
