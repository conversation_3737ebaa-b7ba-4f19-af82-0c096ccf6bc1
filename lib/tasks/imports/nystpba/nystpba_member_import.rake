# frozen_string_literal: true

require 'csv'

desc 'import data'

task :nystpba_members_import, %i[account file_path] => :environment do |t, args|
  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  contacts_hash = [
    { contact_for: Contact::ContactFor::HOME, contact_type: Contact::ContactType::PHONE, value: '' },
    { contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::PHONE, value: '' },
    { contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL, value: '' },
    { contact_for: Contact::ContactFor::COLLEAGUE, contact_type: Contact::ContactType::EMERGENCY, value: '' },
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMERGENCY, value: '' },
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::EMAIL, value: '' },
    { contact_for: Contact::ContactFor::PERSONAL, contact_type: Contact::ContactType::PHONE, value: '' }
  ]

  Employee.skip_callback :save, :after, :update_benefit_address
  Employee.skip_callback :commit, :after, :update_legislative_detail
  EmployeeEmploymentStatus.skip_callback :commit, :after, :update_expiry_status
  count = 0
  employee_statuses_array = []
  employee_positions_array = []
  employee_offices_array = []
  employee_ranks_array = []
  contacts_array = []
  employee_ids = []

  csv_file.each do |row|
    first_name = row['First Name']&.strip || ''
    last_name = row['Last Name']&.strip || ''
    birthday = row['DoB'].present? ? Date.strptime(row['DoB'], '%m/%d/%Y') : ''
    entry_date = row['Entry Date'].present? ? Date.strptime(row['Entry Date'], '%m/%d/%Y') : ''
    retirement_date = row['Retirement Date'].present? ? Date.strptime(row['Retirement Date'], '%m/%d/%Y') : ''
    @row_number = [first_name, last_name]
    new_record = false

    if first_name.blank? || last_name.blank?
      nystpba_feed_errors('Mandatory details not present')
      next
    end
    status_id = create_associated_model_and_values('EmploymentStatus', row['Status'])&.id
    position_id = create_associated_model_and_values('Position', row['Delegate #'])&.id
    office_id = create_associated_model_and_values('Office', row['Troop'])&.id
    rank_id = create_associated_model_and_values('Rank', row['Rank'])&.id


    employees = Employee.kept.includes(:employee_offices, :employee_ranks, :employee_positions, :employee_employment_statuses).where('lower(first_name) = ? AND lower(last_name) = ?', first_name.downcase, last_name.downcase)
    employees = employees.where('lower(middle_name) = ?', row['Middle Name']&.strip&.downcase || '') if row['Middle Name'].present?
    employees = employees.where(birthday: birthday) if birthday.present?
    if employees.count > 1
      nystpba_feed_errors('More than One Member found')
      next
    end

    employee = employees.count == 1 ? employees.first : Employee.new
    employee.first_name = first_name
    employee.last_name = last_name
    employee.middle_name = row['Middle Name'] || ''
    employee.birthday = birthday
    employee.start_date = entry_date if entry_date.present?
    employee.ncc_date = retirement_date if retirement_date.present?

    new_record = true if employee.new_record?
    unless employee.save!
      @errors[@row_number] = employee.errors.full_messages
      next
    end
    employee.contacts.import contacts_hash if new_record
    employee_ids << employee.id

    contact = employee.contacts.where(contact_type: 'phone', contact_for: 'personal').first
    contact_number = nystpba_parse_phone(row['Cell Phone #']).presence || nystpba_parse_phone(row['Phone #']).presence || ''
    contacts_array << { id: contact.id, value: contact_number, employee_id: employee.id, contact_type: 'phone', contact_for: 'personal' } if contact.present? && contact.value != contact_number

    employee_positions_array << { id: employee.employee_positions&.first&.id, employee_id: employee.id, position_id: position_id } if position_id.present? && employee.employee_positions&.first&.position_id != position_id
    if employee.employee_positions&.first&.position_id && position_id.blank?
      @errors[@row_number] = "Previously Delegate Number is present but not in the file"
    end
    employee_offices_array << { id: employee.employee_offices&.first&.id, employee_id: employee.id, office_id: office_id } if office_id.present? && employee.employee_offices&.first&.office_id != office_id
    if employee.employee_offices&.first&.office_id && office_id.blank?
      @errors[@row_number] = "Previously Troop is present but not in the file"
    end
    employee_ranks_array << { id: employee.employee_ranks&.first&.id, employee_id: employee.id, rank_id: rank_id } if rank_id.present? && employee.employee_ranks&.first&.rank_id != rank_id
    if employee.employee_ranks&.first&.rank_id && rank_id.blank?
      @errors[@row_number] = "Previously Rank is present but not in the file"
    end
    employee_statuses_array << { id: employee.employee_employment_statuses&.first&.id, employee_id: employee.id, employment_status_id: status_id } if status_id.present? && employee.employee_employment_statuses&.first&.employment_status_id != status_id
    if employee.employee_employment_statuses&.first&.employment_status_id && status_id.blank?
      @errors[@row_number] = "Previously Status is present but not in the file"
    end


    count += 1
    if count == 500 && (employee_statuses_array.present? || employee_positions_array.present? || employee_offices_array.present? || employee_ranks_array.present? || contacts_array.present?)
      EmployeeEmploymentStatus.import employee_statuses_array.flatten, on_duplicate_key_update: [:employment_status_id], validate: false
      EmployeePosition.import employee_positions_array.flatten, on_duplicate_key_update: [:position_id], validate: false
      EmployeeOffice.import employee_offices_array.flatten, on_duplicate_key_update: [:office_id], validate: false
      EmployeeRank.import employee_ranks_array.flatten, on_duplicate_key_update: [:rank_id], validate: false
      Contact.import contacts_array.flatten, on_duplicate_key_update: [:value], validate: false
      employee_statuses_array = []
      employee_positions_array = []
      employee_offices_array = []
      employee_ranks_array = []
      contacts_array = []
      count = 0
    end

  rescue StandardError => e
    p @row_number, e.message
    nystpba_feed_errors(e.message)
  end
  Employee.kept.where.not(id: employee_ids).each do |employee|
    @row_number = [employee.first_name, employee.last_name]
    nystpba_feed_errors('Member Not found in the file')
  end
  Employee.kept.where.not(id: employee_ids).discard_all

  generate_nystpba_error_report_csv(t.name)

  EmployeeEmploymentStatus.import employee_statuses_array.flatten, on_duplicate_key_update: [:employment_status_id], validate: false
  EmployeePosition.import employee_positions_array.flatten, on_duplicate_key_update: [:position_id], validate: false
  EmployeeOffice.import employee_offices_array.flatten, on_duplicate_key_update: [:office_id], validate: false
  EmployeeRank.import employee_ranks_array.flatten, on_duplicate_key_update: [:rank_id], validate: false
  Contact.import contacts_array.flatten, on_duplicate_key_update: [:value], validate: false

  Employee.set_callback :save, :after, :update_benefit_address
  Employee.set_callback :commit, :after, :update_legislative_detail
  EmployeeEmploymentStatus.set_callback :commit, :after, :update_expiry_status
end

task :nystpba_contacts_update, %i[account file_path] => :environment do |t, args|
  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  contacts_array = []
  count = 0
  csv_file.each do |row|
    first_name = row['First Name']&.strip || ''
    last_name = row['Last Name']&.strip || ''
    @row_number = [first_name, last_name]

    if first_name.blank? || last_name.blank?
      nystpba_feed_errors('Mandatory details not present')
      next
    end
    contact_number = nystpba_parse_phone(row['Cell Phone #']).presence || nystpba_parse_phone(row['Phone #']).presence || ''
    birthday = row['DoB'].present? ? Date.strptime(row['DoB'], '%m/%d/%Y') : ''

    employees = Employee.kept.where('lower(first_name) = ? AND lower(last_name) = ?', first_name.downcase, last_name.downcase)
    employees = employees.where('lower(middle_name) = ?', row['Middle Name']&.strip&.downcase || '') if employees.count > 1
    employees = employees.where(birthday: birthday) if birthday.present? && employees.count > 1
    if employees.count.zero?
      nystpba_feed_errors('Invalid Member or Member was missing')
      next
    elsif employees.count > 1
      nystpba_feed_errors('More than One Member found')
      next
    end
    employee = employees.first
    contact_id = employee.contacts.where(contact_type: 'phone', contact_for: 'personal').first.id
    contacts_array << { id: contact_id, value: contact_number, employee_id: employee.id, contact_type: 'phone', contact_for: 'personal' } if contact_id.present?
    if count == 500
      Contact.import contacts_array.flatten, on_duplicate_key_update: [:value]
      contacts_array = []
      count = 0
    end
    count += 1
  rescue StandardError => e
    p @row_number, e.message
    nystpba_feed_errors(e.message)
  end
  Contact.import contacts_array.flatten, on_duplicate_key_update: [:value]

  generate_nystpba_error_report_csv(t.name)
end

task :nystpba_add_entry_date, %i[account file_path] => :environment do |t, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  csv_file.each do |row|
    first_name = row['First Name']&.strip || ''
    last_name = row['Last Name']&.strip || ''
    birthday = row['DoB'].present? ? Date.strptime(row['DoB'], '%m/%d/%Y') : ''
    entry_date = row['Entry Date'].present? ? Date.strptime(row['Entry Date'], '%m/%d/%Y') : ''
    @row_number = [first_name, last_name]

    if first_name.blank? || last_name.blank?
      nystpba_feed_errors('Mandatory details not present')
      next
    end

    employees = if birthday.present?
                  Employee.kept.where('lower(first_name) = ? AND lower(last_name) = ? AND birthday = ?', first_name.downcase, last_name.downcase, birthday)
                else
                  Employee.kept.where('lower(first_name) = ? AND lower(last_name) = ?', first_name.downcase, last_name.downcase)
                end
    employees = employees.where('lower(middle_name) = ?', row['Middle Name']&.strip&.downcase || '') if employees.count > 1

    case employees.count
    when 0
      nystpba_feed_errors('Employee not found')
      next
    when 1
      employees.first.update_columns(start_date: entry_date) if entry_date.present?
    else
      nystpba_feed_errors('More than One Employee found')
      next
    end
  end
  generate_nystpba_error_report_csv(t.name)
end

def nystpba_parse_phone(phone)
  return nil unless phone.present?
  return nil if phone.length < 10

  phone.remove!('(', ')', ' ', '-')

  '(' + phone.first(3) + ') ' + phone[3..5] + ' - ' + phone.last(4)
rescue StandardError => e
  nystpba_feed_errors('PHONE ' + e.message)
end

def create_associated_model_and_values(model_name, value)
  return nil unless value.present?

  model_name.constantize.kept.where('lower(name) = ?', value.strip.downcase).first_or_create!(name: value.strip)
rescue StandardError => e
  nystpba_feed_errors("#{model_name} " + e.message)
end

def generate_nystpba_error_report_csv(file_name)
  CSV.open("#{Rails.root}/#{file_name}_errors_#{Date.today}.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']

    @errors.each do |error|
      csv << error
    end
  end
end

def nystpba_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end


# bundle exec rake 'nystpba_members_import[nystpba, nystpba_Members_2025-02-27.csv]'
# bundle exec rake 'nystpba_contacts_update[nystpba, nystpba_Members_2025-02-27.csv]'
# bundle exec rake 'nystpba_add_entry_date[nystpba,May2025_SMS_Upload]'
