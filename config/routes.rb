# frozen_string_literal: true

Rails.application.routes.draw do
  # For details on the DSL available within this file, see https://guides.rubyonrails.org/routing.html

  # ============================================== Web APIs ============================================================
  scope module: 'api', format: 'json' do
    resource :account, only: :show
    get 'accounts/app_version' => 'accounts#app_version'

    # Authentication related APIs
    post 'users/login' => 'auth#create'
    delete 'users/logout' => 'auth#destroy'
    get 'refresh_jwt_token' => 'auth#refresh_jwt_token'
    post 'users/forgot_password' => 'passwords#forgot_password'
    patch 'users/reset_password' => 'passwords#reset_password'
    get 'authenticate' => 'auth#authenticate'

    # <PERSON><PERSON><PERSON> and <PERSON>mplaints
    post 'bounce_and_complaints' => 'bounce_and_complaints#create'

    # Notification Tracker
    resources :notification_trackers, only: %i[create index] do
      get 'analytics', on: :collection
    end

    # Sms Tracker
    resources :sms_trackers, only: %i[create]

    # User rights API
    resources :user_rights, only: :index
    get 'roles_list' => 'user_rights#roles_list'

    resources :forms, only: :index
    #Reminder API
    resources :reminders
    #ReminderTracker
    resources :reminder_trackers, only: %i[index update destroy]
    # Notifications API
    resources :notifications, only: %i[create index]
    resources :email_preview_file_uploads, only: %i[create update]
    post :email_preview_file_uploads_s3, to: 'email_templates#email_preview_file_uploads_s3'
    post :delete_email_preview_file, to: 'email_templates#delete_email_preview_file'
    get 'push_notification_index', to: 'notifications#push_notification_index'
    get 'push_notification_show', to: 'notifications#push_notification_show'
    post 'notification_receiver_count', to: 'notifications#notification_receiver_count'
    get 'scheduled_notifications_index', to: 'notifications#scheduled_notifications_index'
    patch 'scheduled_notification_update/:id', to: 'notifications#scheduled_notification_update'
    delete 'scheduled_notification_destroy/:id', to: 'notifications#scheduled_notification_destroy'

    # User and profile APIs
    resources :users, only: %i[index create update destroy]
    get 'profile', to: 'users#profile'
    patch 'profile_update', to: 'users#profile_update'

    # UserAudit APIs
    resources :user_audits, only: %i[index show]

    # Maillog APIs
    resources :maillogs, except: [:show]

    # Report related API
    resources :reports, only: :create

    scope module: 'employees' do
      # Authentication related APIs
      post 'employees/login', to: 'tokens#login'
      post 'employees/refresh_token', to: 'tokens#refresh'
      post 'employees/forgot_password' => 'passwords#forgot_password'
      post 'employees/verify_otp' => 'passwords#verify_otp'
      patch 'employees/reset_password' => 'passwords#reset_password'
      post 'employees/logout', to: 'tokens#logout'
      patch 'employees/update_password', to: 'employees#update_password'

      # profile API
      get 'employees/profile', to: 'employees#profile'
      get 'employees/employee_status_detail', to: 'employees#employee_status_detail'

      resources :employees, only: %i[index create update destroy show] do
        get 'delegate_employees', as: :delegate_employees, on: :collection
      end
      get 'address_fields_data', to: 'employees#address_fields_data'
      get 'employee_dropdown_data', to: 'employees#employee_dropdown_data'
      get 'contact_person_dropdown_data', to: 'employees#contact_person_dropdown_data'
      post 'title_code_update', to: 'employees#title_code_update'
      post 'login_credentials', to: 'employees#login_credentials'

      get 'analytics', to: 'leaves#analytics'
      get 'staff_member_employee', to: 'employees#staff_member_employee'
      patch 'update_allowed_fields', to: 'employees#update_allowed_fields'
      get 'notes_section', to: 'employees#notes_section'
      get 'exact_search', to: 'employees#exact_search'

      resources :change_requests, only: %i[index show create update]
      resources :change_request_uploads, only: %i[index create update destroy]
      resources :contacts, only: %i[index]
      resources :employee_positions, only: %i[index create update destroy]
      resources :employee_employment_statuses, only: %i[index create update destroy]
      resources :employee_officer_statuses, only: %i[index create update destroy]
      resources :employee_offices, only: %i[index create update destroy]
      resources :employee_facilities, only: %i[index create update destroy]
      resources :delegate_assignments, only: %i[index create update destroy]
      resources :employee_ranks, only: %i[index create update destroy]
      resources :employee_firearm_statuses, only: %i[index create update destroy]
      resources :employee_departments, only: %i[index create update destroy]
      resources :employee_sections, only: %i[index create update destroy]
      resources :employee_titles, only: %i[index create update destroy]
      resources :firearm_range_scores, only: %i[index create update destroy]
      resources :employee_benefits, only: %i[index create update destroy show]
      resources :beneficiaries, only: %i[index create update destroy]
      resources :disabilities, only: %i[index create update destroy]
      resources :awards, only: %i[index create update destroy]
      resources :employee_meeting_types, only: %i[index create update destroy]
      resources :uploads, only: %i[index create update destroy]
      resources :benefit_coverages, only: %i[index create update destroy]
      resources :benefit_disbursements, only: %i[index create update destroy] do
        collection do
          get 'get_benefit_claims'
        end
      end
      resources :employee_discipline_settings, only: %i[index create update destroy]
      resources :employee_discipline_steps, only: %i[create update]
      resources :peshes, only: %i[index create update destroy show]
      resources :employee_grievances, only: %i[index create update destroy] do
        collection do
          get 'get_max_case_number'
        end
      end
      resources :assaults, only: %i[index create update destroy show]
      resources :employee_grievance_steps, only: %i[create update]
      resources :hearings, only: %i[index]
      resources :leaves, only: %i[index create update destroy]
      resources :lodis, only: %i[index create update destroy]
      resources :denied_reasons, only: %i[index]
      resources :lodi_request_tabs, only: %i[index create update destroy]
      resources :employee_pacfs, only: %i[index create update destroy]
      resources :life_insurances, only: %i[index create update destroy]
      resources :dependents, only: %i[index create update destroy]
      resources :devices, only: %i[create]
      resources :poly_notes, only: %i[index create update destroy]
      resources :legislative_addresses, only: %i[index] do
        collection do
          get 'employee_legislative_address'
        end
      end
    end

    resources :email_templates, only: %i[index show] do
      member do
        post :preview
      end
    end

    scope module: 'settings' do
      resources :analytics_configurations, only: %i[index create update]
      resources :benefits, only: %i[index create update destroy show]
      resources :offices, only: %i[index create update destroy]
      resources :facilities, only: %i[index create update destroy]
      resources :firearm_statuses, only: %i[index create update destroy]
      resources :ranks, only: %i[index create update destroy]
      resources :employment_statuses, only: %i[index create update destroy]
      resources :officer_statuses, only: %i[index create update destroy]
      resources :marital_statuses, only: %i[index create update destroy]
      resources :delegate_series, only: %i[index create update destroy]
      resources :positions, only: %i[index create update destroy]
      resources :payment_types, only: %i[index create update destroy]
      resources :meeting_types, only: %i[index create update destroy]
      resources :departments, only: %i[index create update destroy show]
      resources :sections, only: %i[index create update destroy show]
      resources :grievances, only: %i[index create update destroy show]
      resources :pacfs, only: %i[index create update destroy show]
      resources :discipline_settings, only: %i[index create update destroy show]
      resources :discipline_charges, only: %i[index create update destroy show]
      resources :discipline_statuses, only: %i[index create update destroy show]
      resources :grievance_statuses, only: %i[index create update destroy show]
      resources :titles, only: %i[index create update destroy]
      resources :units, only: %i[index create update destroy]
      resources :genders, only: %i[index create update destroy]
      resources :affiliations, only: %i[index create update destroy]
      resources :tour_of_duties, only: %i[index create update destroy]
      resources :platoons, only: %i[index create update destroy]
    end
  end

  mount ActionCable.server => '/cable'
  get 'health_check', to: 'health_check#index'

  # ============================================== PG HERO =============================================================

  mount PgHero::Engine, at: "pghero"

  # ====================================================================================================================

  require 'sidekiq/web'

  Sidekiq::Web.use Rack::Auth::Basic do |username, password|
    ActiveSupport::SecurityUtils.secure_compare(::Digest::SHA256.hexdigest(username), ::Digest::SHA256.hexdigest(Rails.application.credentials.fuse_dev_username)) &
      ActiveSupport::SecurityUtils.secure_compare(::Digest::SHA256.hexdigest(password), ::Digest::SHA256.hexdigest(Rails.application.credentials.fuse_dev_password))
  end
  mount Sidekiq::Web => '/sidekiq'
end
