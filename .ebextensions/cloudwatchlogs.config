packages:
  yum:
    awslogs: []

files:
  "/etc/awslogs/awscli.conf" :
    mode: "000600"
    owner: root
    group: root
    content: |
      [plugins]
      cwlogs = cwlogs
      [default]
      region = `{"Ref":"AWS::Region"}`

  "/etc/awslogs/awslogs.conf" :
    mode: "000600"
    owner: root
    group: root
    content: |
      [general]
      state_file = /var/lib/awslogs/agent-state

  "/etc/awslogs/config/logs.conf" :
    mode: "000600"
    owner: root
    group: root
    content: |
      [/var/app/containerfiles/logs/appsignal]
      log_group_name = `{"Fn::Join":["/", ["/aws/elasticbeanstalk", { "Ref":"AWSEBEnvironmentName" }, "var/log/production/appsignal"]]}`
      log_stream_name = {instance_id}
      file = /var/app/containerfiles/logs/appsignal.log

      [/var/app/containerfiles/logs/mailer_worker]
      log_group_name = `{"Fn::Join":["/", ["/aws/elasticbeanstalk", { "Ref":"AWSEBEnvironmentName" }, "var/log/production/mailer_worker"]]}`
      log_stream_name = {instance_id}
      file = /var/app/containerfiles/logs/mailer_worker.log

      [/var/app/containerfiles/logs/production]
      log_group_name = `{"Fn::Join":["/", ["/aws/elasticbeanstalk", { "Ref":"AWSEBEnvironmentName" }, "var/log/production/productionlog"]]}`
      log_stream_name = {instance_id}
      file = /var/app/containerfiles/logs/production.log

      [/var/app/containerfiles/logs/report_worker]
      log_group_name = `{"Fn::Join":["/", ["/aws/elasticbeanstalk", { "Ref":"AWSEBEnvironmentName" }, "var/log/production/report_worker"]]}`
      log_stream_name = {instance_id}
      file = /var/app/containerfiles/logs/report_worker.log

      [/var/log/nginx/access]
      log_group_name = `{"Fn::Join":["/", ["/aws/elasticbeanstalk", { "Ref":"AWSEBEnvironmentName" }, "var/log/production/nginx/access"]]}`
      log_stream_name = {instance_id}
      file = /var/log/nginx/access.log

      [/var/log/nginx/error]
      log_group_name = `{"Fn::Join":["/", ["/aws/elasticbeanstalk", { "Ref":"AWSEBEnvironmentName" }, "var/log/production/nginx/error"]]}`
      log_stream_name = {instance_id}
      file = /var/log/nginx/error.log

commands:
  "01":
    command: systemctl start awslogsd && systemctl enable awslogsd.service
  "02":
    command: systemctl restart awslogsd