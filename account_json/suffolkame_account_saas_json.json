{"schema": {"pacfs": {"key_name": "PAF", "name": "Name", "description": "Description", "required_fields": ["name"]}, "benefits": {"key_name": "Benefits", "name": "Name", "description": "Description", "required_fields": ["name"]}, "payment_types": {"key_name": "Payment Types", "name": "Name", "description": "Description", "required_fields": ["name"]}, "awards": {"key_name": "Awards", "name": "Name", "awarded_on": "Date", "description": "Description", "required_fields": ["employee_id", "name"]}, "discipline_settings": {"key_name": "Disciplines", "name": "Name", "description": "Description", "required_fields": ["name"]}, "grievances": {"key_name": "Grievances", "name": "Name", "description": "Description", "required_fields": ["name"]}, "meeting_types": {"key_name": "Meeting Types", "name": "Name", "description": "Description", "required_fields": ["name"]}, "uploads": {"key_name": "Uploads", "file": "File", "notes": "Notes", "required_fields": ["file", "employee_id"]}, "users": {"key_name": "Users", "username": "Username", "email": "Email", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "password": "Password", "password_confirmation": "Password Confirmation", "user_audit_logging": "User <PERSON>t Logging", "required_fields": ["username", "email", "password", "password_confirmation", "role_id"]}, "roles": {"key_name": "Roles"}, "reports": {"single_employee": "Single Member", "sick_bank": "Sick Bank", "lodi": "<PERSON><PERSON>", "union_meetings": "Union Meeting", "employee_delegate_assignment": "Member Delegate Assignment", "janus": "<PERSON><PERSON>", "columns": "Report Columns", "started_at": "From Date", "ended_at": "To Date", "employee_summary": "Member Summary", "total_summary": "Total Summary", "active_date": "Active Date", "inactive_date": "Inactive Date", "disbursement_date": "Disbursement Date", "disbursement_year": "Disbursement Year", "disbursements_not_found": "Benefit disbursements not found.", "name": "Name", "date_in": "Date In", "date_out": "Date Out", "used_hours": "Used Hours", "lodi_return_to_work_status": "Return to Work Status", "date_to": "Date To", "date_from": "Date From", "excel_report": "Generate Excel Report", "pdf_report": "Generate PDF Report", "single_mailing_label": "Generate Single Mailing Labels", "multiple_mailing_label": "Generate Multiple Mailing Labels", "payment_date_from": "Payment Date From", "payment_date_to": "Payment Date To", "show_disbursements": "Show Disbursements", "delegates": "Delegates", "meetings": "Meetings"}, "employees": {"key_name": "Members", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "do_not_mail": "Do Not Mail", "birthday": "DOB", "social_security_number": "SS #", "veteran_status": "Veteran Status", "start_date": "Start Date", "notes": "Notes", "janus_card": "<PERSON><PERSON>t Out", "janus_card_opt_out_date": "<PERSON><PERSON> Opt Out Date", "cellphone": "Cell Phone", "email": "Email", "social_security_number_format": "9", "allow_multiple_present_status": "true", "required_fields": [], "search_columns": {"same_model": ["first_name", "middle_name", "last_name"], "associated_model": {"employment_statuses": ["name"], "contacts": ["value"], "benefit_coverages": ["name"]}}}, "contacts": {"contact_number": {"key_name": "Contact Number", "work_phone": "Work", "personal_phone": "Personal", "home_phone": "Home", "required_fields": []}, "email_address": {"key_name": "Email address", "work_email": "Work", "personal_email": "Personal", "required_fields": []}}, "genders": {"key_name": "Gender", "name": "Name", "description": "Description", "required_fields": ["name"]}, "marital_statuses": {"key_name": "Marital Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employment_statuses": {"key_name": "Member Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "units": {"key_name": "Units", "name": "Name", "description": "Description", "required_fields": ["name"]}, "positions": {"key_name": "Positions", "name": "Name", "description": "Description", "required_fields": ["name"]}, "notifications": {"key_name": "Notifications", "subject": "Subject", "sms_message": "Sms Message", "email_message": "Email Message", "sms": "false", "email": "false", "filter": "Filter"}, "leaves": {"started_at": "From", "ended_at": "To", "hours_used": "No. of hours used", "notes": "Notes", "required_fields": ["started_at", "leave_type", "hours_used"]}, "lodis": {"key_name": "<PERSON><PERSON>", "incident_date": "Date of incident", "return_date": "Date of return", "office_name": "Location", "notes": "Notes", "required_fields": ["office_id", "employee_id", "incident_date"]}, "employee_pacfs": {"date": "Date", "amount": "Amount", "notes": "Notes", "required_fields": ["pacf_id", "employee_id"]}, "employee_benefits": {"start_date": "Start Date", "end_date": "End Date", "description": "Description", "required_fields": ["benefit_id", "employee_id"]}, "beneficiaries": {"key_name": "Beneficiaries", "name": "Name", "relationship": "Relationship", "address": "Address", "beneficiary_type": "Type", "percentage": "Percentage", "file": "Upload", "required_fields": ["name", "employee_id", "percentage"]}, "benefit_coverages": {"key_name": "Benefit Coverages", "name": "Name", "relationship": "Relationship", "social_security_number": "employees.social_security_number", "address": "Address", "birthday": "DOB", "expires_at": "Expiration", "required_fields": ["employee_id", "address", "employee_benefit_id", "birthday", "name", "social_security_number", "relationship"]}, "benefit_disbursements": {"key_name": "Benefit Disbursements", "year": "Year", "date": "Date", "reference_number": "Reference Number", "amount": "Amount", "notes": "Notes", "required_fields": ["employee_id", "amount", "employee_benefit_id", "payment_type_id", "date", "reference_number", "year"]}, "employee_discipline_settings": {"discipline_setting_id": "Type", "date": "Date", "description": "Description", "files": "Files", "required_fields": ["employee_id", "discipline_setting_id"]}, "employee_grievances": {"key_name": "Grievances", "date": "Date", "description": "Description", "files": "Files", "required_fields": ["grievance_id"]}, "employee_meeting_types": {"meeting_date": "Date", "attended": "Attended", "notes": "Notes", "required_fields": ["meeting_type_id", "employee_id"]}, "employee_employment_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "employment_status_id"]}, "employee_positions": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["position_id", "employee_id"]}, "common_terms": {"employee_analytics": "Analytics", "sick_bank": "Sick Banks", "personal": "Personal", "vacation": "Vacations", "over_time": "Overtime", "comp_time": "Comp Time"}, "user_audit": {"key_name": "User <PERSON>t", "user_name": "User Name", "title": "Title", "employee_name": "Employee Name", "created_at": "Created At"}}, "ui": {"employees": {"key_name": "Member List", "is_search": ["employees"], "table_headers": ["name", "employment_status_name", "personal_phone", "personal_email", "city", "state", "zipcode"], "tabs": ["profile", "pacfs", "employee_analytics", "benefits", "awards", "discipline_settings", "grievances", "meeting_types", "uploads"], "profile": {"key_name": "Profile", "additional_details": ["first_name", "middle_name", "last_name"], "employees": ["name", "address", "do_not_mail", "birthday", "social_security_number", "genders", "marital_statuses", "veteran_status", "start_date", "notes", "units"], "contacts": [{"contact_number": ["personal_phone"]}, {"email_address": ["personal_email"]}], "others": ["employee_employment_statuses", "employee_positions", "employees.janus_card", "employees.janus_card_opt_out_date"]}}, "employee_grievances": {"key_name": "Grievances", "table_headers": ["date", "description", "files"]}, "users": {"is_search": ["users"], "table_headers": ["username", "name", "email", "roles"]}, "reports": {"key_name": "Reports", "tabs": ["single_employee", "benefits", "sick_bank", "lodi", "disciplines", "grievances", "janus"], "single_employee": {"primary_filters": [["reports.columns"], ["employees"]], "secondary_filters": [["employment_statuses", "positions"], ["marital_statuses", "genders"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["units", "employees.email"], ["employees.cellphone", "employees.city"], ["employees.state", "employees.zipcode"]], "columns": ["employees", "employees.first_name", "employees.middle_name", "employees.last_name", "employment_statuses", "employees.email", "marital_statuses", "positions", "employees.social_security_number", "employees.birthday", "employees.cellphone", "employees.apartment", "employees.street", "employees.city", "employees.state", "employees.zipcode", "pacfs", "employees.start_date", "units", "genders"], "default_columns": ["employees"], "actions": ["mailing_label", "excel_report", "pdf_report"]}, "benefits": {"primary_filters": [["benefits"], ["reports.active_date", "reports.inactive_date"], ["payment_types", "reports.disbursement_year"], ["reports.payment_date_from", "reports.payment_date_to"], ["reports.show_disbursements", ""]], "secondary_filters": [["employees"], ["employment_statuses", "positions"], ["marital_statuses", "genders"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["units", "employees.email"], ["employees.cellphone", "employees.city"], ["employees.state", "employees.zipcode"]], "actions": ["pdf_report"]}, "sick_bank": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["employment_statuses", "positions"], ["marital_statuses", "genders"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["units", "employees.email"], ["employees.cellphone", "employees.city"], ["employees.state", "employees.zipcode"]], "actions": ["excel_report", "pdf_report"]}, "lodi": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["employment_statuses", "positions"], ["marital_statuses", "genders"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["units", "employees.email"], ["employees.cellphone", "employees.city"], ["employees.state", "employees.zipcode"]], "actions": ["excel_report", "pdf_report"]}, "disciplines": {"primary_filters": [["discipline_settings"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["genders", "employment_statuses"], ["marital_statuses", "employees.email"], ["positions", "units"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.cellphone", "employees.city"], ["employees.state", "employees.zipcode"]], "actions": ["excel_report", "pdf_report"]}, "grievances": {"primary_filters": [["grievances"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["genders", "employment_statuses"], ["marital_statuses", "employees.email"], ["positions", "units"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.cellphone", "employees.city"], ["employees.state", "employees.zipcode"]], "actions": ["excel_report", "pdf_report"]}, "janus": {"primary_filters": [["employees.janus_card"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["genders", "employment_statuses"], ["marital_statuses", "employees.email"], ["positions", "units"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.cellphone", "employees.city"], ["employees.state", "employees.zipcode"]], "actions": ["excel_report", "pdf_report"]}}, "settings": {"key_name": "Settings", "tabs": ["pacfs", "benefits", "employment_statuses", "discipline_settings", "grievances", "payment_types", "meeting_types", "marital_statuses", "genders", "units", "positions"]}, "notification": {"key_name": "Notifications", "analytics": "Analytics", "is_search": true, "allow_sms_attachments": true, "check_confirmation": true, "notification_tracker_count": true, "filters": [["employees"], ["employment_statuses", "positions"], ["marital_statuses", "genders"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["units", "employees.cellphone"], ["employees.email", "employees.city"], ["employees.state", "employees.zipcode"]]}, "user_audit": {"key_name": "User <PERSON>t", "table_headers": ["user_name", "title", "employee_name", "created_at"]}}}