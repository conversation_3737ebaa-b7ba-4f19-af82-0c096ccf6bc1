{"schema": {"contact_persons": {"key_name": "Contact List", "avatar": "Photo", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "address": "Home Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "placard_number": "Placard Number", "placard_multiple": true, "rdo": "Company", "notes": "Notes", "cellphone": "Cell phone", "home_phone": "Home phone", "work_phone": "Work phone", "email": "Email", "required_fields": ["name", "first_name", "last_name", "address", "city", "state", "street", "zipcode"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "street", "city", "placard_number"]}}, "employees": {"key_name": "Members", "avatar": "Member Photo", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "social_security_number": "SS #", "social_security_number_format": "9", "visible_social_security_number": true, "birthday": "DOB", "age": "Age", "shield_number": "Shield #", "placard_number": "Placard #", "start_date": "UCS Start Date", "member_start_date": "NYSCCA Start Date", "do_not_mail": "Do Not Mail", "payroll_id": "Member ID #", "primary_work_location": "Benefit ID #", "placard_customize": "Placard", "prescription": "Firearm Serial #", "maiden_name": "Firearm Make", "previous_shield_number": "Firearm Model", "personal_emergency": "Personal Emergency", "colleague_emergency": "Colleague Emergency", "notes": "Notes", "cellphone": "Cell phone", "home_phone": "Home phone", "work_phone": "Work phone", "email": "Email", "allow_multiple_present_status": "false", "allow_multiple_delegate_assignments": "true", "placard_multiple": true, "username": "Username", "close_active_status": true, "ssn_unique_search": true, "placard_label": ["2025", "2024", "2023"], "active_status_to_be_close": ["employee_positions", "employee_sections", "employee_ranks"], "required_fields": ["name", "first_name", "last_name"], "unique_fields": ["username"], "exact_search_fields": ["shield_number"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "shield_number", "placard_number", "street", "start_date", "birthday", "social_security_number"], "associated_model": {"ranks": ["name"], "benefit_coverages": ["name"], "beneficiaries": ["name"]}}}, "genders": {"key_name": "Genders", "name": "Name", "description": "Description", "required_fields": ["name"]}, "contacts": {"contact_number": {"key_name": "Contact Number", "work_phone": "Work", "personal_phone": "Mobile", "home_phone": "Home", "required_fields": []}, "email_address": {"key_name": "Email address", "work_email": "Work", "personal_email": "Personal", "required_fields": []}, "emergency_contacts": {"key_name": "Emergency Contact", "personal_emergency": "Personal", "colleague_emergency": "Colleague", "required_fields": []}}, "employment_statuses": {"key_name": "Member Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_employment_statuses": {"start_date": "Start Date", "end_date": "End Date", "close_active_status_popup": "Do you want to also end the active Title, Union Position and Court?", "required_fields": ["employee_id", "employment_status_id", "start_date"]}, "employee_departments": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["start_date", "section_id", "department_id", "employee_id"]}, "employee_sections": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["start_date", "section_id", "department_id", "employee_id"]}, "employee_titles": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["start_date", "department_id", "section_id", "title_id", "employee_id"]}, "departments": {"key_name": "County", "name": "Name", "notes": "Notes", "required_fields": ["name"]}, "units": {"key_name": "Contact Title", "name": "Name", "description": "Description", "required_fields": ["name"]}, "sections": {"key_name": "Court", "name": "Name", "notes": "Notes", "required_fields": ["name", "department_id"]}, "titles": {"key_name": "Delegate", "name": "Name", "description": "Description", "title_code": "Title Code", "required_fields": ["department_id", "section_id", "name"]}, "affiliations": {"key_name": "Tier", "name": "Name", "description": "Description", "required_fields": ["name"]}, "offices": {"key_name": "Location", "name": "Name", "address": "Address", "phone": "Phone Number", "fax": "Description", "required_fields": ["name", "address"]}, "employee_offices": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["office_id", "employee_id"]}, "delegate_assignments": {"key_name": "Delegate Assignments", "delegate_employee_id": "Delegate Name", "start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["start_date", "delegate_employee_id", "employee_id"]}, "delegate_employees": {"key_name": "Delegate Name"}, "legislative_addresses": {"key_name": "Legislation", "legislation_details": "Legislation"}, "positions": {"key_name": "Union Position", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_positions": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["start_date", "position_id", "employee_id"]}, "ranks": {"key_name": "Title", "name": "Name", "description": "Description", "ignore_rank": true, "required_fields": ["name"]}, "employee_ranks": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["start_date", "rank_id", "employee_id"]}, "firearm_statuses": {"key_name": "Firearm Status", "name": "Name", "firearm_type": "Type", "firearm_test_type": "Test Type", "required_fields": ["name"]}, "employee_firearm_statuses": {"status_date": "Date", "firearm_type": "Type", "notes": "Notes", "files": "Uploads", "additional_field": ["prescription", "maiden_name", "previous_shield_number"], "customization": true, "required_fields": ["firearm_status_id", "employee_id"], "required_tables": ["firearm_statuses"]}, "leaves": {"started_at": "From", "ended_at": "To", "hours_used": "No. of hours used", "notes": "Notes", "files": "Files", "required_fields": ["started_at", "leave_type", "hours_used"]}, "lodis": {"key_name": "LODI", "incident_date": "Date of incident", "return_date": "Date of return", "office_name": "Location", "notes": "Notes", "files": "Files", "required_fields": ["office_id", "employee_id", "incident_date"]}, "workers_comp": {"key_name": "Workers Comp", "incident_date": "Date of incident", "return_date": "Date of return", "office_name": "Location", "notes": "Notes", "files": "Files", "required_fields": ["office_id", "employee_id", "incident_date"]}, "benefits": {"key_name": "Benefits", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_benefits": {"start_date": "Start Date", "end_date": "End Date", "description": "Description", "required_fields": ["benefit_id", "employee_id"]}, "beneficiaries": {"key_name": "Beneficiaries", "name": "Name", "relationship": "Relationship", "address": "Address", "birthday": "DOB", "phone": "Phone #", "beneficiary_type": "Type", "percentage": "Percentage", "file": "Upload", "required_fields": ["name", "employee_id"]}, "benefit_coverages": {"key_name": "Dependents", "name": "Name", "relationship": "Relationship", "birthday": "DOB", "age": "Age", "expires_at": "Expiration", "student": "Student Status", "school_status": "<PERSON><PERSON><PERSON>", "semester": "Year", "order_by_relationship": true, "required_fields": ["employee_id", "birthday", "name", "relationship"], "relationship_value": [{"value": "Member", "key": "member"}, {"value": "Spouse", "key": "spouse"}, {"value": "Domestic Partner", "key": "domestic_partner"}, {"value": "Child", "key": "child"}, {"value": "Disabled Child", "key": "disabled_child"}, {"value": "Step Child", "key": "step_child"}, {"value": "Disabled <PERSON> Child", "key": "disabled_step_child"}, {"value": "Adopted Child", "key": "adopted_child"}, {"value": "Other", "key": "other"}], "student_options": [{"value": "Yes", "key": true}, {"value": "No", "key": false}], "school_status_options": [{"value": "Spring", "key": "spring"}, {"value": "Fall", "key": "fall"}]}, "dependents": {"key_name": "Dependent", "name": "Name", "relationship": "Relationship", "amount": "Amount", "spouse_contribution": "Spouse Contribution", "relationship_value": [{"value": "Spouse", "key": "spouse"}, {"value": "Child", "key": "child"}], "address": "Address", "date": "DOB", "age": "Age", "required_fields": ["life_insurance_id", "employee_id", "name", "relationship", "amount"]}, "awards": {"key_name": "Awards", "name": "Type", "awarded_on": "Date", "description": "Description", "required_fields": ["employee_id", "name"]}, "facilities": {"key_name": "Mailing List Category", "name": "Name", "fax": "Description", "required_fields": ["name"]}, "employee_facilities": {"key_name": "Mailing List Category", "start_date": "Start Date", "end_date": "End Date", "required_fields": ["facility_id", "employee_id"]}, "discipline_charges": {"key_name": "Discipline Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "discipline_settings": {"key_name": "Disciplines", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_discipline_settings": {"discipline_setting_id": "Type", "charge": "Charge", "date": "Date", "filed_olr": "Filed OLR", "description": "Remarks", "discipline_charge_id": "Status", "files": "Files", "required_fields": ["employee_id", "discipline_setting_id"]}, "employee_discipline_steps": {"date": "Date", "recommended_notes": "Remarks", "hearing": "Hearing", "is_settled": "Settled", "is_pending": "Pending", "win": "Win", "loss": "Loss", "files": "Uploads", "required_fields": ["step", "employee_discipline_setting_id"]}, "grievances": {"key_name": "Grievances", "name": "Name", "description": "Description", "required_fields": ["name"]}, "grievance_statuses": {"key_name": "Grievance Statuses", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_grievances": {"key_name": "Class Action Grievances", "grievance_id": "Infraction", "number": "Case Number", "multiple_charges": "Article #", "date": "Date", "description": "Remarks", "files": "Uploads", "isMultiple_Charge": true, "required_fields": ["grievance_id"]}, "employee_grievance_steps": {"date": "Date", "recommended_notes": "Remarks", "is_settled": "Settled", "is_pending": "Pending", "win": "Win", "loss": "Loss", "files": "Uploads", "required_fields": ["step", "employee_grievance_id"]}, "meeting_types": {"key_name": "Union Meetings", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_meeting_types": {"meeting_date": "Date", "attended": "Attended", "notes": "Notes", "required_fields": ["meeting_date", "meeting_type_id", "employee_id"]}, "uploads": {"key_name": "Uploads", "file": "File", "notes": "Notes", "required_fields": ["file", "employee_id"]}, "users": {"key_name": "Users", "username": "Username", "email": "Email Address", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "password": "Password", "password_confirmation": "Password Confirmation", "user_audit_logging": "User <PERSON>t Logging", "required_fields": ["username", "email", "password", "password_confirmation", "role_id"]}, "roles": {"key_name": "Roles"}, "payment_types": {"key_name": "Document Type", "name": "Name", "description": "Description", "required_fields": ["name"]}, "reports": {"single_employee": "Single Member", "sick_bank": "Sick Bank", "lodi": "<PERSON><PERSON>", "workers_comp": "Workers Comp", "benefit_coverages": "Benefit Dependents", "benefit_coverage_name": "Dependent Name", "relationship": "Relationship", "benefit_coverage_ssn": "Dependent SSN#", "benefit_coverage_birthday": "Birthday", "union_meetings": "Union Meetings", "employee_delegate_assignment": "Member Delegate Assignment", "columns": "Report Columns", "birthday_from_date": "Birthday From Date", "birthday_to_date": "Birthday To Date", "expiration_from_date": "Expiration From Date", "expiration_to_date": "Expiration To Date", "started_at": "From Date", "ended_at": "To Date", "employee_summary": "Member Summary", "total_summary": "Total Summary", "active_date": "Active Date", "inactive_date": "Inactive Date", "disbursement_date": "Disbursement Date", "disbursement_year": "Disbursement Year", "disbursements_not_found": "Benefit disbursements not found.", "name": "Name", "date_in": "Date In", "date_out": "Date Out", "used_hours": "Used Hours", "date_to": "Date To", "date_from": "Date From", "excel_report": "Generate Excel Report", "pdf_report": "Generate PDF Report", "single_mailing_label": "Generate Single Mailing Labels", "multiple_mailing_label": "Generate Multiple Mailing Labels", "payment_date_from": "Payment Date From", "payment_date_to": "Payment Date To", "show_disbursements": "Show Disbursements", "show_coverages": "Show Dependents", "delegates": "Delegates", "meetings": "Meetings", "settled_steps": "Settled Steps", "pending_steps": "Pending Steps", "filed_olr_from_date": "OLR From Date", "filed_olr_to_date": "OLR To Date", "step_1_from_date": "Step I From Date", "step_1_to_date": "Step I To Date", "step_2_from_date": "Step II From Date", "step_2_to_date": "Step II To Date", "step_3_from_date": "Step III From Date", "step_3_to_date": "Step III To Date", "arbritration_from_date": "Arbritration From Date", "arbritration_to_date": "Arbritration To Date", "win": "Win", "loss": "Loss", "congress_district_id": "Congress District Name", "assembly_district_id": "Assembly District Name", "senate_district_id": "Senate District Name", "council_district_id": "Council District Name", "department_with_section": true, "step_values": [{"value": "Step I", "key": "step_1"}, {"value": "Step II", "key": "step_2"}, {"value": "Step III", "key": "step_3"}, {"value": "Arbritration", "key": "arbritration"}], "benefit_coverages_options": [{"value": "All members with/without dependents", "key": "all"}, {"value": "Members with dependents", "key": "true"}, {"value": "Members without dependents", "key": "false"}]}, "common_terms": {"step_1": "STEP I", "step_2": "STEP II", "step_3": "STEP III", "arbritration": "ARBRITRATION", "none": "None", "basic": "Basic", "premium": "Premium", "supplemental": "Supplemental", "life_insurances": "Life Insurances", "dependents": "Dependents", "lodis": "<PERSON><PERSON>", "workers_comp": "Workers Comp", "medscope": "Medscope", "hearing": "Hearing 207c", "arbitration": "Arbitration", "employee_analytics": "Analytics", "sick_bank": "Sick Bank"}, "employee_analytics": {"key_name": "Analytics", "customization": true, "dashboard_stats": true, "required_tables": ["sick_bank", "lodis", "workers_comp"]}, "notifications": {"key_name": "Notifications", "subject": "Subject", "sms_message": "Sms Message", "email_message": "Email Message", "sms": "false", "email": "false", "filter": "Filter", "congress_district_id": "Congress District Name", "assembly_district_id": "Assembly District Name", "senate_district_id": "Senate District Name", "council_district_id": "Council District Name"}, "user_audit": {"key_name": "User <PERSON>t", "user_name": "User Name", "title": "Title", "employee_name": "Employee Name", "created_at": "Created At"}}, "ui": {"notes_timestamps": true, "notes_disabled_fields": ["notes", "description", "recommended_notes"], "employees": {"key_name": "Member List", "has_exact_search": true, "is_search": ["employees"], "is_filter": ["employees", "ssn", "placard_number"], "show_union_status_top": true, "table_headers": ["name", "social_security_number", "employment_status_name", "rank_name", "section_name", "address"], "tabs": ["profile", "firearm_statuses", "employee_analytics", "benefits", "grievances", "discipline_settings", "meeting_types", "legislative_addresses", "uploads"], "profile": {"key_name": "Profile", "avatar_expand_view": true, "multiple_notes": true, "additional_details": ["first_name", "middle_name", "last_name"], "employees": ["avatar", "name", "address", "do_not_mail", "social_security_number", "birthday", "age", "genders", "shield_number", "placard_number", "start_date", "member_start_date", "affiliations", "primary_work_location", "payroll_id", "notes"], "contacts": [{"contact_number": ["personal_phone", "home_phone", "work_phone"], "emergency_contacts": ["personal_emergency", "colleague_emergency"], "emergency_contact_details": ["name", "contact", "relationship"]}, {"email_address": ["personal_email", "work_email"]}], "others": ["employee_employment_statuses", "employee_sections", "employee_ranks", "employee_positions", "delegate_assignments"]}, "discipline_settings": {"tabs": ["discipline_settings", "step_1", "step_2", "step_3", "arbritration"], "table_headers": ["discipline_setting", "charge", "date"]}, "benefits": {"isDetailsView": true}, "grievances": {"tabs": ["grievances", "step_1", "step_2", "arbritration"], "table_headers": ["grievance", "charge", "number", "date"]}}, "contact_persons": {"key_name": "Contact List", "remove_home_contact_list": true, "avatar_expand_view": true, "is_search": true, "additional_details": ["first_name", "middle_name", "last_name", "employee_facilities"], "table_headers": ["name", "address", "rdo", "units"], "employees": ["avatar", "name", "address", "employee_facilities", "notes", "rdo", "units"], "contacts": [{"is_restrict_emergency_contact_details": true, "contact_number": ["work_phone", "personal_phone"]}, {"email_address": ["work_email", "personal_email"]}]}, "employee_grievances": {"key_name": "Class Action Grievances", "table_headers": ["charge", "number", "date", "filed_olr", "description", "files"]}, "settings": {"key_name": "Settings", "tabs": ["genders", "affiliations", "employment_statuses", "departments", "sections", "titles", "ranks", "units", "positions", "offices", "firearm_statuses", "benefits", "grievances", "grievance_statuses", "discipline_settings", "discipline_charges", "meeting_types", "facilities"]}, "users": {"show_password_icon": false, "is_search": ["users"], "table_headers": ["username", "name", "email", "roles"]}, "reports": {"key_name": "Reports", "tabs": ["single_employee", "disciplines", "grievances", "benefit_coverages", "union_meetings"], "single_employee": {"primary_filters": [["reports.columns"], ["employees"]], "secondary_filters": [["employees.placard_number"], ["employment_statuses", "sections"], ["employees.email", "employees.cellphone"], ["employees.home_phone", "employees.work_phone"], ["employees.social_security_number", "genders"], ["positions", "affiliations"], ["employees.city", "employees.state"], ["employees.zipcode", "departments"], ["employees.birthday_from", "employees.birthday_to"], ["employees.age_from", "employees.age_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.member_start_date", "employees.primary_work_location"], ["employees.shield_number", "employees.payroll_id"], ["ranks", "firearm_statuses"], ["facilities", "reports.congress_district_id"], ["reports.assembly_district_id", "reports.senate_district_id"], ["reports.council_district_id", ""]], "columns": ["employees", "employees.first_name", "employees.middle_name", "employees.last_name", "employees.street", "employees.apartment", "employees.city", "employees.state", "employees.zipcode", "employees.social_security_number", "employees.birthday", "genders", "employees.shield_number", "employees.placard_customize", "employees.start_date", "employees.member_start_date", "affiliations", "employees.primary_work_location", "employees.payroll_id", "employees.cellphone", "employees.home_phone", "employees.work_phone", "employees.notes", "employees.email", "employment_statuses", "sections", "ranks", "positions", "firearm_statuses", "departments", "reports.congress_district_id", "reports.assembly_district_id", "reports.senate_district_id", "reports.council_district_id"], "default_columns": ["employees", "employment_statuses", "sections", "ranks"], "actions": ["mailing_label", "multiple_mailing_label", "excel_report", "pdf_report"]}, "disciplines": {"primary_filters": [["discipline_settings"], ["reports.started_at", "reports.ended_at"], ["reports.filed_olr_from_date", "reports.filed_olr_to_date"], ["reports.step_1_from_date", "reports.step_1_to_date"], ["reports.step_2_from_date", "reports.step_2_to_date"], ["reports.step_3_from_date", "reports.step_3_to_date"], ["reports.arbritration_from_date", "reports.arbritration_to_date"], ["reports.settled_steps", "reports.pending_steps"], ["reports.win", "reports.loss"]], "secondary_filters": [["employees.placard_number"], ["employees", "employment_statuses"], ["employees.email", "employees.cellphone"], ["employees.home_phone", "employees.work_phone"], ["employees.social_security_number", "genders"], ["positions", "affiliations"], ["employees.city", "employees.state"], ["employees.zipcode", "departments"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.member_start_date", "employees.primary_work_location"], ["employees.shield_number", "employees.payroll_id"], ["ranks", "firearm_statuses"], ["sections", "facilities"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"]], "actions": ["excel_report", "pdf_report"]}, "grievances": {"primary_filters": [["grievances"], ["reports.started_at", "reports.ended_at"], ["reports.filed_olr_from_date", "reports.filed_olr_to_date"], ["reports.step_1_from_date", "reports.step_1_to_date"], ["reports.step_2_from_date", "reports.step_2_to_date"], ["reports.arbritration_from_date", "reports.arbritration_to_date"], ["reports.settled_steps", "reports.pending_steps"], ["reports.win", "reports.loss"]], "secondary_filters": [["employees.placard_number"], ["employees", "employment_statuses"], ["employees.email", "employees.cellphone"], ["employees.home_phone", "employees.work_phone"], ["employees.social_security_number", "genders"], ["positions", "affiliations"], ["employees.city", "employees.state"], ["employees.zipcode", "departments"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.member_start_date", "employees.primary_work_location"], ["employees.shield_number", "employees.payroll_id"], ["ranks", "firearm_statuses"], ["sections", "facilities"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"]], "actions": ["excel_report", "pdf_report"]}, "benefit_coverages": {"primary_filters": [["employees"], ["benefits"], ["reports.show_coverages"]], "secondary_filters": [["employees.placard_number"], ["employees", "employment_statuses"], ["employees.email", "employees.cellphone"], ["employees.home_phone", "employees.work_phone"], ["employees.social_security_number", "genders"], ["positions", "affiliations"], ["employees.city", "employees.state"], ["employees.zipcode", "departments"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.member_start_date", "employees.primary_work_location"], ["employees.shield_number", "employees.payroll_id"], ["ranks", "firearm_statuses"], ["sections", "facilities"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"]], "actions": ["excel_report", "pdf_report"]}, "union_meetings": {"primary_filters": [["meeting_types"], ["reports.started_at", "reports.ended_at"], ["reports.meetings"]], "secondary_filters": [["employees.placard_number"], ["employees", "employment_statuses"], ["employees.email", "employees.cellphone"], ["employees.home_phone", "employees.work_phone"], ["employees.social_security_number", "genders"], ["positions", "affiliations"], ["employees.city", "employees.state"], ["employees.zipcode", "departments"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.member_start_date", "employees.primary_work_location"], ["employees.shield_number", "employees.payroll_id"], ["ranks", "firearm_statuses"], ["sections", "facilities"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"]], "actions": ["excel_report", "pdf_report"]}}, "notification": {"key_name": "Notifications", "analytics": "Analytics", "is_search": true, "allow_sms_attachments": true, "check_confirmation": true, "notification_tracker_count": true, "filters": [["employees"], ["employment_statuses", "sections"], ["employees.email", "employees.cellphone"], ["employees.home_phone", "employees.work_phone"], ["employees.social_security_number", "genders"], ["positions", "affiliations"], ["employees.city", "employees.state"], ["employees.zipcode", "departments"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.member_start_date", "employees.primary_work_location"], ["employees.shield_number", "employees.payroll_id"], ["ranks", "firearm_statuses"], ["facilities", "notifications.congress_district_id"], ["notifications.assembly_district_id", "notifications.senate_district_id"], ["notifications.council_district_id", ""]]}, "user_audit": {"key_name": "User <PERSON>t", "table_headers": ["user_name", "title", "employee_name", "created_at"]}}}