{"schema": {"employees": {"key_name": "Members", "avatar": "Member Photo", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "suffix": "Suffix", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "do_not_mail": "Do Not Mail", "birthday": "DOB", "age": "Age", "social_security_number": "SSN", "social_security_number_format": "4", "veteran_status": "Veteran Status", "responder_911": "9/11 Re<PERSON>onder", "placard_number": "Placard Number", "a_number": "Reference #", "shield_number": "NYCERS #", "title_code": "Badge #", "start_date": "Dispatcher Start Date", "start_date_duration": "Dispatcher Years in Service", "member_start_date": "UFADBA Start Date", "member_start_date_duration": "UFADBA Years in Service", "notes": "Notes", "cellphone": "Cell phone", "email": "Email", "allow_multiple_present_status": "false", "required_fields": ["name", "first_name", "last_name", "address", "city", "state", "street", "zipcode"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "suffix", "shield_number", "placard_number", "street", "start_date", "birthday"], "associated_model": {"benefit_coverages": ["name"]}}}, "marital_statuses": {"key_name": "Marital Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "genders": {"key_name": "Genders", "name": "Name", "description": "Description", "required_fields": ["name"]}, "units": {"key_name": "Nationality", "name": "Name", "description": "Description", "required_fields": ["name"]}, "contacts": {"contact_number": {"key_name": "Contact Number", "personal_phone": "Cell", "required_fields": []}, "email_address": {"key_name": "Email Address", "personal_email": "Personal", "required_fields": []}}, "employment_statuses": {"key_name": "Union Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_employment_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "employment_status_id"]}, "ranks": {"key_name": "Titles", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_ranks": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["rank_id", "employee_id"]}, "offices": {"key_name": "Work Locations", "name": "Name", "address": "Address", "phone": "Phone Number", "fax": "Description", "required_fields": ["name", "address"]}, "employee_offices": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["office_id", "employee_id"]}, "positions": {"key_name": "Union Positions", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_positions": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["position_id", "employee_id"]}, "lodis": {"key_name": "LODI", "incident_date": "Date of incident", "return_date": "Date of return", "office_name": "Location", "notes": "Notes", "files": "Files", "required_fields": ["office_id", "employee_id", "incident_date"]}, "workers_comp": {"key_name": "Workers Comp", "incident_date": "Date of incident", "return_date": "Date of return", "office_name": "Location", "notes": "Notes", "files": "Files", "required_fields": ["office_id", "employee_id", "incident_date"]}, "benefits": {"key_name": "Benefits", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_benefits": {"start_date": "Start Date", "end_date": "End Date", "description": "Description", "required_fields": ["benefit_id", "employee_id", "start_date"]}, "beneficiaries": {"key_name": "Beneficiaries", "name": "Name", "relationship": "Relationship", "address": "Address", "beneficiary_type": "Type", "percentage": "Percentage", "file": "Upload", "required_fields": ["name", "employee_id", "percentage"]}, "benefit_coverages": {"key_name": "Benefit Coverages", "name": "Name", "relationship": "Relationship", "social_security_number": "employees.social_security_number", "address": "Address", "birthday": "DOB", "expires_at": "Expiration", "required_fields": ["employee_id", "address", "employee_benefit_id", "birthday", "name", "social_security_number", "relationship"]}, "payment_types": {"key_name": "Payment Types", "name": "Name", "description": "Description", "required_fields": ["name"]}, "benefit_disbursements": {"key_name": "Benefit Disbursements", "year": "Year", "date": "Date", "reference_number": "Reference Number", "amount": "Amount", "notes": "Notes", "required_fields": ["employee_id", "amount", "employee_benefit_id", "payment_type_id", "date", "reference_number", "year"]}, "life_insurances": {"key_name": "Life Insurance", "insurance_type": "Type", "amount": "Amount", "start_date": "As of Date", "age": "Age", "notes": "Notes", "files": "Uploads", "member_contributions": "Member Contribution", "age_group_type": "Age Group", "required_fields": ["insurance_type", "employee_id", "amount", "start_date"], "member_contribution": [{"low": 1, "high": 29, "amount": {"50000": 1.2, "100000": 2.4, "200000": 4.8}}, {"low": 30, "high": 34, "amount": {"50000": 1.37, "100000": 2.74, "200000": 5.47}}, {"low": 35, "high": 39, "amount": {"50000": 1.68, "100000": 3.36, "200000": 6.72}}, {"low": 40, "high": 44, "amount": {"50000": 2.4, "100000": 4.8, "200000": 9.6}}, {"low": 45, "high": 49, "amount": {"50000": 3.6, "100000": 7.2, "200000": 14.4}}, {"low": 50, "high": 54, "amount": {"50000": 5.52, "100000": 11.04, "200000": 22.08}}, {"low": 55, "high": 59, "amount": {"50000": 9.96, "100000": 19.92, "200000": 39.84}}, {"low": 60, "high": 64, "amount": {"50000": 15.05, "100000": 30.1, "200000": 60.2}}, {"low": 65, "high": 69, "amount": {"50000": 25.6, "100000": 51.22, "200000": 102.43}}, {"low": 70, "high": 99, "amount": {"50000": 48.38, "100000": 96.77, "200000": 193.53}}], "life_insurances_premium": {"key_name": "Premium", "member_contribution": "Member Contribution", "total_contribution": "Total Contribution"}, "insurance_amount": {"supplemental": [50000, 100000, 200000], "basic": [15000]}, "insurance_types": [{"value": "Basic", "key": "basic"}, {"value": "Supplemental", "key": "supplemental"}]}, "dependents": {"key_name": "Dependent", "name": "Name", "relationship": "Relationship", "amount": "Amount", "spouse_contribution": "Spouse Contribution", "relationship_amount_validation": {"basic": {"spouse": [15000], "child": [5000]}, "supplemental": {"spouse": [25000, 50000, 100000]}}, "relationship_value": [{"value": "Spouse", "key": "spouse"}, {"value": "Child", "key": "child"}], "address": "Address", "date": "DOB", "age": "Age", "required_fields": ["life_insurance_id", "employee_id", "name", "relationship", "amount"], "spouse_contribution_value": {"amount": {"25000": 1.82, "50000": 3.63, "100000": 7.26, "1000000": 999.99}}, "dependents_premium": {"key_name": "Premium", "spouse_contribution": "Spouse Contribution", "total_contribution": "Total Contribution"}}, "discipline_settings": {"key_name": "Disciplines", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_discipline_settings": {"discipline_setting_id": "Infraction", "charge": "Charge", "dan_number": "Case Number", "date": "Date", "filed_olr": "Filed OLR", "description": "Remarks", "files": "Files", "required_fields": ["employee_id", "discipline_setting_id"]}, "employee_discipline_steps": {"date": "Date", "recommended_notes": "Remarks", "is_settled": "Settled", "is_pending": "Pending", "win": "Win", "loss": "Loss", "files": "Uploads", "required_fields": ["step", "employee_discipline_setting_id"]}, "grievances": {"key_name": "Grievances", "name": "Name", "description": "Description", "required_fields": ["name"]}, "grievance_statuses": {"key_name": "Grievance Statuses", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_grievances": {"key_name": "Grievances", "grievance_id": "Infraction", "charge": "Charge", "number": "Case Number", "date": "Date", "filed_olr": "UOR/Aided", "description": "Remarks", "files": "Uploads", "required_fields": ["grievance_id"]}, "employee_grievance_steps": {"date": "Date", "grievance_status_id": "Status", "recommended_notes": "Remarks", "is_settled": "Settled", "is_pending": "Pending", "win": "Win", "loss": "Loss", "files": "Uploads", "required_fields": ["step", "employee_grievance_id"]}, "reports": {"single_employee": "Single Member", "workers_comp": "Workers Comp", "benefit_coverages": "Benefit Dependents", "benefit_coverage_name": "Dependent Name", "relationship": "Relationship", "benefit_coverage_ssn": "Dependent SSN#", "benefit_coverage_birthday": "Birthday", "columns": "Report Columns", "birthday_from_date": "Birthday From Date", "birthday_to_date": "Birthday To Date", "expiration_from_date": "Expiration From Date", "expiration_to_date": "Expiration To Date", "started_at": "From Date", "ended_at": "To Date", "employee_summary": "Member Summary", "total_summary": "Total Summary", "active_date": "Active Date", "inactive_date": "Inactive Date", "disbursement_date": "Disbursement Date", "disbursement_year": "Disbursement Year", "disbursements_not_found": "Benefit disbursements not found.", "name": "Name", "date_in": "Date In", "date_out": "Date Out", "used_hours": "Used Hours", "lodi_return_to_work_status": "Return to Work Status", "date_to": "Date To", "date_from": "Date From", "excel_report": "Generate Excel Report", "pdf_report": "Generate PDF Report", "single_mailing_label": "Generate Single Mailing Labels", "multiple_mailing_label": "Generate Multiple Mailing Labels", "payment_date_from": "Payment Date From", "payment_date_to": "Payment Date To", "show_disbursements": "Show Disbursements", "show_coverages": "Show Dependents", "delegates": "Delegates", "meetings": "Meetings", "settled_steps": "Settled Steps", "pending_steps": "Pending Steps", "filed_olr_from_date": "OLR From Date", "filed_olr_to_date": "OLR To Date", "step_1_from_date": "Step I From Date", "step_1_to_date": "Step I To Date", "step_2_from_date": "Step II From Date", "step_2_to_date": "Step II To Date", "step_3_from_date": "Step III From Date", "step_3_to_date": "Step III To Date", "arbritration_from_date": "Arbritration From Date", "arbritration_to_date": "Arbritration To Date", "win": "Win", "loss": "Loss", "dob_ranges": "DOB Ranges", "life_insurances": "Life Insurances", "age_group_type": "Type", "dob_ranges_options": [{"value": "Less than 30", "key": "30"}, {"value": "30 to 34", "key": "30-34"}, {"value": "35 to 39", "key": "35-39"}, {"value": "40 to 44", "key": "40-44"}, {"value": "45 to 49", "key": "45-49"}, {"value": "50 to 54", "key": "50-54"}, {"value": "55 to 59", "key": "55-59"}, {"value": "60 to 64", "key": "60-64"}, {"value": "65 to 69", "key": "65-69"}, {"value": "70 to 99", "key": "70-99"}], "step_values": [{"value": "Step I", "key": "step_1"}, {"value": "Step II", "key": "step_2"}, {"value": "Step III", "key": "step_3"}, {"value": "Arbritration", "key": "arbritration"}]}, "common_terms": {"step_1": "STEP I", "step_2": "STEP II", "step_3": "STEP III", "arbritration": "ARBRITRATION", "none": "None", "basic": "Basic", "premium": "Premium", "supplemental": "Supplemental", "life_insurances": "Life Insurances", "dependents": "Dependents", "workers_comp": "Workers Comp", "medscope": "Medscope", "hearing": "Hearing 207c", "arbitration": "Arbitration", "employee_analytics": "Analytics", "sick_bank": "Sick Bank"}, "employee_analytics": {"key_name": "Analytics", "customization": true, "dashboard_stats": true, "required_tables": ["workers_comp"]}, "uploads": {"key_name": "Uploads", "file": "File", "notes": "Notes", "required_fields": ["file", "employee_id"]}, "users": {"key_name": "Users", "username": "Username", "email": "Email Address", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "password": "Password", "password_confirmation": "Password Confirmation", "user_audit_logging": "User <PERSON>t Logging", "required_fields": ["username", "email", "password", "password_confirmation", "role_id"]}, "roles": {"key_name": "Roles"}, "notifications": {"key_name": "Notifications", "subject": "Subject", "sms_message": "Sms Message", "email_message": "Email Message", "sms": "false", "email": "false", "filter": "Filter"}, "user_audit": {"key_name": "User <PERSON>t", "user_name": "User Name", "title": "Title", "employee_name": "Employee Name", "created_at": "Created At"}}, "ui": {"employees": {"key_name": "Member List", "is_search": ["employees"], "table_headers": ["name", "shield_number", "placard_number", "a_number", "address"], "tabs": ["profile", "employee_analytics", "benefits", "discipline_settings", "grievances", "uploads"], "profile": {"key_name": "Profile", "avatar_expand_view": true, "additional_details": ["first_name", "middle_name", "last_name", "suffix"], "employees": ["avatar", "name", "address", "do_not_mail", "birthday", "age", "social_security_number", "marital_statuses", "units", "genders", "veteran_status", "responder_911", "placard_number", "a_number", "shield_number", "title_code", "start_date", "start_date_duration", "member_start_date", "member_start_date_duration", "notes"], "contacts": [{"contact_number": ["personal_phone"]}, {"email_address": ["personal_email"]}], "others": ["employee_employment_statuses", "employee_ranks", "employee_offices", "employee_positions"]}, "discipline_settings": {"tabs": ["discipline_settings", "step_1", "step_2", "step_3", "arbritration"], "table_headers": ["dan_number", "discipline_setting", "charge", "date"]}, "grievances": {"tabs": ["grievances", "step_1", "step_2", "step_3", "arbritration"], "table_headers": ["grievance", "charge", "number", "date"]}, "life_insurances": {"table_headers": ["insurance_type", "amount", "start_date", "notes"], "insurance_type": ["basic", "premium", "supplemental", "none"], "insurance_amount": [50000, 100000, 200000], "tabs": ["life_insurances", "premium"]}, "dependents": {"tabs": ["dependents", "premium"]}}, "users": {"is_search": ["users"], "table_headers": ["username", "name", "email", "roles"]}, "employee_grievances": {"key_name": "Grievances", "table_headers": ["charge", "number", "date", "filed_olr", "description", "files"]}, "settings": {"key_name": "Settings", "tabs": ["benefits", "employment_statuses", "offices", "ranks", "positions", "marital_statuses", "genders", "units", "payment_types", "discipline_settings", "grievances"]}, "reports": {"key_name": "Reports", "tabs": ["single_employee", "disciplines", "grievances", "benefits", "life_insurances"], "single_employee": {"primary_filters": [["reports.columns"], ["employees"]], "secondary_filters": [["offices", "genders"], ["ranks", "employment_statuses"], ["positions", "units"], ["marital_statuses", "employees.responder_911"], ["employees.email", "employees.social_security_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.age_from", "employees.age_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.shield_number", "employees.a_number"], ["employees.title_code", "employees.cellphone"], ["employees.city", "employees.state"], ["employees.zipcode", "employees.placard_number"]], "columns": ["employees", "employees.first_name", "employees.middle_name", "employees.last_name", "employees.suffix", "offices", "ranks", "employment_statuses", "employees.email", "marital_statuses", "positions", "genders", "units", "employees.social_security_number", "employees.placard_number", "employees.shield_number", "employees.a_number", "employees.title_code", "employees.birthday", "employees.cellphone", "employees.apartment", "employees.street", "employees.city", "employees.state", "employees.zipcode", "employees.start_date", "employees.member_start_date"], "default_columns": ["employees", "employees.shield_number", "employees.a_number", "ranks", "offices"], "actions": ["mailing_label", "multiple_mailing_label", "excel_report", "pdf_report"]}, "disciplines": {"primary_filters": [["discipline_settings"], ["reports.started_at", "reports.ended_at"], ["reports.filed_olr_from_date", "reports.filed_olr_to_date"], ["reports.step_1_from_date", "reports.step_1_to_date"], ["reports.step_2_from_date", "reports.step_2_to_date"], ["reports.step_3_from_date", "reports.step_3_to_date"], ["reports.arbritration_from_date", "reports.arbritration_to_date"], ["reports.settled_steps", "reports.pending_steps"], ["reports.win", "reports.loss"]], "secondary_filters": [["employees"], ["offices", "genders"], ["ranks", "employment_statuses"], ["positions", "units"], ["marital_statuses", "employees.responder_911"], ["employees.email", "employees.social_security_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.shield_number", "employees.a_number"], ["employees.title_code", "employees.cellphone"], ["employees.city", "employees.state"], ["employees.zipcode", "employees.placard_number"]], "actions": ["excel_report", "pdf_report"]}, "grievances": {"primary_filters": [["grievances"], ["reports.started_at", "reports.ended_at"], ["reports.filed_olr_from_date", "reports.filed_olr_to_date"], ["reports.step_1_from_date", "reports.step_1_to_date"], ["reports.step_2_from_date", "reports.step_2_to_date"], ["reports.step_3_from_date", "reports.step_3_to_date"], ["reports.arbritration_from_date", "reports.arbritration_to_date"], ["reports.settled_steps", "reports.pending_steps"], ["reports.win", "reports.loss"]], "secondary_filters": [["employees"], ["offices", "genders"], ["ranks", "employment_statuses"], ["positions", "units"], ["marital_statuses", "employees.responder_911"], ["employees.email", "employees.social_security_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.shield_number", "employees.a_number"], ["employees.title_code", "employees.cellphone"], ["employees.city", "employees.state"], ["employees.zipcode", "employees.placard_number"]], "actions": ["excel_report", "pdf_report"]}, "benefits": {"primary_filters": [["benefits"], ["reports.active_date", "reports.inactive_date"], ["payment_types", "reports.disbursement_year"], ["reports.payment_date_from", "reports.payment_date_to"], ["reports.show_disbursements", ""]], "secondary_filters": [["employees"], ["offices", "genders"], ["ranks", "employment_statuses"], ["positions", "units"], ["marital_statuses", "employees.responder_911"], ["employees.email", "employees.social_security_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.shield_number", "employees.a_number"], ["employees.title_code", "employees.cellphone"], ["employees.city", "employees.state"], ["employees.zipcode", "employees.placard_number"]], "actions": ["pdf_report"]}, "life_insurances": {"primary_filters": [["reports.age_group_type", ""]], "secondary_filters": [["employees"], ["offices", "genders"], ["ranks", "employment_statuses"], ["positions", "units"], ["marital_statuses", "employees.responder_911"], ["employees.email", "employees.social_security_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.shield_number", "employees.a_number"], ["employees.title_code", "employees.cellphone"], ["employees.city", "employees.state"], ["employees.zipcode", "employees.placard_number"]], "actions": ["excel_report"]}}, "notification": {"key_name": "Notifications", "analytics": "Analytics", "is_search": true, "allow_sms_attachments": true, "check_confirmation": true, "notification_tracker_count": true, "filters": [["employees"], ["offices", "genders"], ["ranks", "employment_statuses"], ["positions", "units"], ["marital_statuses", "employees.responder_911"], ["employees.email", "employees.social_security_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.shield_number", "employees.a_number"], ["employees.title_code", "employees.cellphone"], ["employees.city", "employees.state"], ["employees.zipcode", "employees.placard_number"]]}, "user_audit": {"key_name": "User <PERSON>t", "table_headers": ["user_name", "title", "employee_name", "created_at"]}}}