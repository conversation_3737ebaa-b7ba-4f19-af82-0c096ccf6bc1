{"schema": {"notifications": {"key_name": "Notifications", "subject": "Subject", "sms_message": "Sms Message", "email_message": "Email Message", "sms": "false", "email": "false", "filter": "Filter", "sms_no_reply_text": "Please note, this is a no-reply text."}, "offices": {"key_name": "Court Location", "name": "Name", "address": "Address", "phone": "Phone Number", "fax": "Description", "required_fields": ["name"]}, "ranks": {"key_name": "Titles", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employment_statuses": {"key_name": "Employment Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "marital_statuses": {"key_name": "Marital Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "positions": {"key_name": "Positions", "name": "Name", "description": "Description", "required_fields": ["name"]}, "genders": {"key_name": "Sex", "name": "Name", "description": "Description", "required_fields": ["name"]}, "affiliations": {"key_name": "Tier", "name": "Name", "description": "Description", "required_fields": ["name"]}, "users": {"key_name": "Users", "username": "Username", "email": "Email Address", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "password": "Password", "password_confirmation": "Password Confirmation", "user_audit_logging": "User <PERSON>t Logging", "required_fields": ["username", "email", "password", "password_confirmation", "role_id"]}, "roles": {"key_name": "Roles"}, "firearm_statuses": {"key_name": "Firearm Status", "name": "Name", "firearm_type": "Type", "firearm_test_type": "Test Type", "required_fields": ["name"]}, "employee_firearm_statuses": {"status_date": "Date", "firearm_type": "Type", "notes": "Notes", "required_fields": ["firearm_status_id", "employee_id"]}, "firearm_range_scores": {"key_name": "Firearm Range Scores", "test_type": "Test type", "test_date": "Date", "score": "Score", "notes": "Notes", "required_fields": ["test_date", "test_type", "score", "employee_id"]}, "benefits": {"key_name": "Benefits", "name": "Name", "description": "Description", "required_fields": ["name"], "benefits_name": ["Active Death Benefit", "COBRA", "Dental", "Hearing Aide", "Heartscan", "Hospital Income", "Inner Imaging", "Laser Correction", "Maternity/Adoption", "Optical", "Orthodontic Expense", "Part-time Buy Up", "Prescription Medical Copay Reimbursement", "Supplemental Workers Compensation"]}, "employee_benefits": {"start_date": "Start Date", "end_date": "Expiration", "serviced_expiration": "Serviced Expiration", "description": "Description", "employee_status_start_date": true, "auto_expire_benefits": true, "expire_all_benefits": "Expire All Benefits", "employee_status": {"section_I": ["Active", "Active Part-Time"], "section_III": ["Deceased"], "section_IV": [["Retired", "Out of State Retiree"], ["Retiree Death Benefit"]]}, "required_fields": ["benefit_id", "employee_id"]}, "beneficiaries": {"key_name": "Beneficiaries", "name": "Name", "relationship": "Relationship", "address": "Address", "beneficiary_type": "Type", "percentage": "Percentage", "files": "Uploads", "required_fields": ["name", "employee_id", "percentage"]}, "benefit_coverages": {"key_name": "Benefit Coverages", "first_name": "First Name", "last_name": "Last Name", "relationship": "Relationship", "dependent": "Dependent", "address": "Address", "birthday": "DOB", "age": "Age", "expires_at": "Expiration", "serviced_expiration": "Serviced Expiration", "coverage_expire_age": 26, "update_unexpire": true, "add_dependent_to_all_benefits": "Add Dependent To All Benefits", "expire_relationship_types": ["child"], "relationship_value": [{"value": "Spouse", "key": "spouse"}, {"value": "Child", "key": "child"}, {"value": "Disabled Child", "key": "disabled_child"}, {"value": "Domestic Partner", "key": "domestic_partner"}], "required_fields": ["employee_id", "address", "employee_benefit_id", "birthday", "first_name", "last_name", "relationship"]}, "benefit_disbursements": {"key_name": "Benefit Disbursements", "year": "Year", "date": "Date", "relationship": "Relationship", "entry_date": "Date of Service", "benefit_coverage_id": "Person Serviced", "reference_number": "Reference Number", "optical_coverage_expiration": true, "calendar_year_expiration": true, "amount": "Amount", "notes": "Notes", "relationship_value": [{"value": "Spouse", "key": "spouse"}, {"value": "Child", "key": "child"}, {"value": "Disabled Child", "key": "disabled_child"}, {"value": "Domestic Partner", "key": "domestic_partner"}, {"value": "Self", "key": "self"}], "required_fields": ["employee_id", "employee_benefit_id", "date", "year"]}, "employees": {"key_name": "Members", "avatar": "Member Photo", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "birthday": "DOB", "veteran_status": "Veteran Status", "shield_number": "Shield Number", "social_security_number": "Social Security #", "previous_shield_number": "ASO ID #", "placard_number": "Grade Number", "start_date": "COBANC Start Date", "ncc_date": "UCS Start Date", "notes": "Notes", "cellphone": "Cell phone", "home_phone": "Home phone", "email": "Email", "allow_multiple_present_status": "false", "social_security_number_format": "9", "close_active_status": true, "active_status_to_be_close": ["employee_positions", "employee_offices", "employee_ranks"], "required_fields": ["name", "address", "first_name", "last_name"], "custom_validations": {"starts_with": {"placard_number": "JG"}}, "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "shield_number", "street", "start_date", "birthday", "apartment", "city", "state", "zipcode"], "associated_model": {"employment_statuses": ["name"], "contacts": ["value"], "benefit_coverages": ["first_name", "last_name", "suffix"]}}}, "contacts": {"contact_number": {"key_name": "Contact Number", "personal_phone": "Cell", "home_phone": "Home", "required_fields": []}, "email_address": {"key_name": "Email Address", "personal_email": "Personal", "required_fields": []}}, "employee_employment_statuses": {"start_date": "Start Date", "end_date": "End Date", "close_active_status_popup": "Do you want to also end the active Title, Position and Court Location?", "required_fields": ["employee_id", "employment_status_id", "start_date"]}, "employee_offices": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["office_id", "employee_id", "start_date"]}, "employee_positions": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["position_id", "employee_id", "start_date"]}, "employee_ranks": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["rank_id", "employee_id", "start_date"]}, "reports": {"single_employee": "Single Member", "sick_bank": "Sick Bank", "lodi": "<PERSON><PERSON>", "union_meetings": "Union Meetings", "benefit_coverages": "Benefit Dependents", "employee_delegate_assignment": "Member Delegate Assignment", "columns": "Report Columns", "started_at": "From Date", "ended_at": "To Date", "employee_summary": "Member Summary", "total_summary": "Total Summary", "active_date": "Active Date", "inactive_date": "Inactive Date", "disbursement_date": "Disbursement Date", "disbursement_year": "Disbursement Year", "disbursements_not_found": "Benefit disbursements not found.", "name": "Name", "date_in": "Date In", "date_out": "Date Out", "used_hours": "Used Hours", "lodi_return_to_work_status": "Return to Work Status", "date_to": "Date To", "date_from": "Date From", "excel_report": "Generate Excel Report", "pdf_report": "Generate PDF Report", "single_mailing_label": "Generate Single Mailing Labels", "multiple_mailing_label": "Generate Multiple Mailing Labels", "payment_date_from": "Payment Date From", "payment_date_to": "Payment Date To", "show_disbursements": "Show Disbursements", "delegates": "Delegates", "meetings": "Meetings", "employment_statuses_from": "Employment Status From", "employment_statuses_to": "Employment Status To", "show_dependents": "Show Dependents", "show_coverages": "Show Dependents", "benefit_coverages_options": [{"value": "All members with/without dependents", "key": "all"}, {"value": "Members with dependents", "key": "true"}, {"value": "Members without dependents", "key": "false"}]}, "common_terms": {"employee_analytics": "Analytics", "sick_bank": "Sick Banks", "personal": "Personal", "vacation": "Vacations", "over_time": "Overtime", "comp_time": "Comp Time"}, "user_audit": {"key_name": "User <PERSON>t", "user_name": "User Name", "title": "Title", "employee_name": "Employee Name", "created_at": "Created At"}}, "ui": {"employees": {"key_name": "Member List", "is_search": ["employees"], "is_filter": ["employees"], "table_headers": ["name", "employment_status_name", "previous_shield_number", "personal_phone", "personal_email", "address"], "tabs": ["profile", "benefits", "firearm_statuses"], "profile": {"key_name": "Profile", "avatar_expand_view": true, "additional_details": ["first_name", "middle_name", "last_name"], "employees": ["avatar", "name", "address", "birthday", "genders", "marital_statuses", "affiliations", "veteran_status", "shield_number", "social_security_number", "previous_shield_number", "placard_number", "start_date", "ncc_date", "notes"], "contacts": [{"contact_number": ["personal_phone", "home_phone"]}, {"email_address": ["personal_email"]}], "others": ["employee_employment_statuses", "employee_offices", "employee_positions", "employee_ranks"]}}, "settings": {"key_name": "Settings", "tabs": ["benefits", "offices", "ranks", "employment_statuses", "marital_statuses", "positions", "genders", "affiliations", "firearm_statuses"]}, "users": {"is_search": ["users"], "table_headers": ["username", "name", "email", "roles"]}, "reports": {"key_name": "Reports", "tabs": ["single_employee", "benefits", "benefit_coverages"], "single_employee": {"primary_filters": [["reports.columns"], ["employees"]], "secondary_filters": [["offices", "genders"], ["ranks", "employment_statuses"], ["reports.employment_statuses_from", "reports.employment_statuses_to"], ["positions", "marital_statuses"], ["affiliations", "employees.social_security_number"], ["employees.shield_number", "employees.previous_shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.ncc_date_from", "employees.ncc_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.city", "employees.state"], ["employees.zipcode", "firearm_statuses"]], "columns": ["employees", "employees.first_name", "employees.middle_name", "employees.last_name", "offices", "ranks", "employment_statuses", "employees.email", "marital_statuses", "positions", "employees.social_security_number", "employees.shield_number", "employees.previous_shield_number", "employees.placard_number", "employees.birthday", "employees.home_phone", "employees.cellphone", "employees.city", "employees.state", "employees.zipcode", "employees.start_date", "employees.ncc_date", "employees.apartment", "employees.street", "genders", "affiliations", "firearm_statuses"], "default_columns": ["employees"], "actions": ["mailing_label", "excel_report", "pdf_report"], "mailing_list_types": [{"key": "CustomAvery5160", "value": "Avery 5160 - 1\" x 2-5/8\""}, {"key": "CustomAvery5366", "value": "Avery 5366 - 2/3\" x 3-7/16\""}, {"key": "CustomAvery5163", "value": "Avery 5163 - 2\" x 4\""}, {"key": "CustomAvery5161", "value": "Avery 5161 - 1\" x 4\""}, {"key": "CustomAvery5735", "value": "Avery 5735 - 1\" x 3\""}, {"key": "CustomPostCard", "value": "Postcard"}, {"key": "CustomEnvelope10", "value": "Envelope 10"}], "avery_columns": ["ranks", "employees.shield_number", "employees.placard_number"]}, "benefits": {"primary_filters": [["benefits"], ["reports.active_date", "reports.inactive_date"], ["reports.payment_date_from", "reports.payment_date_to"], ["reports.disbursement_year", "reports.show_disbursements"], ["reports.show_dependents"]], "secondary_filters": [["offices", "genders"], ["ranks", "employment_statuses"], ["employees.social_security_number", "firearm_statuses"], ["reports.employment_statuses_from", "reports.employment_statuses_to"], ["positions", "marital_statuses"], ["employees.shield_number", "employees.previous_shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.ncc_date_from", "employees.ncc_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.city", "employees.state"], ["employees.zipcode", "affiliations"]], "actions": ["pdf_report"]}, "benefit_coverages": {"primary_filters": [["employees"], ["benefits"], ["reports.show_coverages"]], "secondary_filters": [["offices", "genders"], ["ranks", "employment_statuses"], ["employees.social_security_number", "firearm_statuses"], ["reports.employment_statuses_from", "reports.employment_statuses_to"], ["positions", "marital_statuses"], ["employees.shield_number", "employees.previous_shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.ncc_date_from", "employees.ncc_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.city", "employees.state"], ["employees.zipcode", "affiliations"]], "actions": ["excel_report", "pdf_report"]}}, "notification": {"key_name": "Notifications", "analytics": "Analytics", "is_search": true, "allow_sms_attachments": true, "check_confirmation": true, "notification_tracker_count": true, "filters": [["employees"], ["offices", "ranks"], ["employment_statuses", "positions"], ["marital_statuses", "genders"], ["employees.social_security_number", "firearm_statuses"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.ncc_date_from", "employees.ncc_date_to"], ["employees.previous_shield_number", "employees.shield_number"], ["employees.city", "employees.state"], ["employees.zipcode", "affiliations"]]}, "user_audit": {"key_name": "User <PERSON>t", "table_headers": ["user_name", "title", "employee_name", "created_at"]}}}