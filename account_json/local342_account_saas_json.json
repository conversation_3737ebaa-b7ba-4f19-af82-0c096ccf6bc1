{"schema": {"employees": {"key_name": "Members", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "suffix": "Suffix", "maiden_name": "Maiden Name", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "birthday": "DOB", "age": "Age", "do_not_mail": "Do Not Mail", "social_security_number": "SSN", "mailing_address": "Previous Address", "start_date": "Eligibility Date", "notes": "Notes", "janus_card": "<PERSON><PERSON>", "janus_card_opt_out_date": "<PERSON><PERSON>t Out Date", "cellphone": "Cell phone", "home_phone": "Home phone", "email": "Personal Email", "social_security_number_format": "9", "username": "Username", "visible_social_security_number": true, "ssn_unique_search": true, "update_previous_address": true, "required_fields": ["name", "first_name", "last_name"], "unique_fields": ["username", "social_security_number"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "suffix"], "associated_model": {"benefit_coverages": ["first_name", "last_name", "suffix"]}}}, "contacts": {"contact_number": {"key_name": "Contact Number", "personal_phone": "Cell Phone", "home_phone": "Home Phone", "required_fields": []}, "email_address": {"key_name": "Email Address", "personal_email": "Personal Email", "required_fields": []}}, "mailing_address": {"key_name": "Previous Address", "address": "Address", "apartment": "Apartment", "city": "City", "state": "State", "street": "Street", "zipcode": "Zipcode"}, "genders": {"key_name": "Gender", "name": "Name", "description": "Description", "required_fields": ["name"]}, "marital_statuses": {"key_name": "Marital Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "contact_persons": {"key_name": "Contact List", "avatar": "Photo", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "primary_work_location": "Company", "notes": "Notes", "cellphone": "Cell phone", "home_phone": "Business Phone", "email": "Email", "required_fields": [], "skip_name_validations": true, "search_columns": {"same_model": ["first_name", "middle_name", "last_name"]}, "contacts": {"contact_number": {"key_name": "Contact Number", "personal_phone": "Cell Phone", "home_phone": "Business Phone", "required_fields": []}}}, "employment_statuses": {"key_name": "Member Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_employment_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "employment_status_id", "start_date"]}, "benefits": {"key_name": "Benefits", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_benefits": {"start_date": "Start Date", "end_date": "End Date", "description": "Description", "required_fields": ["benefit_id", "employee_id", "start_date"]}, "beneficiaries": {"key_name": "Beneficiaries", "name": "Name", "relationship": "Relationship", "address": "Address", "birthday": "DOB", "phone": "Phone Number", "beneficiary_type": "Type", "percentage": "Percentage", "file": "Upload", "required_fields": ["name", "employee_id", "percentage"], "required_auto_fields": ["address"]}, "benefit_coverages": {"key_name": "Benefit Dependents", "first_name": "First Name", "last_name": "Last Name", "suffix": "Suffix", "gender_id": "Gender", "relationship": "Relationship", "birthday": "DOB", "age": "Age", "effective_date": "Effective Date", "expires_at": "Expiration", "student": "Student", "school_status": "School Status", "add_dependent_to_all_benefits": "Revise to all benefits", "disabled_child_edit": true, "update_student_after_expiration": true, "optical_coverage_expire_age": 19, "dental_coverage_expire_age": 19, "expire_relationship_types": ["child"], "required_fields": ["employee_id", "employee_benefit_id", "birthday", "first_name", "last_name", "relationship"], "relationship_value": [{"value": "Member", "key": "member"}, {"value": "Spouse", "key": "spouse"}, {"value": "Child", "key": "child"}, {"value": "Disabled Child", "key": "disabled_child"}], "student_options": [{"value": "Yes", "key": true}, {"value": "No", "key": false}], "school_status_options": [{"value": "Spring", "key": "spring"}, {"value": "Fall", "key": "fall"}]}, "benefit_disbursements": {"key_name": "<PERSON><PERSON><PERSON>", "date": "Claims Service Date", "entry_date": "Entry Date", "relationship": "Relationship", "benefit_coverage_id": "Person Serviced", "notes": "Notes", "order_by_date": true, "required_fields": ["employee_id", "employee_benefit_id"], "custom_validations": {"minimum_one_required_fields": ["date", "entry_date", "relationship", "benefit_coverage_id", "reference_number", "amount", "notes"]}, "relationship_value": [{"value": "Self", "key": "self"}, {"value": "Spouse", "key": "spouse"}, {"value": "Child", "key": "child"}, {"value": "Disabled Child", "key": "disabled_child"}]}, "meeting_types": {"key_name": "Meetings", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_meeting_types": {"meeting_date": "Date", "attended": "Attended", "notes": "Notes", "required_fields": ["meeting_date", "meeting_type_id", "employee_id"]}, "departments": {"key_name": "Mailing List Category", "name": "Name", "notes": "Notes", "required_fields": ["name"]}, "employee_departments": {"key_name": "Mailing List Category", "required_fields": ["department_id", "employee_id"]}, "pacfs": {"key_name": "Dues Payments", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_pacfs": {"date": "Date", "amount": "Amount", "notes": "Notes", "auto_dues_status": true, "required_fields": ["pacf_id", "employee_id"]}, "offices": {"key_name": "Employer", "name": "Name", "address": "Address", "phone": "Phone Number", "required_fields": ["name"]}, "employee_offices": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["office_id", "employee_id"]}, "officer_statuses": {"key_name": "Union Status", "name": "Name", "address": "Address", "phone": "Phone Number", "notes": "Notes", "required_fields": ["name"]}, "employee_officer_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["officer_status_id", "employee_id"]}, "firearm_statuses": {"key_name": "COPE", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_firearm_statuses": {"status_date": "Date", "amount": "Amount", "notes": "Notes", "files": "Uploads", "required_fields": ["firearm_status_id", "employee_id"], "customization": true, "required_tables": ["firearm_statuses"]}, "legislative_addresses": {"key_name": "Legislation", "legislation_details": "Legislation"}, "uploads": {"key_name": "Uploads", "file": "File", "notes": "Notes", "required_fields": ["file", "employee_id"]}, "units": {"key_name": "Title", "name": "Name", "description": "Description", "required_fields": ["name"]}, "reports": {"single_employee": "Single Member", "benefit_coverages": "Benefit Dependents", "janus": "<PERSON><PERSON>", "columns": "Report Columns", "started_at": "From Date", "ended_at": "To Date", "name": "Name", "date_to": "Date To", "date_from": "Date From", "excel_report": "Generate Excel Report", "pdf_report": "Generate PDF Report", "single_mailing_label": "Generate Single Mailing Labels", "multiple_mailing_label": "Generate Multiple Mailing Labels", "congress_district_id": "Congress District Name", "assembly_district_id": "Assembly District Name", "senate_district_id": "Senate District Name", "council_district_id": "Council District Name", "show_coverages": "Show Dependents", "benefit_coverages_options": [{"value": "All members with/without dependents", "key": "all"}, {"value": "Members with dependents", "key": "true"}, {"value": "Members without dependents", "key": "false"}], "member_count": "Member Counts"}, "notifications": {"key_name": "Notifications", "subject": "Subject", "sms_message": "Sms Message", "email_message": "Email Message", "sms": "false", "email": "false", "filter": "Filter", "congress_district_id": "Congress District Name", "assembly_district_id": "Assembly District Name", "senate_district_id": "Senate District Name", "council_district_id": "Council District Name"}, "users": {"key_name": "Users", "username": "Username", "email": "Email", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "password": "Password", "password_confirmation": "Password Confirmation", "user_audit_logging": "User <PERSON>t Logging", "required_fields": ["username", "email", "password", "password_confirmation", "role_id"]}, "roles": {"key_name": "Roles"}, "user_audit": {"key_name": "User <PERSON>t", "user_name": "User Name", "title": "Title", "employee_name": "Employee Name", "created_at": "Created At"}}, "ui": {"notes_timestamps": true, "notes_disabled_fields": ["notes", "description", "recommended_notes"], "employees": {"key_name": "Member List", "is_search": ["employees"], "is_filter": ["employees", "ssn", "birthday"], "show_union_status_top": true, "default_active_status": true, "employment_statuses_count": ["ACTIVE", "UNION", "RETIRED"], "table_headers": ["name", "social_security_number", "birthday", "employment_status_name", "start_date"], "tabs": ["profile", "benefits", "legislative_addresses", "meeting_types", "uploads", "pacfs", "firearm_statuses"], "profile": {"key_name": "Profile", "additional_details": ["first_name", "middle_name", "last_name", "suffix"], "employees": ["name", "maiden_name", "address", "mailing_address", "do_not_mail", "birthday", "age", "social_security_number", "genders", "marital_statuses", "start_date", "notes"], "others": ["employee_employment_statuses", "employee_offices", "employee_officer_statuses", "employees.janus_card", "employees.janus_card_opt_out_date"], "contacts": [{"contact_number": ["personal_phone", "home_phone"]}, {"email_address": ["personal_email"]}]}}, "contact_persons": {"key_name": "Contact List", "avatar_expand_view": true, "is_search": true, "additional_details": ["first_name", "middle_name", "last_name"], "table_headers": ["name", "address", "primary_work_location", "units", "departments"], "employees": ["avatar", "name", "address", "primary_work_location", "units", "employee_departments", "notes"], "contacts": [{"contact_number": ["personal_phone", "home_phone"]}, {"email_address": ["personal_email"]}]}, "users": {"is_search": ["users"], "table_headers": ["username", "name", "email", "roles"]}, "user_audit": {"key_name": "User <PERSON>t", "table_headers": ["user_name", "title", "employee_name", "created_at"]}, "reports": {"key_name": "Reports", "tabs": ["single_employee", "janus", "benefit_coverages", "member_count"], "single_employee": {"primary_filters": [["reports.columns"], ["employees"]], "secondary_filters": [["contact_persons", "employment_statuses"], ["officer_statuses", "employees.social_security_number"], ["employees.start_date_from", "employees.start_date_to"], ["employees.birthday_from", "employees.birthday_to"], ["employees.age_from", "employees.age_to"], ["employees.city", "employees.state"], ["employees.zipcode", "employees.cellphone"], ["employees.home_phone", "employees.email"], ["marital_statuses", "genders"], ["units", "offices"], ["departments", "reports.congress_district_id"], ["reports.assembly_district_id", "reports.senate_district_id"], ["reports.council_district_id", ""]], "columns": ["employees", "employment_statuses", "employees.first_name", "employees.middle_name", "employees.last_name", "employees.social_security_number", "employees.birthday", "employees.cellphone", "employees.home_phone", "employees.email", "employees.start_date", "employees.street", "employees.apartment", "employees.city", "employees.state", "employees.zipcode", "employees.birthday", "marital_statuses", "genders", "departments", "offices", "reports.congress_district_id", "reports.assembly_district_id", "reports.senate_district_id", "reports.council_district_id"], "default_columns": ["employees"], "actions": ["mailing_label", "multiple_mailing_label", "excel_report", "pdf_report"]}, "janus": {"primary_filters": [["employees.janus_card"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["contact_persons", "employment_statuses"], ["officer_statuses", "employees.social_security_number"], ["employees.start_date_from", "employees.start_date_to"], ["employees.birthday_from", "employees.birthday_to"], ["employees.city", "employees.state"], ["employees.zipcode", "employees.cellphone"], ["employees.home_phone", "employees.email"], ["marital_statuses", "genders"], ["units", "offices"], ["departments", "reports.congress_district_id"], ["reports.assembly_district_id", "reports.senate_district_id"], ["reports.council_district_id", ""]], "actions": ["excel_report", "pdf_report"]}, "benefit_coverages": {"primary_filters": [["reports.columns"], ["employees"], ["benefits"], ["reports.show_coverages"]], "secondary_filters": [["contact_persons", "employment_statuses"], ["officer_statuses", "employees.social_security_number"], ["employees.start_date_from", "employees.start_date_to"], ["employees.birthday_from", "employees.birthday_to"], ["employees.city", "employees.state"], ["employees.zipcode", "employees.cellphone"], ["employees.home_phone", "employees.email"], ["marital_statuses", "genders"], ["units", "offices"], ["departments", "reports.congress_district_id"], ["reports.assembly_district_id", "reports.senate_district_id"], ["reports.council_district_id", ""]], "columns": ["employees.first_name", "employees.middle_name", "employees.last_name", "employees.social_security_number", "employees.a_number", "employees.birthday", "employees.start_date", "employees.street", "employees.apartment", "employees.city", "employees.state", "employees.zipcode", "genders", "employees.title_code", "marital_statuses", "tour_of_duties"], "actions": ["excel_report", "pdf_report"]}, "member_count": {"secondary_filters": [], "actions": ["excel_report"]}}, "settings": {"key_name": "Settings", "tabs": ["genders", "marital_statuses", "benefits", "employment_statuses", "officer_statuses", "offices", "meeting_types", "pacfs", "firearm_statuses", "departments", "units"]}, "notification": {"key_name": "Notifications", "analytics": "Analytics", "is_search": true, "allow_sms_attachments": true, "check_confirmation": true, "notification_tracker_count": true, "filters": [["employees"], ["contact_persons", "employment_statuses"], ["officer_statuses", "employees.social_security_number"], ["employees.start_date_from", "employees.start_date_to"], ["employees.birthday_from", "employees.birthday_to"], ["employees.city", "employees.state"], ["employees.zipcode", "employees.cellphone"], ["employees.home_phone", "employees.email"], ["marital_statuses", "genders"], ["units", "offices"], ["departments", "notifications.congress_district_id"], ["notifications.assembly_district_id", "notifications.senate_district_id"], ["notifications.council_district_id", ""]]}}}