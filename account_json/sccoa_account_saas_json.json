{"schema": {"employees": {"key_name": "Members", "avatar": "Member Photo", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "suffix": "Suffix", "maiden_name": "Maiden Name", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "primary_work_location": "Company", "title_code": "Spouse Name", "placard_number": "Employee ID#", "birthday": "DOB", "age": "Age", "shield_number": "Shield Number", "previous_shield_number": "Previous Shield Number", "start_date": "Start Date", "veteran_status": "Veteran Status", "responder_911": "9/11 Re<PERSON>onder", "janus_card": "<PERSON><PERSON>t Out", "janus_card_opt_out_date": "<PERSON><PERSON>t Out Date", "notes": "Notes", "home_phone": "Home Phone", "cellphone": "Cell Phone", "personal_emergency": "Personal Emergency", "colleague_emergency": "Colleague Emergency", "birthday_from": "Birthday From", "birthday_to": "Birthday To", "email": "Email", "start_date_from": "Start Date From", "start_date_to": "Start Date to", "social_security_number": "Social Security Number", "staff_member": "FLSA", "mailing_address": "Mailing Address", "same_as_mailing_address": "Same as Mailing Address", "allow_multiple_present_status": "false", "username": "Username", "app_downloaded": "App Downloaded", "required_fields": ["name", "first_name", "last_name"], "unique_fields": ["username"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "suffix", "shield_number", "birthday"], "associated_model": {"contacts": ["value"]}}, "login_credentials": {"key_name": "Login Information", "username": "Username", "send_credentials": "Update & Send Credentials", "enable_mobile_access": "Allow Access to Mobile app", "required_fields": []}}, "contact_persons": {"key_name": "Contact List", "emergency_contacts": "Emergency Contacts", "avatar": "Photo", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "suffix": "Suffix", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "primary_work_location": "Company", "notes": "Notes", "cellphone": "Cell phone", "home_phone": "Home phone", "work_phone": "Work phone", "email": "Email", "required_fields": ["name", "first_name", "last_name", "address", "city", "state", "street", "zipcode"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "street", "city"]}}, "officer_statuses": {"key_name": "Crew Assign", "name": "Name", "address": "Address", "phone": "Phone Number", "notes": "Notes", "required_fields": ["name"]}, "contacts": {"contact_number": {"key_name": "Contact Number", "home_phone": "Home Phone", "personal_phone": "Cell Phone", "required_fields": []}, "emergency_contacts": {"key_name": "Emergency Contact", "personal_emergency": "Personal", "colleague_emergency": "Colleague", "required_fields": []}, "email_address": {"key_name": "Email Address", "personal_email": "Personal", "work_email": "Backup Personal", "required_fields": []}}, "positions": {"key_name": "Union Position", "name": "Name", "description": "Description", "required_fields": ["name"]}, "offices": {"key_name": "Unit", "name": "Name", "address": "Address", "phone": "Phone Number", "fax": "Description", "required_fields": ["name", "address"]}, "facilities": {"key_name": "Facility", "name": "Name", "address": "Address", "phone": "Phone Number", "fax": "Description", "required_fields": ["name"]}, "employee_ranks": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["rank_id", "employee_id", "start_date"]}, "employee_offices": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["office_id", "employee_id"]}, "mailing_address": {"key_name": "Mailing Address", "address": "Address", "apartment": "Apartment", "city": "City", "state": "State", "street": "Street", "zipcode": "Zipcode"}, "employee_facilities": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["facility_id", "employee_id"]}, "employee_employment_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "employment_status_id"]}, "employee_officer_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["officer_status_id", "employee_id"]}, "employee_positions": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["position_id", "employee_id"]}, "firearm_statuses": {"key_name": "Firearm Status", "name": "Name", "firearm_type": "Type", "firearm_test_type": "Test Type", "required_fields": ["name"]}, "employee_firearm_statuses": {"status_date": "Date", "firearm_type": "Type", "notes": "Notes", "customization": true, "required_fields": ["firearm_status_id", "employee_id"], "required_tables": ["firearm_statuses"]}, "benefit_coverages": {"key_name": "Benefit Coverages", "name": "Name", "relationship": "Relationship", "social_security_number": "employees.social_security_number", "address": "Address", "birthday": "DOB", "expires_at": "Expiration", "coverage_expire_age": 23, "update_unexpire": true, "relationship_value": [{"value": "Spouse", "key": "spouse"}, {"value": "Child", "key": "child"}, {"value": "Disabled Child", "key": "disabled_child"}, {"value": "Domestic Partner", "key": "domestic_partner"}], "required_fields": ["employee_id", "address", "employee_benefit_id", "birthday", "name", "social_security_number", "relationship"]}, "benefit_disbursements": {"key_name": "Benefit Disbursements", "year": "Year", "date": "Date", "reference_number": "Reference Number", "amount": "Amount", "notes": "Notes", "required_fields": ["employee_id", "amount", "employee_benefit_id", "payment_type_id", "date", "reference_number", "year"]}, "employee_discipline_settings": {"discipline_setting_id": "Type", "date": "Date", "description": "Description", "files": "Files", "required_fields": ["employee_id", "discipline_setting_id"]}, "employee_grievances": {"key_name": "Grievances", "date": "Date", "description": "Description", "files": "Files", "required_fields": ["grievance_id"]}, "uploads": {"key_name": "Uploads", "file": "File", "notes": "Notes", "required_fields": ["file", "employee_id"]}, "employment_statuses": {"key_name": "Member Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "marital_statuses": {"key_name": "Marital Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "payment_types": {"key_name": "Payment Types", "name": "Name", "description": "Description", "required_fields": ["name"]}, "discipline_settings": {"key_name": "Disciplines", "name": "Name", "description": "Description", "required_fields": ["name"]}, "grievances": {"key_name": "Grievances", "name": "Name", "description": "Description", "required_fields": ["name"]}, "genders": {"key_name": "Genders", "name": "Name", "description": "Description", "required_fields": ["name"]}, "units": {"key_name": "Political Party", "name": "Name", "description": "Description", "required_fields": ["name"]}, "users": {"key_name": "Users", "username": "Username", "email": "Email", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "password": "Password", "password_confirmation": "Password Confirmation", "user_audit_logging": "User <PERSON>t Logging", "required_fields": ["username", "email", "password", "password_confirmation", "role_id"]}, "members": {"key_name": "Members"}, "titles": {"key_name": "Titles", "name": "Name", "description": "Description", "title_code": "Title Code", "required_fields": ["department_id", "section_id", "name", "title_code"]}, "employee_meeting_types": {"meeting_date": "Date", "attended": "Attended", "notes": "Notes", "required_fields": ["meeting_date", "meeting_type_id", "employee_id"]}, "benefits": {"key_name": "Benefits", "name": "Name", "description": "Description", "required_fields": ["name"]}, "beneficiaries": {"key_name": "Beneficiaries", "name": "Name", "relationship": "Relationship", "address": "Address", "beneficiary_type": "Type", "percentage": "Percentage", "file": "Upload", "required_fields": ["name", "employee_id", "percentage"]}, "employee_benefits": {"start_date": "Start Date", "end_date": "End Date", "description": "Description", "required_fields": ["benefit_id", "employee_id"]}, "reports": {"single_employee": "Single Member", "sick_bank": "Sick Bank", "lodi": "<PERSON><PERSON>", "union_meetings": "Union Meeting", "employee_delegate_assignment": "Member Delegate Assignment", "janus": "<PERSON><PERSON>", "columns": "Report Columns", "started_at": "From Date", "ended_at": "To Date", "employee_summary": "Member Summary", "total_summary": "Total Summary", "active_date": "Active Date", "inactive_date": "Inactive Date", "disbursement_date": "Disbursement Date", "disbursement_year": "Disbursement Year", "disbursements_not_found": "Benefit disbursements not found.", "name": "Name", "date_in": "Date In", "date_out": "Date Out", "used_hours": "Used Hours", "lodi_return_to_work_status": "Return to Work Status", "date_to": "Date To", "date_from": "Date From", "excel_report": "Generate Excel Report", "pdf_report": "Generate PDF Report", "single_mailing_label": "Generate Single Mailing Labels", "multiple_mailing_label": "Generate Multiple Mailing Labels", "payment_date_from": "Payment Date From", "payment_date_to": "Payment Date To", "show_disbursements": "Show Disbursements", "delegates": "Delegates", "meetings": "Meetings", "was_employee_pds": "Was Employee PDS", "app_downloaded": "App Downloaded", "step_values": [{"value": "Step I", "key": "step_1"}, {"value": "Step II", "key": "step_2"}, {"value": "Arbritration", "key": "arbritration"}]}, "notifications": {"key_name": "Notifications", "subject": "Subject", "sms_message": "Sms Message", "email_message": "Email Message", "sms": "false", "email": "false", "filter": "Filter", "sms_attachments": "SMS attachments", "push": "false", "push_message": "Push Message", "change_request_notification": true, "sms_no_reply_text": "Please note, this is a no-reply text.", "default_email_signature": "<p>Although this email was sent from a no-reply address, members are encouraged to contact their Delegate or Building Representative with questions.</p><p><a href=\"mailto:<EMAIL>\" title=\"<EMAIL>\"><EMAIL></a> - Riverhead Building Representative / <a href=\"mailto:<EMAIL>\" title=\"<EMAIL>\"><EMAIL></a> - Yaphank Building Representative</p>"}, "affiliations": {"key_name": "Tier", "name": "Name", "description": "Description", "required_fields": ["name"]}, "platoons": {"key_name": "ID Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "ranks": {"key_name": "Ranks", "name": "Name", "description": "Description", "required_fields": ["name"]}, "departments": {"key_name": "Mailing List Category", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_departments": {"key_name": "Mailing List Category", "required_fields": ["department_id", "employee_id"]}, "meeting_types": {"key_name": "Union Meetings", "name": "Name", "description": "Description", "required_fields": ["name"]}, "roles": {"key_name": "Roles"}, "change_requests": {"key_name": "Change Requests", "employee_name": "Member", "request_type": "Request Type", "status": "Status"}, "user_audit": {"key_name": "User <PERSON>t", "user_name": "User Name", "title": "Title", "employee_name": "Employee Name", "created_at": "Created At"}, "forms": {"key_name": "Forms", "file": "Form", "name": "Form Name", "heading": "Heading"}}, "ui": {"employees": {"key_name": "Member List", "is_search": ["employees"], "employment_statuses_count": ["Active"], "table_headers": ["name", "employment_status_name", "shield_number", "personal_phone", "address"], "tabs": ["profile", "firearm_statuses", "benefits", "discipline_settings", "grievances", "meeting_types", "uploads"], "profile": {"key_name": "Profile", "avatar_expand_view": true, "additional_details": ["first_name", "middle_name", "last_name", "suffix"], "employees": ["avatar", "name", "maiden_name", "address", "same_as_mailing_address", "mailing_address", "genders", "birthday", "age", "marital_statuses", "title_code", "shield_number", "placard_number", "previous_shield_number", "start_date", "staff_member", "affiliations", "platoons", "units", "veteran_status", "responder_911", "notes"], "contacts": [{"contact_number": ["home_phone", "personal_phone"], "emergency_contacts": ["personal_emergency", "colleague_emergency"], "emergency_contact_details": ["name", "contact", "relationship"]}, {"email_address": ["personal_email", "work_email"]}], "others": ["employee_employment_statuses", "employee_facilities", "employee_officer_statuses", "employee_offices", "employee_ranks", "employee_positions", "employees.janus_card", "employees.janus_card_opt_out_date", "employees.app_downloaded"], "login_credentials": ["username", "send_credentials", "enable_mobile_access"]}, "hide_benefits": true, "grievances": {"table_headers": ["grievance", "date", "description", "files"]}}, "contact_persons": {"key_name": "Contact List", "avatar_expand_view": true, "is_search": true, "additional_details": ["first_name", "middle_name", "last_name"], "table_headers": ["name", "address", "primary_work_location"], "employees": ["avatar", "name", "address", "primary_work_location", "employee_departments", "notes"], "contacts": [{"contact_number": ["work_phone", "personal_phone", "home_phone"]}, {"email_address": ["work_email", "personal_email"]}]}, "employee_grievances": {"key_name": "Grievances", "table_headers": ["date", "description", "files"]}, "settings": {"key_name": "Settings", "tabs": ["genders", "marital_statuses", "employment_statuses", "affiliations", "positions", "facilities", "offices", "officer_statuses", "ranks", "discipline_settings", "grievances", "meeting_types", "firearm_statuses", "units", "payment_types", "departments"]}, "users": {"is_search": ["users"], "table_headers": ["username", "name", "email", "roles"]}, "reports": {"key_name": "Reports", "tabs": ["single_employee", "disciplines", "grievances", "union_meetings", "janus"], "single_employee": {"primary_filters": [["reports.columns"], ["employees"]], "secondary_filters": [["employment_statuses", "offices"], ["genders", "marital_statuses"], ["employees.email", "officer_statuses"], ["departments", "ranks"], ["employees.birthday_from", "employees.birthday_to"], ["employees.age_from", "employees.age_to"], ["employees.shield_number", "employees.previous_shield_number"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.city", "employees.state"], ["employees.zipcode", "positions"], ["facilities", "employees.app_downloaded"]], "columns": ["employees", "employees.first_name", "employees.middle_name", "employees.last_name", "employees.suffix", "titles", "employment_statuses", "employees.email", "marital_statuses", "positions", "employees.shield_number", "employees.previous_shield_number", "employees.birthday", "employees.home_phone", "employees.cellphone", "employees.street", "employees.city", "employees.state", "employees.zipcode", "employees.start_date", "units", "genders", "affiliations", "titles.title_code", "ranks", "employees.app_downloaded"], "default_columns": ["employees"], "actions": ["mailing_label", "excel_report", "pdf_report"]}, "disciplines": {"primary_filters": [["discipline_settings"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["offices", "units"], ["employment_statuses", "marital_statuses"], ["officer_statuses", "employees.email"], ["departments", "ranks"], ["employees.shield_number", "employees.previous_shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.city", "employees.state"], ["employees.zipcode", "positions"], ["facilities", "employees.app_downloaded"]], "actions": ["excel_report", "pdf_report"], "steps": ["step_1", "step_2", "arbritration"]}, "grievances": {"primary_filters": [["grievances"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["titles", "employment_statuses"], ["officer_statuses", "offices"], ["marital_statuses", "employees.email"], ["employees.shield_number", "employees.previous_shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.city", "employees.state"], ["employees.zipcode", "units"], ["positions", "ranks"], ["departments", "facilities"], ["employees.app_downloaded", ""]], "report_columns": ["number"], "actions": ["excel_report", "pdf_report"], "steps": ["step_1", "step_2", "arbritration"]}, "union_meetings": {"primary_filters": [["meeting_types"], ["reports.started_at", "reports.ended_at"], ["reports.meetings"]], "secondary_filters": [["employees"], ["offices", "genders"], ["employment_statuses", "marital_statuses"], ["employees.shield_number", "employees.previous_shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.city", "employees.state"], ["employees.zipcode", "officer_statuses"], ["positions", "ranks"], ["departments", "facilities"], ["employees.app_downloaded", ""]], "actions": ["excel_report", "pdf_report"]}, "janus": {"primary_filters": [["employees.janus_card"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["titles", "employment_statuses"], ["marital_statuses", "employees.email"], ["employees.shield_number", "employees.previous_shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.city", "employees.state"], ["employees.zipcode", "units"], ["officer_statuses", "offices"], ["positions", "ranks"], ["departments", "facilities"], ["employees.app_downloaded", ""]], "actions": ["excel_report", "pdf_report"]}}, "notification": {"key_name": "Notifications", "analytics": "Analytics", "is_search": true, "check_confirmation": true, "notification_tracker_count": true, "default_notification_receivers": ["<PERSON>", "<PERSON>", "<PERSON>"], "allow_sms_attachments": true, "filters": [["employees", "contact_persons"], ["titles", "employment_statuses"], ["genders", "marital_statuses"], ["positions", "employees.email"], ["officer_statuses", "offices"], ["departments", "ranks"], ["employees.birthday_from", "employees.birthday_to"], ["employees.shield_number", "employees.previous_shield_number"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.city", "employees.state"], ["employees.zipcode", "units"], ["departments", "facilities"], ["employees.app_downloaded", ""]]}, "change_requests": {"key_name": "Change Requests", "employee": "General Info", "contact": "Contact Info", "request_header": "Request Header", "current_value": "Current Status", "requested_changes": "Requested Changes", "status": "Status", "action": "Action", "table_headers": ["request_header", "current_value", "requested_changes", "status", "action"]}, "user_audit": {"key_name": "User <PERSON>t", "table_headers": ["user_name", "title", "employee_name", "created_at"]}}, "mobile": {"employees": {"key_name": "Profile", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/memberprofile.png", "welcome_message": {"header": "Presidents Message", "description": "Welcome to the SCCOA Member App. This app was created by the Executive Board to bring useful resources to our members. This app will also be used to communicate with members, so please make sure notifications are enabled. If you have any ideas for additional information that can be added in the future, please email", "email": "<EMAIL>", "regards": "Stay safe,\n<PERSON>", "button_name": "CLICK HERE TO ENTER"}, "tabs": ["profile", "contacts", "employee_facilities", "employee_officer_statuses"], "profile": {"key_name": "General Info", "request_type": "employee", "widget_type": "Info", "api": "employees/profile", "hint": {"caption": "PLEASE NOTE *", "caption_color": "#d93025", "text": "If any non-editable items need to be revised please contact the SCCOA office."}, "actions": ["view", "edit"], "attributes": {"first_name": {"type": "SingleLineText", "name": "First Name", "required": true, "actions": ["view", "edit"]}, "middle_name": {"type": "SingleLineText", "name": "Middle Name", "actions": ["view", "edit"]}, "last_name": {"type": "SingleLineText", "name": "Last Name", "required": true, "actions": ["view", "edit"]}, "suffix": {"type": "SingleLineText", "name": "Suffix", "actions": ["view", "edit"]}, "maiden_name": {"type": "SingleLineText", "name": "Maiden Name", "actions": ["view", "edit"]}, "street": {"type": "SingleLineText", "name": "Address", "actions": ["view", "edit"]}, "apartment": {"type": "SingleLineText", "name": "Apt", "actions": ["view", "edit"]}, "city": {"type": "SingleLineText", "name": "City", "actions": ["view", "edit"]}, "state": {"type": "SingleLineText", "name": "State", "actions": ["view", "edit"]}, "zipcode": {"type": "ZipCode", "name": "Zip Code", "actions": ["view", "edit"]}, "same_as_mailing_address": {"type": "CheckBox", "name": "Same as Mailing Address", "actions": ["edit"]}, "mailing_address_street": {"type": "SingleLineText", "name": "Mailing Address", "change_request_attribute": "mailing_address_attributes", "change_request_key": "street", "actions": ["view", "edit"]}, "mailing_address_apartment": {"type": "SingleLineText", "name": "Mailing Address Apt", "change_request_attribute": "mailing_address_attributes", "change_request_key": "apartment", "actions": ["view", "edit"]}, "mailing_address_city": {"type": "SingleLineText", "name": "Mailing Address City", "change_request_attribute": "mailing_address_attributes", "change_request_key": "city", "actions": ["view", "edit"]}, "mailing_address_state": {"type": "SingleLineText", "name": "Mailing Address State", "change_request_attribute": "mailing_address_attributes", "change_request_key": "state", "actions": ["view", "edit"]}, "mailing_address_zipcode": {"type": "ZipCode", "name": "Mailing Address Zip Code", "change_request_attribute": "mailing_address_attributes", "change_request_key": "zipcode", "actions": ["view", "edit"]}, "birthday": {"type": "Date", "name": "DOB", "actions": ["view"]}, "age": {"type": "Label", "name": "Age", "actions": ["view"]}, "marital_status_name": {"type": "DropDown", "name": "Marital Status", "api": "marital_statuses", "api_key": "marital_status_id", "actions": ["view", "edit"]}, "title_code": {"type": "SingleLineText", "name": "Spouse Name", "actions": ["view", "edit"]}, "placard_number": {"type": "SingleLineText", "name": "Employee ID#", "actions": ["view", "edit"]}, "shield_number": {"type": "SingleLineText", "name": "Shield Number", "actions": ["view"]}, "previous_shield_number": {"type": "SingleLineText", "name": "Previous Shield Number", "actions": ["view"]}, "start_date": {"type": "Date", "name": "Start Date", "actions": ["view"]}, "staff_member": {"type": "Radio", "name": "FLSA", "actions": ["view"]}, "unit_name": {"type": "DropDown", "name": "Political Party", "api": "units", "api_key": "unit_id", "actions": ["view", "edit"]}, "veteran_status": {"type": "Radio", "name": "Veteran Status", "actions": ["view"]}}}, "contacts": {"key_name": "Contact Info", "request_type": "contact", "widget_type": "Info", "api": "contacts?employee_id=[EMPLOYEE_ID]", "actions": ["view", "edit"], "sub_sections": ["contact_number", "email_address", "emergency_contacts"], "contact_number": {"key_name": "Contact Number", "api": "contacts?employee_id=[EMPLOYEE_ID]&contact_type=phone", "attributes": {"home_phone": {"type": "PhoneNumber", "name": "Home Phone", "contact_for": "home", "contact_type": "phone", "contact_attribute": "value", "actions": ["view", "edit"]}, "personal_phone": {"type": "PhoneNumber", "name": "Cell Phone", "contact_for": "personal", "contact_type": "phone", "contact_attribute": "value", "actions": ["view", "edit"]}}}, "email_address": {"key_name": "Email", "api": "contacts?employee_id=[EMPLOYEE_ID]&contact_type=email", "attributes": {"personal_email": {"type": "Email", "name": "Personal Email", "contact_for": "personal", "contact_type": "email", "contact_attribute": "value", "actions": ["view", "edit"]}, "work_email": {"type": "Email", "name": "Backup Personal Email", "contact_for": "work", "contact_type": "email", "contact_attribute": "value", "actions": ["view", "edit"]}}}, "emergency_contacts": {"key_name": "Emergency Contacts", "api": "contacts?employee_id=[EMPLOYEE_ID]&contact_type=emergency", "attributes": {"personal_emergency_name": {"type": "SingleLineText", "name": "Personal Emergency Contact Name", "contact_for": "personal", "contact_type": "emergency", "contact_attribute": "contact_name", "actions": ["view", "edit"]}, "personal_emergency_phone": {"type": "PhoneNumber", "name": "Personal Emergency Contact Phone", "contact_for": "personal", "contact_type": "emergency", "contact_attribute": "value", "actions": ["view", "edit"]}, "personal_emergency_relationship": {"type": "SingleLineText", "name": "Personal Emergency Contact Relationship", "contact_for": "personal", "contact_type": "emergency", "contact_attribute": "contact_relationship", "actions": ["view", "edit"]}, "colleague_emergency_name": {"type": "SingleLineText", "name": "Colleague Emergency Contact Name", "contact_for": "colleague", "contact_type": "emergency", "contact_attribute": "contact_name", "actions": ["view", "edit"]}, "colleague_emergency_phone": {"type": "PhoneNumber", "name": "Colleague Emergency Contact Phone", "contact_for": "colleague", "contact_type": "emergency", "contact_attribute": "value", "actions": ["view", "edit"]}, "colleague_emergency_relationship": {"type": "SingleLineText", "name": "Colleague Emergency Contact Relationship", "contact_for": "colleague", "contact_type": "emergency", "contact_attribute": "contact_relationship", "actions": ["view", "edit"]}}}}, "employee_facilities": {"key_name": "Facilities", "request_type": "employee_facility", "empty_message": "No Data Available", "actions": ["view"], "api": "employee_facilities?employee_id=[EMPLOYEE_ID]", "widget_type": "Status", "attributes": {"facility_name": {"type": "DropDown", "name": "Division", "api": "facilities", "api_key": "facility_id", "required": true, "actions": ["view"]}, "start_date": {"type": "Date", "name": "Start Date", "actions": ["view"]}, "end_date": {"type": "Date", "name": "End Date", "actions": ["view"]}}}, "employee_officer_statuses": {"key_name": "Crew Assign", "request_type": "employee_officer_status", "empty_message": "No Data Available", "actions": ["view"], "api": "employee_officer_statuses?employee_id=[EMPLOYEE_ID]", "widget_type": "Status", "attributes": {"officer_status_name": {"type": "DropDown", "name": "Crew Assign", "api": "officer_statuses", "api_key": "officer_status_id", "required": true, "actions": ["view"]}, "start_date": {"type": "Date", "name": "Start Date", "actions": ["view"]}, "end_date": {"type": "Date", "name": "End Date", "actions": ["view"]}}}}, "benefits": {"key_name": "Benefits", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/benefits.png", "tabs": ["beneficiaries"], "beneficiaries": {"key_name": "Beneficiaries", "request_type": "beneficiary", "required_key": "beneficiary_type", "empty_message": "There are no Beneficiaries posted.", "actions": ["view", "edit"], "api": "beneficiaries?employee_id=[EMPLOYEE_ID]", "widget_type": "Analysis", "hint": {"caption": "PLEASE NOTE *", "caption_color": "#d93025", "text": "For a change in Beneficiary to be valid and finalized please submit the change of Beneficiary form for the corresponding insurance policy. This is just for the policy provided as a SCCOA contractual benefit.", "show_links_in_add": true, "links": {"Beneficiary Form": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/sccoa/Beneficiary+Form.pdf"}}, "attributes": {"name": {"type": "SingleLineText", "name": "Name", "required": true, "actions": ["view", "edit"]}, "relationship": {"type": "SingleLineText", "name": "Relationship", "actions": ["view", "edit"]}, "address": {"type": "SingleLineText", "name": "Address", "actions": ["view", "edit"]}, "beneficiary_type": {"type": "DropDown", "name": "Type", "value": {"Primary": "Primary", "Secondary": "Secondary"}, "actions": ["view", "edit"]}, "percentage": {"type": "Number", "name": "Percentage", "required": true, "actions": ["view", "edit"]}, "file": {"type": "FileField", "name": "Upload", "selection_type": "single", "max_file_size": 10, "total_files": 1, "actions": ["view", "edit"]}}}}, "forms": {"key_name": "Frequently Used Resources", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/form.png", "empty_message": "There are no Frequently Used Resources.", "api": "forms?file_type=form", "widget_type": "Forms", "actions": ["view"], "headings": ["Contractual Resources", "Health and Wellness Resources", "S.C.C.O.A. Resources"], "attributes": {"file": {"type": "FileField", "name": "Form", "actions": ["view"]}, "name": {"type": "SingleLineText", "name": "Form Name", "actions": ["view"]}}}, "links": {"key_name": "Helpful Links", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/link.png", "empty_message": "There are no Helpful Links.", "api": "forms?file_type=useful_links", "widget_type": "Links", "actions": ["view"], "headings": ["Agency Links", "Health and Wellness", "SCSO Links"], "attributes": {"name": {"type": "SingleLineText", "name": "Name", "actions": ["view"]}, "link": {"type": "SingleLineText", "name": "Link", "actions": ["view"]}}}, "uploads": {"key_name": "Uploads", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/upload.png", "empty_message": "No Data Available", "actions": ["view", "new"], "attributes": {"file": {"type": "FileField", "name": "File", "selection_type": "single", "max_file_size": 10, "total_files": 1, "required": true, "actions": ["view", "edit"]}, "date": {"type": "FromAPI", "name": "Date", "actions": ["view"]}, "notes": {"type": "MultiLineText", "name": "Notes", "actions": ["view", "edit"]}}}, "notification": {"key_name": "Notifications", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/notification.png", "empty_message": "There are no Notifications.", "api": "push_notification_index"}, "contact_us": {"key_name": "Contact Us", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/contact_us.png", "to": "<EMAIL>,<EMAIL>", "subject": "SCCOA: Feedback/Suggestion", "default_message": null}, "update_password": {"key_name": "Update Password", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/reset-password.png", "api": "employees/update_password"}}}