{"schema": {"benefits": {"key_name": "Benefits", "name": "Name", "description": "Description", "required_fields": ["name"]}, "life_insurances": {"key_name": "Life Insurance", "insurance_type": "Type", "amount": "Amount", "start_date": "As of Date", "age": "Age", "notes": "Notes", "files": "Uploads", "member_contributions": "Member Contribution", "age_group_type": "Age Group", "required_fields": ["insurance_type", "employee_id", "amount", "start_date"], "member_contribution": [{"low": 1, "high": 29, "amount": {"50000": 1.2, "100000": 2.4, "200000": 4.8}}, {"low": 30, "high": 34, "amount": {"50000": 1.37, "100000": 2.74, "200000": 5.47}}, {"low": 35, "high": 39, "amount": {"50000": 1.68, "100000": 3.36, "200000": 6.72}}, {"low": 40, "high": 44, "amount": {"50000": 2.4, "100000": 4.8, "200000": 9.6}}, {"low": 45, "high": 49, "amount": {"50000": 3.6, "100000": 7.2, "200000": 14.4}}, {"low": 50, "high": 54, "amount": {"50000": 5.52, "100000": 11.04, "200000": 22.08}}, {"low": 55, "high": 59, "amount": {"50000": 9.96, "100000": 19.92, "200000": 39.84}}, {"low": 60, "high": 64, "amount": {"50000": 15.05, "100000": 30.1, "200000": 60.2}}, {"low": 65, "high": 69, "amount": {"50000": 25.6, "100000": 51.22, "200000": 102.43}}, {"low": 70, "high": 99, "amount": {"50000": 48.38, "100000": 96.77, "200000": 193.53}}], "life_insurances_premium": {"key_name": "Premium", "member_contribution": "Member Contribution", "total_contribution": "Total Contribution"}, "insurance_amount": {"supplemental": [50000, 100000, 200000], "basic": [15000]}, "insurance_types": [{"value": "Basic", "key": "basic"}, {"value": "Supplemental", "key": "supplemental"}]}, "dependents": {"key_name": "Dependent", "name": "Name", "relationship": "Relationship", "amount": "Amount", "spouse_contribution": "Spouse Contribution", "relationship_amount_validation": {"basic": {"spouse": [15000], "child": [5000]}, "supplemental": {"spouse": [25000, 50000, 100000]}}, "relationship_value": [{"value": "Spouse", "key": "spouse"}, {"value": "Child", "key": "child"}], "address": "Address", "date": "DOB", "age": "Age", "required_fields": ["life_insurance_id", "employee_id", "name", "relationship", "amount"], "spouse_contribution_value": {"amount": {"25000": 1.82, "50000": 3.63, "100000": 7.26, "1000000": 999.99}}, "dependents_premium": {"key_name": "Premium", "spouse_contribution": "Spouse Contribution", "total_contribution": "Total Contribution"}}, "notifications": {"key_name": "Notifications", "subject": "Subject", "sms_message": "Sms Message", "email_message": "Email Message", "sms": "false", "email": "false", "filter": "Filter", "sms_no_reply_text": "Please note, this is a no-reply text notification."}, "offices": {"key_name": "Unit/Platoon", "name": "Name", "address": "Address", "phone": "Phone Number", "fax": "Description", "required_fields": ["name", "address"]}, "firearm_statuses": {"key_name": "Firearm Status", "name": "Name", "firearm_type": "Type", "firearm_test_type": "Test Type", "required_fields": ["name"]}, "ranks": {"key_name": "Ranks", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employment_statuses": {"key_name": "Employment Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "marital_statuses": {"key_name": "Marital Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "positions": {"key_name": "Positions", "name": "Name", "description": "Description", "required_fields": ["name"]}, "payment_types": {"key_name": "Payment Types", "name": "Name", "description": "Description", "required_fields": ["name"]}, "discipline_settings": {"key_name": "Disciplines", "name": "Name", "description": "Description", "required_fields": ["name"]}, "grievances": {"key_name": "Grievances", "name": "Name", "description": "Description", "required_fields": ["name"]}, "grievance_statuses": {"key_name": "Grievance Statuses", "name": "Name", "description": "Description", "required_fields": ["name"]}, "meeting_types": {"key_name": "Meeting Types", "name": "Name", "description": "Description", "required_fields": ["name"]}, "genders": {"key_name": "Genders", "name": "Name", "description": "Description", "required_fields": ["name"]}, "leaves": {"started_at": "From", "ended_at": "To", "hours_used": "No. of hours used", "notes": "Notes", "required_fields": ["started_at", "leave_type", "hours_used"]}, "lodis": {"key_name": "<PERSON><PERSON>", "incident_date": "Date of incident", "notified_date": "Date COBA Notified", "return_date": "Date of return", "office_name": "Location", "injury": "Injury #", "wcb": "WCB #", "carrier_case": "Carrier Case #", "approved": "Approved", "denied": "Denied", "denied_reason_ids": "Reason Denied", "notes": "Remarks", "files": "Files", "required_fields": ["employee_id", "incident_date"]}, "lodi_request_tab": {"date": "Date", "reason": "Reason for Request", "status": "Status", "remarks": "Remarks", "files": "Uploads", "required_fields": [], "reasons_types": {"medscope": ["Causal Connection", "Recurrence/Aggravation", "Police Surgeon Dispute", "Paragraph-13 Disputes"], "hearing": ["Causal Connection", "Recurrence/Aggravation", "Police Surgeon Dispute", "Did not occur in the Performance of Duties", "No loss Time/Disability, Evaluation/Treatment Only", "Other"], "arbitration": ["Causal Connection", "Did not occur in the Performance of Duties", "No loss Time/Disability, Evaluation/Treatment Only", "Other"]}, "status_types": {"medscope": ["Granted", "Denied", "Pending", "Moved to Arbitration", "Moved to 207c Hearing"], "hearing": ["Granted", "Denied", "Pending"], "arbitration": ["Granted", "Denied", "Pending", "Settled", "Other"]}}, "users": {"key_name": "Users", "username": "Username", "email": "Email Address", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "password": "Password", "password_confirmation": "Password Confirmation", "user_audit_logging": "User <PERSON>t Logging", "required_fields": ["username", "email", "password", "password_confirmation", "role_id"]}, "roles": {"key_name": "Roles"}, "employees": {"key_name": "Members", "avatar": "Member Photo", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "do_not_mail": "Do Not Mail", "birthday": "DOB", "age": "Age", "social_security_number": "Social Security Number", "social_security_number_format": "4", "veteran_status": "Veteran Status", "responder_911": "9/11 Re<PERSON>onder", "janus_card": "<PERSON><PERSON>t Out", "janus_card_opt_out_date": "<PERSON><PERSON> Opt Out Date", "shield_number": "Shield Number", "ncc_date": "NCC Date", "longevity_date": "Longevity Date", "leave_progression_date": "Leave Progression Date", "payroll_id": "Employee ID", "notes": "Notes", "cellphone": "Cell phone", "home_phone": "Home phone", "work_phone": "Work phone", "email": "Email", "allow_multiple_present_status": "false", "required_fields": ["name", "address", "first_name", "last_name", "birthday", "city", "payroll_id", "state", "street", "zipcode"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "shield_number", "street", "birthday"], "associated_model": {"contacts": ["value"]}}}, "contacts": {"contact_number": {"key_name": "Contact Number", "work_phone": "Work", "personal_phone": "Cell", "home_phone": "Home", "required_fields": []}, "email_address": {"key_name": "Email Address", "work_email": "Work", "personal_email": "Personal", "required_fields": []}}, "employee_employment_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "employment_status_id", "start_date"]}, "employee_offices": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["office_id", "employee_id", "start_date"]}, "employee_positions": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["position_id", "employee_id", "start_date"]}, "employee_ranks": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["rank_id", "employee_id", "start_date"]}, "employee_firearm_statuses": {"status_date": "Date", "firearm_type": "Type", "notes": "Notes", "required_fields": ["firearm_status_id", "employee_id"], "customization": true, "required_tables": ["firearm_statuses"]}, "employee_benefits": {"start_date": "Start Date", "end_date": "End Date", "description": "Description", "required_fields": ["benefit_id", "employee_id", "start_date"]}, "beneficiaries": {"key_name": "Beneficiaries", "name": "Name", "relationship": "Relationship", "address": "Address", "beneficiary_type": "Type", "percentage": "Percentage", "file": "Upload", "required_fields": ["name", "employee_id", "percentage"]}, "benefit_coverages": {"key_name": "Benefit Coverages", "name": "Name", "relationship": "Relationship", "social_security_number": "employees.social_security_number", "address": "Address", "birthday": "DOB", "expires_at": "Expiration", "required_fields": ["employee_id", "address", "employee_benefit_id", "birthday", "name", "social_security_number", "relationship"]}, "benefit_disbursements": {"key_name": "Benefit Disbursements", "year": "Year", "date": "Date", "reference_number": "Reference Number", "amount": "Amount", "notes": "Notes", "required_fields": ["employee_id", "amount", "employee_benefit_id", "payment_type_id", "date", "reference_number", "year"]}, "employee_discipline_settings": {"discipline_setting_id": "Penalty Imposed", "charge": "Charge", "dan_number": "Case Number", "date": "Date", "filed_olr": "Filed OLR", "description": "Remarks", "files": "Files", "required_fields": ["employee_id", "discipline_setting_id"]}, "employee_discipline_steps": {"date": "Date", "recommended_notes": "Remarks", "is_settled": "Settled", "is_pending": "Pending", "win": "Win", "loss": "Loss", "files": "Uploads", "required_fields": ["step", "employee_discipline_setting_id"]}, "employee_grievances": {"key_name": "Class Action Grievance", "charge": "Charge", "number": "Case Number", "date": "Date", "filed_olr": "Filed OLR", "description": "Remarks", "files": "Uploads", "required_fields": ["grievance_id"]}, "employee_grievance_steps": {"date": "Date", "grievance_status_id": "Status", "recommended_notes": "Remarks", "is_settled": "Settled", "is_pending": "Pending", "win": "Win", "loss": "Loss", "files": "Uploads", "required_fields": ["step", "employee_grievance_id"]}, "employee_meeting_types": {"meeting_date": "Date", "attended": "Attended", "notes": "Notes", "required_fields": ["meeting_date", "meeting_type_id", "employee_id"]}, "uploads": {"key_name": "Uploads", "file": "File", "notes": "Notes", "required_fields": ["file", "employee_id"]}, "reports": {"single_employee": "Single Member", "sick_bank": "Sick Bank", "lodi": "<PERSON><PERSON>", "janus": "<PERSON><PERSON>", "union_meetings": "Union Meetings", "columns": "Report Columns", "started_at": "From Date", "ended_at": "To Date", "employee_summary": "Member Summary", "total_summary": "Total Summary", "active_date": "Active Date", "inactive_date": "Inactive Date", "disbursement_date": "Disbursement Date", "disbursement_year": "Disbursement Year", "disbursements_not_found": "Benefit disbursements not found.", "name": "Name", "date_in": "Date In", "date_out": "Date Out", "used_hours": "Used Hours", "lodi_return_to_work_status": "Return to Work Status", "date_to": "Date To", "date_from": "Date From", "excel_report": "Generate Excel Report", "pdf_report": "Generate PDF Report", "single_mailing_label": "Generate Single Mailing Labels", "multiple_mailing_label": "Generate Multiple Mailing Labels", "payment_date_from": "Payment Date From", "payment_date_to": "Payment Date To", "show_disbursements": "Show Disbursements", "delegates": "Delegates", "meetings": "Meetings", "settled_steps": "Settled Steps", "pending_steps": "Pending Steps", "filed_olr_from_date": "OLR From Date", "filed_olr_to_date": "OLR To Date", "step_1_from_date": "Step I From Date", "step_1_to_date": "Step I To Date", "step_2_from_date": "Step II From Date", "step_2_to_date": "Step II To Date", "step_3_from_date": "Step III From Date", "step_3_to_date": "Step III To Date", "arbritration_from_date": "Arbritration From Date", "arbritration_to_date": "Arbritration To Date", "win": "Win", "loss": "Loss", "dob_ranges": "DOB Ranges", "life_insurances": "Life Insurances", "age_group_type": "Type", "dob_ranges_options": [{"value": "Less than 30", "key": "30"}, {"value": "30 to 34", "key": "30-34"}, {"value": "35 to 39", "key": "35-39"}, {"value": "40 to 44", "key": "40-44"}, {"value": "45 to 49", "key": "45-49"}, {"value": "50 to 54", "key": "50-54"}, {"value": "55 to 59", "key": "55-59"}, {"value": "60 to 64", "key": "60-64"}, {"value": "65 to 69", "key": "65-69"}, {"value": "70 to 99", "key": "70-99"}], "step_values": [{"value": "Step I", "key": "step_1"}, {"value": "Step II", "key": "step_2"}, {"value": "Step III", "key": "step_3"}, {"value": "Arbritration", "key": "arbritration"}]}, "common_terms": {"step_1": "STEP I", "step_2": "STEP II", "step_3": "STEP III", "arbritration": "ARBRITRATION", "none": "None", "basic": "Basic", "premium": "Premium", "supplemental": "Supplemental", "life_insurances": "Life Insurances", "dependents": "Dependents", "lodis": "<PERSON><PERSON>", "medscope": "Medscope", "hearing": "Hearing 207c", "arbitration": "Arbitration"}, "employee_analytics": {"key_name": "Analytics", "customization": true, "required_tables": ["lodis"]}, "user_audit": {"key_name": "User <PERSON>t", "user_name": "User Name", "title": "Title", "employee_name": "Employee Name", "created_at": "Created At"}}, "ui": {"is_read_view_allows": true, "employees": {"key_name": "Member List", "is_search": ["employees"], "table_headers": ["name", "employment_status_name", "payroll_id", "shield_number", "birthday", "ncc_date", "personal_phone"], "tabs": ["profile", "firearm_statuses", "employee_analytics", "benefits", "discipline_settings", "grievances", "meeting_types", "uploads"], "profile": {"key_name": "Profile", "avatar_expand_view": true, "additional_details": ["first_name", "middle_name", "last_name"], "employees": ["avatar", "name", "address", "do_not_mail", "birthday", "age", "social_security_number", "payroll_id", "genders", "marital_statuses", "veteran_status", "shield_number", "ncc_date", "longevity_date", "leave_progression_date", "responder_911", "notes"], "contacts": [{"contact_number": ["work_phone", "personal_phone", "home_phone"]}, {"email_address": ["work_email", "personal_email"]}], "others": ["employee_employment_statuses", "employee_offices", "employee_positions", "employees.janus_card", "employees.janus_card_opt_out_date", "employee_ranks"]}, "discipline_settings": {"tabs": ["discipline_settings", "step_1", "step_2", "step_3", "arbritration"], "table_headers": ["dan_number", "discipline_setting", "charge", "date"]}, "grievances": {"tabs": ["grievances", "step_1", "step_2", "step_3", "arbritration"], "table_headers": ["grievance", "charge", "number", "date"]}, "life_insurances": {"table_headers": ["insurance_type", "amount", "start_date", "files", "notes"], "insurance_type": ["basic", "premium", "supplemental", "none"], "insurance_amount": [50000, 100000, 200000], "tabs": ["life_insurances", "premium"]}, "dependents": {"tabs": ["dependents", "premium"]}, "lodis": {"tabs": ["lodis", "medscope", "hearing", "arbitration"], "table_headers": ["incident_date", "notified_date", "return_date", "office_name", "notes", "files"]}}, "employee_grievances": {"key_name": "Class Action Grievance", "table_headers": ["charge", "number", "date", "filed_olr", "description", "files"]}, "settings": {"key_name": "Settings", "tabs": ["benefits", "offices", "firearm_statuses", "ranks", "employment_statuses", "marital_statuses", "positions", "payment_types", "discipline_settings", "grievances", "grievance_statuses", "meeting_types", "genders"]}, "users": {"is_search": ["users"], "table_headers": ["username", "name", "email", "roles"]}, "reports": {"key_name": "Reports", "tabs": ["single_employee", "disciplines", "grievances", "benefits", "lodi", "union_meetings", "janus", "life_insurances"], "single_employee": {"primary_filters": [["reports.columns"], ["employees"]], "secondary_filters": [["offices", "genders"], ["ranks", "employment_statuses"], ["positions", "firearm_statuses"], ["marital_statuses", "employees.social_security_number"], ["employees.shield_number", "employees.payroll_id"], ["employees.birthday_from", "employees.birthday_to"], ["employees.age_from", "employees.age_to"], ["employees.ncc_date_from", "employees.ncc_date_to"], ["employees.longevity_date_from", "employees.longevity_date_to"], ["employees.leave_progression_date_from", "employees.leave_progression_date_to"], ["employees.email", "employees.home_phone"], ["employees.cellphone", "employees.work_phone"], ["employees.city", "employees.state"], ["employees.zipcode", "employees.responder_911"]], "columns": ["employees", "employees.first_name", "employees.middle_name", "employees.last_name", "offices", "ranks", "employment_statuses", "employees.email", "firearm_statuses", "marital_statuses", "positions", "employees.social_security_number", "employees.shield_number", "employees.payroll_id", "employees.birthday", "employees.home_phone", "employees.cellphone", "employees.work_phone", "employees.city", "employees.state", "employees.zipcode", "employees.ncc_date", "employees.longevity_date", "employees.leave_progression_date", "employees.apartment", "employees.street", "genders"], "default_columns": ["employees"], "actions": ["mailing_label", "excel_report", "pdf_report"]}, "benefits": {"primary_filters": [["benefits"], ["reports.active_date", "reports.inactive_date"], ["payment_types", "reports.disbursement_year"], ["reports.payment_date_from", "reports.payment_date_to"], ["reports.show_disbursements", ""]], "secondary_filters": [["employees"], ["offices", "genders"], ["ranks", "employment_statuses"], ["positions", "firearm_statuses"], ["marital_statuses", "employees.social_security_number"], ["employees.shield_number", "employees.payroll_id"], ["employees.birthday_from", "employees.birthday_to"], ["employees.ncc_date_from", "employees.ncc_date_to"], ["employees.longevity_date_from", "employees.longevity_date_to"], ["employees.leave_progression_date_from", "employees.leave_progression_date_to"], ["employees.email", "employees.home_phone"], ["employees.cellphone", "employees.work_phone"], ["employees.city", "employees.state"], ["employees.zipcode", ""]], "actions": ["pdf_report"]}, "sick_bank": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["offices", "genders"], ["ranks", "employment_statuses"], ["positions", "firearm_statuses"], ["marital_statuses", "employees.social_security_number"], ["employees.shield_number", "employees.payroll_id"], ["employees.birthday_from", "employees.birthday_to"], ["employees.ncc_date_from", "employees.ncc_date_to"], ["employees.longevity_date_from", "employees.longevity_date_to"], ["employees.leave_progression_date_from", "employees.leave_progression_date_to"], ["employees.email", "employees.home_phone"], ["employees.cellphone", "employees.work_phone"], ["employees.city", "employees.state"], ["employees.zipcode", ""]], "actions": ["excel_report", "pdf_report"]}, "lodi": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["offices", "genders"], ["ranks", "employment_statuses"], ["positions", "firearm_statuses"], ["marital_statuses", "employees.social_security_number"], ["employees.shield_number", "employees.payroll_id"], ["employees.birthday_from", "employees.birthday_to"], ["employees.ncc_date_from", "employees.ncc_date_to"], ["employees.longevity_date_from", "employees.longevity_date_to"], ["employees.leave_progression_date_from", "employees.leave_progression_date_to"], ["employees.email", "employees.home_phone"], ["employees.cellphone", "employees.work_phone"], ["employees.city", "employees.state"], ["employees.zipcode", ""]], "actions": ["excel_report", "pdf_report"]}, "union_meetings": {"primary_filters": [["meeting_types"], ["reports.started_at", "reports.ended_at"], ["reports.delegates"], ["reports.meetings"]], "secondary_filters": [["employees"], ["offices", "genders"], ["ranks", "employment_statuses"], ["positions", "firearm_statuses"], ["marital_statuses", "employees.social_security_number"], ["employees.shield_number", "employees.payroll_id"], ["employees.birthday_from", "employees.birthday_to"], ["employees.ncc_date_from", "employees.ncc_date_to"], ["employees.longevity_date_from", "employees.longevity_date_to"], ["employees.leave_progression_date_from", "employees.leave_progression_date_to"], ["employees.email", "employees.home_phone"], ["employees.cellphone", "employees.work_phone"], ["employees.city", "employees.state"], ["employees.zipcode", ""]], "actions": ["excel_report", "pdf_report"]}, "disciplines": {"primary_filters": [["discipline_settings"], ["reports.started_at", "reports.ended_at"], ["reports.filed_olr_from_date", "reports.filed_olr_to_date"], ["reports.step_1_from_date", "reports.step_1_to_date"], ["reports.step_2_from_date", "reports.step_2_to_date"], ["reports.step_3_from_date", "reports.step_3_to_date"], ["reports.arbritration_from_date", "reports.arbritration_to_date"], ["reports.settled_steps", "reports.pending_steps"], ["reports.win", "reports.loss"]], "secondary_filters": [["employees"], ["offices", "genders"], ["ranks", "employment_statuses"], ["positions", "firearm_statuses"], ["marital_statuses", "employees.social_security_number"], ["employees.shield_number", "employees.payroll_id"], ["employees.birthday_from", "employees.birthday_to"], ["employees.ncc_date_from", "employees.ncc_date_to"], ["employees.longevity_date_from", "employees.longevity_date_to"], ["employees.leave_progression_date_from", "employees.leave_progression_date_to"], ["employees.email", "employees.home_phone"], ["employees.cellphone", "employees.work_phone"], ["employees.city", "employees.state"], ["employees.zipcode", ""]], "report_columns": ["charge", "dan_number"], "actions": ["excel_report", "pdf_report"], "steps": ["step_1", "step_2", "step_3", "arbritration"]}, "grievances": {"primary_filters": [["grievances"], ["reports.started_at", "reports.ended_at"], ["reports.filed_olr_from_date", "reports.filed_olr_to_date"], ["reports.step_1_from_date", "reports.step_1_to_date"], ["reports.step_2_from_date", "reports.step_2_to_date"], ["reports.step_3_from_date", "reports.step_3_to_date"], ["reports.arbritration_from_date", "reports.arbritration_to_date"], ["reports.settled_steps", "reports.pending_steps"], ["reports.win", "reports.loss"]], "secondary_filters": [["employees"], ["offices", "genders"], ["ranks", "employment_statuses"], ["positions", "firearm_statuses"], ["marital_statuses", "employees.social_security_number"], ["employees.shield_number", "employees.payroll_id"], ["employees.birthday_from", "employees.birthday_to"], ["employees.ncc_date_from", "employees.ncc_date_to"], ["employees.longevity_date_from", "employees.longevity_date_to"], ["employees.leave_progression_date_from", "employees.leave_progression_date_to"], ["employees.email", "employees.home_phone"], ["employees.cellphone", "employees.work_phone"], ["employees.city", "employees.state"], ["employees.zipcode", ""]], "report_columns": ["charge", "number"], "actions": ["excel_report", "pdf_report"], "steps": ["step_1", "step_2", "step_3", "arbritration"]}, "janus": {"primary_filters": [["employees.janus_card"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["offices", "genders"], ["ranks", "employment_statuses"], ["positions", "firearm_statuses"], ["marital_statuses", "employees.social_security_number"], ["employees.shield_number", "employees.payroll_id"], ["employees.birthday_from", "employees.birthday_to"], ["employees.ncc_date_from", "employees.ncc_date_to"], ["employees.longevity_date_from", "employees.longevity_date_to"], ["employees.leave_progression_date_from", "employees.leave_progression_date_to"], ["employees.email", "employees.home_phone"], ["employees.cellphone", "employees.work_phone"], ["employees.city", "employees.state"], ["employees.zipcode", ""]], "actions": ["excel_report", "pdf_report"]}, "life_insurances": {"primary_filters": [["reports.age_group_type", ""]], "secondary_filters": [["employees"], ["offices", "genders"], ["ranks", "employment_statuses"], ["positions", "firearm_statuses"], ["marital_statuses", "employees.social_security_number"], ["employees.shield_number", "employees.payroll_id"], ["employees.birthday_from", "employees.birthday_to"], ["employees.ncc_date_from", "employees.ncc_date_to"], ["employees.longevity_date_from", "employees.longevity_date_to"], ["employees.leave_progression_date_from", "employees.leave_progression_date_to"], ["employees.email", "employees.home_phone"], ["employees.cellphone", "employees.work_phone"], ["employees.city", "employees.state"], ["employees.zipcode", ""]], "actions": ["excel_report"]}}, "notification": {"key_name": "Notifications", "analytics": "Analytics", "is_search": true, "allow_sms_attachments": true, "check_confirmation": true, "notification_tracker_count": true, "filters": [["employees"], ["offices", "genders"], ["ranks", "employment_statuses"], ["positions", "firearm_statuses"], ["marital_statuses", "employees.social_security_number"], ["employees.shield_number", "employees.payroll_id"], ["employees.birthday_from", "employees.birthday_to"], ["employees.ncc_date_from", "employees.ncc_date_to"], ["employees.longevity_date_from", "employees.longevity_date_to"], ["employees.leave_progression_date_from", "employees.leave_progression_date_to"], ["employees.email", "employees.home_phone"], ["employees.cellphone", "employees.work_phone"], ["employees.city", "employees.state"], ["employees.zipcode", ""]]}, "user_audit": {"key_name": "User <PERSON>t", "table_headers": ["user_name", "title", "employee_name", "created_at"]}}}