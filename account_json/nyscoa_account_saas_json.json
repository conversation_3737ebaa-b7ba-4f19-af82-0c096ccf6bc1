{"schema": {"employees": {"key_name": "Members", "avatar": "Member Photo", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "do_not_mail": "Do Not Mail", "birthday": "DOB", "age": "Age", "social_security_number": "Social Security Number", "social_security_number_format": "9", "veteran_status": "Veteran Status", "a_number": "A Number", "shield_number": "Shield Number", "placard_number": "Placard Number", "placard_customize": "Placard", "responder_911": "9/11 Re<PERSON>onder", "start_date": "Hire Date", "staff_member": "Nights", "notes": "Notes", "janus_card": "<PERSON><PERSON>t Out", "janus_card_opt_out_date": "<PERSON><PERSON> Opt Out Date", "janus_card_status": "true", "cellphone": "Cell phone", "home_phone": "Home phone", "work_phone": "Work phone", "email": "Email", "allow_multiple_present_status": "false", "placard_multiple": true, "placard_number_years_customize": true, "username": "Username", "app_downloaded": "App Downloaded", "required_fields": ["name", "first_name", "last_name", "address", "city", "state", "street", "zipcode", "birthday", "social_security_number", "gender_id", "marital_status_id"], "unique_fields": ["username"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "shield_number", "placard_number", "street", "start_date", "birthday"], "associated_model": {"offices": ["name"], "ranks": ["name"], "benefit_coverages": ["name"], "beneficiaries": ["name"]}}, "login_credentials": {"key_name": "Login Information", "username": "Username", "send_credentials": "Update & Send Credentials", "enable_mobile_access": "Allow Access to Mobile app", "required_fields": []}}, "change_requests": {"key_name": "Change Requests", "employee_name": "Member", "request_type": "Request Type", "status": "Status"}, "devices": {"key_name": "Push notification"}, "genders": {"key_name": "Genders", "name": "Name", "description": "Description", "required_fields": ["name"]}, "marital_statuses": {"key_name": "Marital Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "contacts": {"contact_number": {"key_name": "Contact Number", "work_phone": "Work", "personal_phone": "Cell", "home_phone": "Home", "required_fields": []}, "email_address": {"key_name": "Email Address", "personal_email": "Personal", "required_fields": []}}, "officer_statuses": {"key_name": "Member Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_officer_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "officer_status_id"]}, "offices": {"key_name": "Commands", "name": "Name", "address": "Address", "phone": "Phone Number", "fax": "Description", "required_fields": ["name", "address"]}, "employee_offices": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["office_id", "employee_id"]}, "delegate_assignments": {"key_name": "Delegate Assignments", "delegate_employee_id": "Delegate Name", "start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["delegate_employee_id", "employee_id"]}, "delegate_employees": {"key_name": "Delegate Name"}, "positions": {"key_name": "Union Positions", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_positions": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["position_id", "employee_id"]}, "ranks": {"key_name": "Ranks", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_ranks": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["rank_id", "employee_id"]}, "firearm_statuses": {"key_name": "Firearm Status", "name": "Name", "firearm_type": "Type", "firearm_test_type": "Test Type", "required_fields": ["name"]}, "employee_firearm_statuses": {"status_date": "Date", "firearm_type": "Type", "notes": "Notes", "customization": true, "required_fields": ["firearm_status_id", "employee_id"], "required_tables": ["firearm_statuses"]}, "leaves": {"started_at": "From", "ended_at": "To", "hours_used": "No. of hours used", "notes": "Notes", "files": "Files", "required_fields": ["started_at", "leave_type", "hours_used"]}, "lodis": {"key_name": "LODI", "incident_date": "Date of incident", "return_date": "Date of return", "office_name": "Location", "notes": "Notes", "files": "Files", "required_fields": ["office_id", "employee_id", "incident_date"]}, "workers_comp": {"key_name": "Workers Comp", "incident_date": "Date of incident", "return_date": "Date of return", "office_name": "Location", "notes": "Notes", "files": "Files", "required_fields": ["office_id", "employee_id", "incident_date"]}, "benefits": {"key_name": "Benefits", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_benefits": {"start_date": "Start Date", "end_date": "End Date", "description": "Description", "required_fields": ["benefit_id", "employee_id"]}, "beneficiaries": {"key_name": "Beneficiaries", "name": "Name", "relationship": "Relationship", "address": "Address", "beneficiary_type": "Type", "percentage": "Percentage", "file": "Upload", "required_fields": ["name", "employee_id", "percentage"]}, "benefit_coverages": {"key_name": "Dependents", "name": "Name", "relationship": "Relationship", "social_security_number": "employees.social_security_number", "address": "Address", "birthday": "DOB", "age": "Age", "expires_at": "Expiration", "files": "Uploads", "order_by_relationship": true, "required_fields": ["employee_id", "address", "birthday", "name", "social_security_number", "relationship"]}, "benefit_disbursements": {"key_name": "Beneficiary Forms", "view_benefit_disbursements": "Benefit Forms", "date": "<PERSON>an Date", "benefit_coverage_id": "Patient/Dependent Name", "additional_coverage_option": ["member"], "notes": "Notes", "file": "Upload", "order_by_date": true, "required_fields": ["employee_id", "payment_type_id", "date"]}, "dependents": {"key_name": "Dependent", "name": "Name", "relationship": "Relationship", "amount": "Amount", "spouse_contribution": "Spouse Contribution", "relationship_amount_validation": {"basic": {"spouse": [15000], "child": [5000]}, "supplemental": {"spouse": [25000, 50000, 100000]}}, "relationship_value": [{"value": "Spouse", "key": "spouse"}, {"value": "Child", "key": "child"}], "address": "Address", "date": "DOB", "age": "Age", "required_fields": ["life_insurance_id", "employee_id", "name", "relationship", "amount"], "spouse_contribution_value": {"amount": {"25000": 1.82, "50000": 3.63, "100000": 7.26, "1000000": 999.99}}, "dependents_premium": {"key_name": "Premium", "spouse_contribution": "Spouse Contribution", "total_contribution": "Total Contribution"}}, "awards": {"key_name": "Awards", "name": "Type", "awarded_on": "Date", "description": "Description", "required_fields": ["employee_id", "name"]}, "discipline_settings": {"key_name": "Disciplines", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_discipline_settings": {"discipline_setting_id": "Infraction", "charge": "Charge", "dan_number": "Case Number", "date": "Date", "filed_olr": "UOR/Aided", "description": "Remarks", "files": "Files", "required_fields": ["employee_id", "discipline_setting_id"]}, "employee_discipline_steps": {"date": "Date", "recommended_notes": "Remarks", "is_settled": "Settled", "is_pending": "Pending", "win": "Win", "loss": "Loss", "files": "Uploads", "required_fields": ["step", "employee_discipline_setting_id"]}, "grievances": {"key_name": "Grievances", "name": "Name", "description": "Description", "required_fields": ["name"]}, "grievance_statuses": {"key_name": "Grievance Statuses", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_grievances": {"key_name": "Grievances", "grievance_id": "Infraction", "charge": "Charge", "number": "Case Number", "date": "Date", "filed_olr": "UOR/Aided", "description": "Remarks", "files": "Uploads", "required_fields": ["grievance_id"]}, "employee_grievance_steps": {"date": "Date", "grievance_status_id": "Status", "recommended_notes": "Remarks", "is_settled": "Settled", "is_pending": "Pending", "win": "Win", "loss": "Loss", "files": "Uploads", "required_fields": ["step", "employee_grievance_id"]}, "meeting_types": {"key_name": "Meeting Types", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_meeting_types": {"meeting_date": "Date", "attended": "Attended", "notes": "Notes", "required_fields": ["meeting_date", "meeting_type_id", "employee_id"]}, "uploads": {"key_name": "Uploads", "file": "File", "notes": "Notes", "required_fields": ["file", "employee_id"]}, "users": {"key_name": "Users", "username": "Username", "email": "Email Address", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "password": "Password", "password_confirmation": "Password Confirmation", "user_audit_logging": "User <PERSON>t Logging", "required_fields": ["username", "email", "password", "password_confirmation", "role_id"]}, "roles": {"key_name": "Roles"}, "payment_types": {"key_name": "Document Type", "name": "Name", "description": "Description", "required_fields": ["name"]}, "reports": {"single_employee": "Single Member", "sick_bank": "Sick Bank", "lodi": "<PERSON><PERSON>", "workers_comp": "Workers Comp", "benefit_coverages": "Benefit Dependents", "benefit_coverage_name": "Dependent Name", "relationship": "Relationship", "benefit_coverage_ssn": "Dependent SSN#", "benefit_coverage_birthday": "Birthday", "janus": "<PERSON><PERSON>", "union_meetings": "Union Meetings", "employee_delegate_assignment": "Member Delegate Assignment", "columns": "Report Columns", "birthday_from_date": "Birthday From Date", "birthday_to_date": "Birthday To Date", "expiration_from_date": "Expiration From Date", "expiration_to_date": "Expiration To Date", "started_at": "From Date", "ended_at": "To Date", "employee_summary": "Member Summary", "total_summary": "Total Summary", "active_date": "Active Date", "inactive_date": "Inactive Date", "disbursement_date": "Disbursement Date", "disbursement_year": "Disbursement Year", "disbursements_not_found": "Benefit disbursements not found.", "name": "Name", "date_in": "Date In", "date_out": "Date Out", "used_hours": "Used Hours", "lodi_return_to_work_status": "Return to Work Status", "date_to": "Date To", "date_from": "Date From", "excel_report": "Generate Excel Report", "pdf_report": "Generate PDF Report", "single_mailing_label": "Generate Single Mailing Labels", "multiple_mailing_label": "Generate Multiple Mailing Labels", "payment_date_from": "Payment Date From", "payment_date_to": "Payment Date To", "show_disbursements": "Show Disbursements", "show_coverages": "Show Dependents", "delegates": "Delegates", "meetings": "Meetings", "dependent_count": "Dependent Count", "settled_steps": "Settled Steps", "pending_steps": "Pending Steps", "filed_olr_from_date": "OLR From Date", "filed_olr_to_date": "OLR To Date", "step_1_from_date": "Step I From Date", "step_1_to_date": "Step I To Date", "step_2_from_date": "Step II From Date", "step_2_to_date": "Step II To Date", "step_3_from_date": "Step III From Date", "step_3_to_date": "Step III To Date", "arbritration_from_date": "Arbritration From Date", "arbritration_to_date": "Arbritration To Date", "win": "Win", "loss": "Loss", "app_downloaded": "App Downloaded", "step_values": [{"value": "Step I", "key": "step_1"}, {"value": "Step II", "key": "step_2"}, {"value": "Step III", "key": "step_3"}, {"value": "Arbritration", "key": "arbritration"}], "benefit_coverages_options": [{"value": "All members with/without dependents", "key": "all"}, {"value": "Members with dependents", "key": "true"}, {"value": "Members without dependents", "key": "false"}]}, "common_terms": {"step_1": "STEP I", "step_2": "STEP II", "step_3": "STEP III", "arbritration": "ARBRITRATION", "none": "None", "basic": "Basic", "premium": "Premium", "supplemental": "Supplemental", "life_insurances": "Life Insurances", "dependents": "Dependents", "lodis": "<PERSON><PERSON>", "workers_comp": "Workers Comp", "medscope": "Medscope", "hearing": "Hearing 207c", "arbitration": "Arbitration", "employee_analytics": "Analytics", "sick_bank": "Sick Bank"}, "employee_analytics": {"key_name": "Analytics", "customization": true, "dashboard_stats": true, "required_tables": ["sick_bank", "lodis", "workers_comp"]}, "notifications": {"key_name": "Notifications", "subject": "Subject", "sms_message": "Sms Message", "email_message": "Email Message", "sms": "false", "email": "false", "filter": "Filter", "push": "false", "push_message": "Push Message", "change_request_notification": true}, "user_audit": {"key_name": "User <PERSON>t", "user_name": "User Name", "title": "Title", "employee_name": "Employee Name", "created_at": "Created At"}}, "ui": {"employees": {"key_name": "Member List", "is_search": ["employees"], "is_filter": ["officer_status", "placard_number", "ignore_employment_status_filter"], "table_headers": ["name", "officer_status_name", "shield_number", "placard_number", "office_name", "rank_name", "address"], "tabs": ["profile", "firearm_statuses", "employee_analytics", "benefits", "awards", "discipline_settings", "grievances", "meeting_types", "uploads"], "profile": {"key_name": "Profile", "avatar_expand_view": true, "only_two_placards": true, "additional_details": ["first_name", "middle_name", "last_name", "officer_status_name"], "employees": ["avatar", "name", "address", "do_not_mail", "birthday", "age", "social_security_number", "genders", "marital_statuses", "veteran_status", "a_number", "shield_number", "placard_number", "start_date", "staff_member", "responder_911", "notes"], "contacts": [{"contact_number": ["work_phone", "personal_phone", "home_phone"]}, {"email_address": ["personal_email"]}], "others": ["employee_officer_statuses", "employee_offices", "delegate_assignments", "employee_positions", "employee_ranks", "employees.janus_card", "employees.janus_card_opt_out_date", "employees.app_downloaded"], "login_credentials": ["username", "send_credentials", "enable_mobile_access"]}, "discipline_settings": {"tabs": ["discipline_settings", "step_1", "step_2", "step_3", "arbritration"], "table_headers": ["dan_number", "discipline_setting", "charge", "date"]}, "benefits": {"isDetailsView": true}, "grievances": {"tabs": ["grievances", "step_1", "step_2", "step_3", "arbritration"], "table_headers": ["grievance", "charge", "number", "date"]}, "life_insurances": {"table_headers": ["insurance_type", "amount", "start_date", "notes"], "insurance_type": ["basic", "premium", "supplemental", "none"], "insurance_amount": [50000, 100000, 200000], "tabs": ["life_insurances", "premium"]}, "dependents": {"tabs": ["dependents", "premium"]}}, "employee_grievances": {"key_name": "Grievances", "table_headers": ["charge", "number", "date", "filed_olr", "description", "files"]}, "settings": {"key_name": "Settings", "tabs": ["benefits", "offices", "firearm_statuses", "ranks", "officer_statuses", "marital_statuses", "positions", "payment_types", "discipline_settings", "grievances", "grievance_statuses", "meeting_types", "genders"]}, "users": {"show_password_icon": false, "is_search": ["users"], "table_headers": ["username", "name", "email", "roles"]}, "reports": {"key_name": "Reports", "tabs": ["single_employee", "disciplines", "grievances", "benefits", "benefit_coverages", "lodi", "workers_comp", "union_meetings", "janus"], "single_employee": {"primary_filters": [["reports.columns"], ["employees"]], "secondary_filters": [["employees.placard_number"], ["offices", "genders"], ["ranks", "employees.shield_number"], ["officer_statuses", "employees.email"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.age_from", "employees.age_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["employees.responder_911", "employees.staff_member"], ["employees.app_downloaded", ""]], "columns": ["employees", "employees.first_name", "employees.middle_name", "employees.last_name", "offices", "ranks", "officer_statuses", "employees.email", "firearm_statuses", "marital_statuses", "positions", "employees.social_security_number", "employees.placard_customize", "employees.shield_number", "employees.birthday", "employees.home_phone", "employees.cellphone", "employees.work_phone", "employees.a_number", "employees.city", "employees.state", "employees.zipcode", "employees.staff_member", "employees.start_date", "employees.apartment", "employees.street", "genders", "reports.dependent_count", "employees.app_downloaded"], "default_columns": ["employees", "employees.shield_number", "ranks", "offices", "firearm_statuses", "employees.staff_member"], "actions": ["mailing_label", "multiple_mailing_label", "excel_report", "pdf_report"]}, "disciplines": {"primary_filters": [["discipline_settings"], ["reports.started_at", "reports.ended_at"], ["reports.filed_olr_from_date", "reports.filed_olr_to_date"], ["reports.step_1_from_date", "reports.step_1_to_date"], ["reports.step_2_from_date", "reports.step_2_to_date"], ["reports.step_3_from_date", "reports.step_3_to_date"], ["reports.arbritration_from_date", "reports.arbritration_to_date"], ["reports.settled_steps", "reports.pending_steps"], ["reports.win", "reports.loss"]], "secondary_filters": [["employees"], ["employees.placard_number"], ["offices", "ranks"], ["employees.shield_number", "officer_statuses"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.email", "employees.home_phone"], ["employees.cellphone", "employees.work_phone"], ["employees.city", "employees.state"], ["employees.zipcode", "employees.staff_member"], ["employees.app_downloaded", ""]], "actions": ["excel_report", "pdf_report"]}, "grievances": {"primary_filters": [["grievances"], ["reports.started_at", "reports.ended_at"], ["reports.filed_olr_from_date", "reports.filed_olr_to_date"], ["reports.step_1_from_date", "reports.step_1_to_date"], ["reports.step_2_from_date", "reports.step_2_to_date"], ["reports.step_3_from_date", "reports.step_3_to_date"], ["reports.arbritration_from_date", "reports.arbritration_to_date"], ["reports.settled_steps", "reports.pending_steps"], ["reports.win", "reports.loss"]], "secondary_filters": [["employees"], ["employees.placard_number"], ["offices", "ranks"], ["employees.shield_number", "officer_statuses"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.email", "employees.home_phone"], ["employees.cellphone", "employees.work_phone"], ["employees.city", "employees.state"], ["employees.zipcode", "employees.staff_member"], ["employees.app_downloaded", ""]], "actions": ["excel_report", "pdf_report"]}, "benefits": {"primary_filters": [["benefits"], ["reports.active_date", "reports.inactive_date"], ["payment_types", "reports.disbursement_year"], ["reports.payment_date_from", "reports.payment_date_to"], ["reports.show_disbursements", ""]], "secondary_filters": [["employees"], ["employees.placard_number"], ["offices", "ranks"], ["employees.shield_number", "officer_statuses"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.email", "employees.home_phone"], ["employees.cellphone", "employees.work_phone"], ["employees.staff_member", "employees.app_downloaded"]], "actions": ["pdf_report"]}, "benefit_coverages": {"primary_filters": [["employees"], ["benefits"], ["reports.show_coverages"]], "secondary_filters": [["employees.placard_number"], ["offices", "ranks"], ["employees.shield_number", "officer_statuses"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.email", "employees.home_phone"], ["employees.cellphone", "employees.work_phone"], ["employees.staff_member", "employees.app_downloaded"]], "actions": ["excel_report", "pdf_report"]}, "lodi": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["employees.placard_number"], ["offices", "ranks"], ["employees.shield_number", "officer_statuses"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.email", "employees.home_phone"], ["employees.cellphone", "employees.work_phone"], ["employees.staff_member", "employees.app_downloaded"]], "actions": ["excel_report", "pdf_report"]}, "workers_comp": {"primary_filters": [["workers_comp"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["employees.placard_number"], ["offices", "ranks"], ["employees.shield_number", "officer_statuses"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.email", "employees.home_phone"], ["employees.cellphone", "employees.work_phone"], ["employees.staff_member", "employees.app_downloaded"]], "actions": ["excel_report", "pdf_report"]}, "union_meetings": {"primary_filters": [["meeting_types"], ["reports.started_at", "reports.ended_at"], ["reports.delegates"], ["reports.meetings"]], "secondary_filters": [["employees"], ["employees.placard_number"], ["offices", "ranks"], ["employees.shield_number", "officer_statuses"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.email", "employees.home_phone"], ["employees.cellphone", "employees.work_phone"], ["employees.staff_member", "employees.app_downloaded"]], "actions": ["excel_report", "pdf_report"]}, "janus": {"primary_filters": [["employees.janus_card"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["employees.placard_number"], ["offices", "ranks"], ["employees.shield_number", "officer_statuses"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.email", "employees.home_phone"], ["employees.cellphone", "employees.work_phone"], ["employees.staff_member", "employees.app_downloaded"]], "actions": ["excel_report", "pdf_report"]}}, "notification": {"key_name": "Notifications", "analytics": "Analytics", "is_search": true, "allow_sms_attachments": true, "check_confirmation": true, "notification_tracker_count": true, "filters": [["employees"], ["employees.placard_number"], ["offices", "ranks"], ["officer_statuses", "employees.shield_number"], ["firearm_statuses", "positions"], ["marital_statuses", "employees.a_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.city", "employees.state"], ["employees.zipcode", "employees.staff_member"], ["employees.app_downloaded", ""]]}, "change_requests": {"key_name": "Change Requests", "employee": "General Info", "contact": "Contact Info", "request_header": "Request Header", "current_value": "Current Status", "requested_changes": "Requested Changes", "status": "Status", "action": "Action", "table_headers": ["request_header", "current_value", "requested_changes", "status", "action"]}, "user_audit": {"key_name": "User <PERSON>t", "table_headers": ["user_name", "title", "employee_name", "created_at"]}}, "mobile": {"employees": {"key_name": "Profile", "tabs": ["profile", "contacts", "employee_offices", "employee_ranks"], "profile": {"key_name": "General Info", "widget_type": "Info", "api": "employees/profile", "actions": ["view", "edit"], "request_type": "employee", "attributes": {"first_name": {"type": "SingleLineText", "name": "First Name", "required": true, "actions": ["view", "edit"]}, "middle_name": {"type": "SingleLineText", "name": "Middle Name", "required": true, "actions": ["view", "edit"]}, "last_name": {"type": "SingleLineText", "name": "Last Name", "required": true, "actions": ["view", "edit"]}, "street": {"type": "SingleLineText", "name": "Address", "required": true, "actions": ["view", "edit"]}, "apartment": {"type": "SingleLineText", "name": "Apartment", "actions": ["view", "edit"]}, "city": {"type": "SingleLineText", "name": "City", "required": true, "actions": ["view", "edit"]}, "state": {"type": "SingleLineText", "name": "State", "required": true, "actions": ["view", "edit"]}, "zipcode": {"type": "ZipCode", "name": "ZipCode", "required": true, "actions": ["view", "edit"]}, "birthday": {"type": "Date", "name": "Date of Birth", "required": true, "actions": ["view", "edit"]}, "age": {"type": "Label", "name": "Age", "actions": ["view"]}, "marital_status_name": {"type": "DropDown", "name": "Marital Status", "api": "marital_statuses", "api_key": "marital_status_id", "actions": ["view", "edit"]}, "shield_number": {"type": "SingleLineText", "name": "Shield Number", "actions": ["view", "edit"]}, "start_date": {"type": "Date", "name": "Hire Date", "actions": ["view", "edit"]}}}, "contacts": {"key_name": "Contact Info", "request_type": "contact", "widget_type": "Info", "api": "contacts?employee_id=[EMPLOYEE_ID]", "actions": ["view", "edit"], "sub_sections": ["contact_number", "email_address"], "contact_number": {"key_name": "Contact Number", "api": "contacts?employee_id=[EMPLOYEE_ID]&contact_type=phone", "attributes": {"work_phone": {"type": "PhoneNumber", "name": "Work", "contact_for": "work", "contact_type": "phone", "contact_attribute": "value", "actions": ["view", "edit"]}, "home_phone": {"type": "PhoneNumber", "name": "Home", "contact_for": "home", "contact_type": "phone", "contact_attribute": "value", "actions": ["view", "edit"]}, "personal_phone": {"type": "PhoneNumber", "name": "Cell", "contact_for": "personal", "contact_type": "phone", "contact_attribute": "value", "actions": ["view", "edit"]}}}, "email_address": {"key_name": "Email", "api": "contacts?employee_id=[EMPLOYEE_ID]&contact_type=email", "attributes": {"personal_email": {"type": "Email", "name": "Personal", "contact_for": "personal", "contact_type": "email", "contact_attribute": "value", "actions": ["view", "edit"]}}}}, "employee_offices": {"key_name": "Commands", "request_type": "employee_office", "empty_message": "No Data Available", "actions": ["view", "new", "edit"], "api": "employee_offices?employee_id=[EMPLOYEE_ID]", "widget_type": "Status", "attributes": {"office_name": {"type": "DropDown", "name": "Command", "api": "offices", "api_key": "office_id", "required": true, "actions": ["view", "edit"]}, "start_date": {"type": "Date", "name": "Start Date", "required": true, "actions": ["view", "edit"]}, "end_date": {"type": "Date", "name": "End Date", "actions": ["view", "edit"]}}}, "employee_ranks": {"key_name": "Ranks", "request_type": "employee_rank", "empty_message": "No Data Available", "actions": ["view", "new", "edit"], "api": "employee_ranks?employee_id=[EMPLOYEE_ID]", "widget_type": "Status", "attributes": {"rank_name": {"type": "DropDown", "name": "Ranks", "api": "ranks", "api_key": "rank_id", "required": true, "actions": ["view", "edit"]}, "start_date": {"type": "Date", "name": "Start Date", "required": true, "actions": ["view", "edit"]}, "end_date": {"type": "Date", "name": "End Date", "actions": ["view", "edit"]}, "notes": {"type": "MultiLineText", "name": "Notes", "actions": ["view", "edit"]}}}}, "benefits": {"key_name": "Benefits", "tabs": ["employee_benefits", "benefit_coverages"], "employee_benefits": {"key_name": "Benefits", "request_type": "employee_benefit", "empty_message": "There are no Benefits posted.", "actions": ["view"], "api": "employee_benefits?employee_id=[EMPLOYEE_ID]", "widget_type": "Action", "attributes": {"benefit_name": {"type": "DropDown", "name": "Type", "api": "benefits", "api_key": "benefit_id", "actions": ["view"]}}}, "benefit_coverages": {"key_name": "Dependents", "request_type": "benefit_coverage", "required_key": "employee_benefit_id", "empty_message": "There are no Dependents.", "hint": {"caption": "PLEASE NOTE *", "caption_color": "#d93025", "text": "For a change in Dependents to be valid and finalized please upload a Marriage Certificate, Birth Certificate and Social Security Card for the dependent."}, "actions": ["view", "new", "edit"], "api": "benefit_coverages?employee_id=[EMPLOYEE_ID]", "widget_type": "Analysis", "attributes": {"name": {"type": "SingleLineText", "name": "Name", "required": true, "actions": ["view", "edit"]}, "relationship": {"type": "SingleLineText", "name": "Relationship", "required": true, "actions": ["view", "edit"]}, "address": {"type": "SingleLineText", "name": "Address", "required": true, "actions": ["view", "edit"]}, "birthday": {"type": "Date", "name": "DOB", "required": true, "actions": ["view", "edit"]}, "files": {"type": "FileField", "name": "Uploads", "selection_type": "multiple", "max_file_size": 10, "total_files": null, "actions": ["view", "edit"]}}}}, "notification": {"key_name": "Notifications", "empty_message": "There are no Notifications.", "api": "push_notification_index"}, "contact_us": {"key_name": "Contact Us", "to": "<EMAIL>", "subject": "NYSCOA: Feedback/Suggestion", "default_message": null}, "update_password": {"key_name": "Update Password", "api": "employees/update_password"}}}