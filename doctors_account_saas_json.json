{"schema": {"employees": {"key_name": "Doctors", "avatar": "Doctor Photo", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "do_not_mail": "Do Not Email", "birthday": "DOB", "a_number": "Doctor Identity Number", "veteran_status": "Veteran Status", "shield_number": "Registration Number", "placard_number": "Salary Account Number", "start_date": "Job Start Date", "notes": "Notes", "cellphone": "Personal phone", "home_phone": "Home phone", "email": "Email", "allow_multiple_present_status": "false", "required_fields": ["name", "first_name", "last_name", "address", "city", "state", "street", "zipcode", "shield_number"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "shield_number", "a_number", "street", "start_date", "birthday"]}}, "contacts": {"contact_number": {"key_name": "Contact Number", "personal_phone": "Personal", "home_phone": "Home", "required_fields": []}, "email_address": {"key_name": "Email Address", "work_email": "Work", "personal_email": "Personal", "required_fields": []}}, "departments": {"key_name": "Hospital Names", "name": "Name", "address": "Address", "phone": "Phone Number", "notes": "Notes", "required_fields": ["name"]}, "sections": {"key_name": "Specialists", "name": "Name", "phone": "Phone Number", "notes": "Notes", "required_fields": ["name", "department_id"]}, "discipline_settings": {"key_name": "Operation Types", "name": "Name", "description": "Description", "required_fields": ["name"]}, "awards": {"key_name": "Awards", "name": "Type", "awarded_on": "Date", "description": "Description", "required_fields": ["employee_id", "name"]}, "grievances": {"key_name": "Grievances", "name": "Name", "description": "Description", "required_fields": ["name"]}, "meeting_types": {"key_name": "Unions", "name": "Name", "description": "Description", "required_fields": ["name"]}, "uploads": {"key_name": "Uploads", "file": "File", "notes": "Notes", "required_fields": ["file", "employee_id"]}, "leaves": {"started_at": "From", "ended_at": "To", "hours_used": "No. of hours used", "notes": "Notes", "required_fields": ["started_at", "leave_type", "hours_used"]}, "lodis": {"key_name": "<PERSON><PERSON>", "incident_date": "Date of incident", "return_date": "Date of return", "office_name": "Location", "notes": "Notes", "required_fields": ["office_id", "employee_id", "incident_date"]}, "genders": {"key_name": "Genders", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employment_statuses": {"key_name": "Employee Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "marital_statuses": {"key_name": "Marital Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_departments": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["department_id", "employee_id"]}, "employee_sections": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["section_id", "department_id", "employee_id"]}, "employee_employment_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "employment_status_id"]}, "employee_meeting_types": {"meeting_date": "Date", "attended": "Attended", "notes": "Notes", "required_fields": ["meeting_date", "meeting_type_id", "employee_id"]}, "employee_discipline_settings": {"discipline_setting_id": "Type", "date": "Date", "description": "Description", "files": "Files", "required_fields": ["employee_id", "discipline_setting_id", "date"]}, "employee_grievances": {"date": "Date", "description": "Description", "files": "Files", "required_fields": ["employee_id", "grievance_id", "date"]}, "users": {"key_name": "<PERSON>", "username": "Username", "email": "Email Address", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "password": "Password", "password_confirmation": "Password Confirmation", "required_fields": ["username", "email", "password", "password_confirmation", "role_id"]}, "roles": {"key_name": "Roles"}, "reports": {"single_employee": "Single Doctor Report", "sick_bank": "Sick Bank", "lodi": "<PERSON><PERSON>", "columns": "Report Columns", "started_at": "From Date", "ended_at": "To Date", "name": "Name", "date_from": "Date from", "date_to": "Date to", "excel_report": "Generate Excel Report", "pdf_report": "Generate PDF Report", "single_mailing_label": "Generate Single Mailing Labels", "multiple_mailing_label": "Generate Multiple Mailing Labels", "delegates": "Delegates", "meetings": "Meetings"}, "common_terms": {"employee_analytics": "Leaves", "sick_bank": "Sick Banks", "personal": "Personal", "vacation": "Vacations", "over_time": "Overtime", "comp_time": "Comp Time"}}, "ui": {"employees": {"key_name": "Doctor List", "is_search": ["employees"], "table_headers": ["name", "a_number", "shield_number", "birthday", "start_date", "address"], "tabs": ["profile", "employee_analytics", "awards", "grievances", "discipline_settings", "meeting_types", "uploads"], "profile": {"key_name": "Profile", "additional_details": ["first_name", "middle_name", "last_name"], "employees": ["avatar", "name", "address", "birthday", "do_not_mail", "a_number", "genders", "marital_statuses", "shield_number", "placard_number", "start_date", "notes"], "contacts": [{"contact_number": ["personal_phone", "home_phone"]}, {"email_address": ["personal_email", "work_email"]}], "others": ["employee_employment_statuses", "employee_departments", "employee_sections"]}}, "settings": {"key_name": "Settings", "tabs": ["departments", "sections", "employment_statuses", "marital_statuses", "grievances", "discipline_settings", "genders", "meeting_types"]}, "users": {"is_search": ["users"], "table_headers": ["username", "name", "email", "roles"]}, "reports": {"key_name": "Reports", "tabs": ["single_employee", "sick_bank", "lodi", "grievances", "disciplines"], "single_employee": {"primary_filters": [["reports.columns"], ["employees"]], "secondary_filters": [["sections"], ["employees.placard_number", "employees.shield_number"], ["marital_statuses"], ["employees.birthday_from", "employees.birthday_to"], ["grievances", "meeting_types"], ["employees.start_date_from", "employees.start_date_to"], ["employment_statuses", "departments"], ["employees.home_phone", "employees.cellphone"], ["employees.email", "employees.a_number"]], "columns": ["employees", "employees.placard_number", "employees.address", "employees.birthday", "genders", "marital_statuses", "employees.start_date", "employment_statuses", "departments", "sections", "meeting_types", "grievances"], "default_columns": ["employees", "employment_statuses", "marital_statuses", "employees.birthday", "departments", "sections"], "actions": ["single_mailing_label", "multiple_mailing_label", "pdf_report"]}, "sick_bank": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees.placard_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["marital_statuses", "sections"], ["employees.start_date_from", "employees.start_date_to"], ["employment_statuses"], ["departments"], ["employees.home_phone", "employees.cellphone"], ["employees.email", "employees.a_number"]], "actions": ["excel_report"]}, "lodi": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["sections", "marital_statuses"], ["employees.birthday_from", "employees.birthday_to"], ["employees.placard_number", "employees.shield_number"], ["employees.start_date_from", "employees.start_date_to"], ["employment_statuses"], ["departments"], ["employees.home_phone", "employees.cellphone"], ["employees.email", "employees.a_number"]], "actions": ["excel_report"]}, "grievances": {"primary_filters": [["grievances"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["sections"], ["marital_statuses"], ["employees.start_date_from", "employees.start_date_to"], ["employees.birthday_from", "employees.birthday_to"], ["employment_statuses", "departments"], ["employees.email", "employees.a_number"], ["employees.placard_number", "employees.shield_number"], ["employees.home_phone", "employees.cellphone"]], "actions": ["excel_report", "pdf_report"]}, "disciplines": {"primary_filters": [["discipline_settings"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees.placard_number", "employees.shield_number"], ["sections", "marital_statuses"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employment_statuses", "departments"], ["employees.email", "employees.a_number"], ["employees.home_phone", "employees.cellphone"]], "actions": ["pdf_report"]}}}}