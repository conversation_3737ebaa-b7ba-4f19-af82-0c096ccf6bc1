GIT
  remote: https://github.com/influitive/apartment.git
  revision: f266f73e58835f94e4ec7c16f28443fe5eada1ac
  branch: development
  specs:
    apartment (2.2.1)
      activerecord (>= 3.1.2, < 6.1)
      parallel (>= 0.7.1)
      public_suffix (>= 2)
      rack (>= 1.3.6)

PATH
  remote: vendor/bundle/prawn-labels
  specs:
    prawn-labels (1.2.4)
      prawn (>= 1.0.0, < 3.0.0)

GEM
  remote: https://rubygems.org/
  specs:
    actioncable (*******)
      actionpack (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      mail (~> 2.5, >= 2.5.4)
      rails-dom-testing (~> 2.0)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      rack (~> 2.0, >= 2.0.8)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
    activerecord-import (1.5.1)
      activerecord (>= 4.2)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      marcel (~> 0.3.1)
    activestorage-validator (0.1.3)
      rails (>= 5.2.0)
    activesupport (*******)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 0.7, < 2)
      minitest (~> 5.1)
      tzinfo (~> 1.1)
      zeitwerk (~> 2.2)
    addressable (2.7.0)
      public_suffix (>= 2.0.2, < 5.0)
    airbrussh (1.4.0)
      sshkit (>= 1.6.1, != 1.7.0)
    appsignal (3.3.11)
      rack
    ast (2.4.0)
    awesome_print (1.8.0)
    aws-eventstream (1.1.0)
    aws-partitions (1.322.0)
    aws-sdk-core (3.97.0)
      aws-eventstream (~> 1, >= 1.0.2)
      aws-partitions (~> 1, >= 1.239.0)
      aws-sigv4 (~> 1.1)
      jmespath (~> 1.0)
    aws-sdk-kms (1.32.0)
      aws-sdk-core (~> 3, >= 3.71.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-s3 (1.67.0)
      aws-sdk-core (~> 3, >= 3.96.1)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.1)
    aws-sdk-ses (1.30.0)
      aws-sdk-core (~> 3, >= 3.71.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-sns (1.24.0)
      aws-sdk-core (~> 3, >= 3.71.0)
      aws-sigv4 (~> 1.1)
    aws-sigv4 (1.1.4)
      aws-eventstream (~> 1.0, >= 1.0.2)
    axiom-types (0.1.1)
      descendants_tracker (~> 0.0.4)
      ice_nine (~> 0.11.0)
      thread_safe (~> 0.3, >= 0.3.1)
    bcrypt (3.1.13)
    better_errors (2.5.1)
      coderay (>= 1.0.0)
      erubi (>= 1.0.0)
      rack (>= 0.9.0)
    bindex (0.8.1)
    binding_of_caller (0.8.0)
      debug_inspector (>= 0.0.1)
    bootsnap (1.4.5)
      msgpack (~> 1.0)
    brakeman (4.7.2)
    builder (3.2.4)
    bullet (6.1.0)
      activesupport (>= 3.0.0)
      uniform_notifier (~> 1.11)
    bundle-audit (0.1.0)
      bundler-audit
    bundler-audit (0.9.1)
      bundler (>= 1.2.0, < 3)
      thor (~> 1.0)
    byebug (11.0.1)
    cancancan (3.0.2)
    capistrano (3.11.2)
      airbrussh (>= 1.0.0)
      i18n
      rake (>= 10.0.0)
      sshkit (>= 1.9.0)
    capistrano-bundler (1.6.0)
      capistrano (~> 3.1)
    capistrano-ext (1.2.1)
      capistrano (>= 1.0.0)
    capistrano-rails (1.4.0)
      capistrano (~> 3.1)
      capistrano-bundler (~> 1.1)
    capybara (3.30.0)
      addressable
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.8)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (~> 1.5)
      xpath (~> 3.2)
    capybara-screenshot (1.0.24)
      capybara (>= 1.0, < 4)
      launchy
    childprocess (3.0.0)
    choice (0.2.0)
    cliver (0.3.2)
    codeclimate-engine-rb (0.4.1)
      virtus (~> 1.0)
    coderay (1.1.2)
    coercible (1.0.0)
      descendants_tracker (~> 0.0.1)
    colorize (0.8.1)
    concurrent-ruby (1.1.5)
    connection_pool (2.2.2)
    coveralls (0.8.23)
      json (>= 1.8, < 3)
      simplecov (~> 0.16.1)
      term-ansicolor (~> 1.3)
      thor (>= 0.19.4, < 2.0)
      tins (~> 1.6)
    crack (0.4.3)
      safe_yaml (~> 1.0.0)
    crass (1.0.5)
    database_cleaner (1.7.0)
    debug_inspector (0.0.3)
    descendants_tracker (0.0.4)
      thread_safe (~> 0.3, >= 0.3.1)
    devise (4.7.1)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    diff-lcs (1.3)
    discard (1.1.0)
      activerecord (>= 4.2, < 7)
    docile (1.3.2)
    doorkeeper (5.2.6)
      railties (>= 5)
    doorkeeper-jwt (0.4.0)
      jwt (~> 2.1)
    dotenv (2.7.5)
    dotenv-rails (2.7.5)
      dotenv (= 2.7.5)
      railties (>= 3.2, < 6.1)
    equalizer (0.0.11)
    erubi (1.9.0)
    et-orbi (1.2.4)
      tzinfo
    fabrication (2.21.0)
    faker (2.10.0)
      i18n (>= 1.6, < 1.8)
    faraday (0.17.3)
      multipart-post (>= 1.2, < 3)
    faraday_middleware (0.12.2)
      faraday (>= 0.7.4, < 1.0)
    fast_jsonapi (1.5)
      activesupport (>= 4.2)
    fasterer (0.8.1)
      colorize (~> 0.7)
      ruby_parser (>= 3.14.1)
    ffi (1.11.3)
    flamegraph (0.9.5)
    foreman (0.87.0)
    formatador (0.2.5)
    friendly_id (5.3.0)
      activerecord (>= 4.0.0)
    fugit (1.5.0)
      et-orbi (~> 1.1, >= 1.1.8)
      raabro (~> 1.4)
    globalid (0.4.2)
      activesupport (>= 4.2.0)
    google-protobuf (3.23.4)
    guard (2.16.1)
      formatador (>= 0.2.4)
      listen (>= 2.7, < 4.0)
      lumberjack (>= 1.0.12, < 2.0)
      nenv (~> 0.1)
      notiffany (~> 0.0)
      pry (>= 0.9.12)
      shellany (~> 0.0)
      thor (>= 0.18.1)
    guard-compat (1.2.1)
    guard-rake (1.0.0)
      guard
      rake
    guard-rspec (4.7.3)
      guard (~> 2.1)
      guard-compat (~> 1.1)
      rspec (>= 2.99.0, < 4.0)
    hashdiff (1.0.0)
    highline (2.0.3)
    hirb (0.7.3)
    hiredis (0.6.3)
    hodel_3000_compliant_logger (0.1.1)
    htmlentities (4.3.4)
    i18n (1.7.1)
      concurrent-ruby (~> 1.0)
    i18n-tasks (0.9.29)
      activesupport (>= 4.0.2)
      ast (>= 2.1.0)
      erubi
      highline (>= 2.0.0)
      i18n
      parser (>= *******)
      rails-i18n
      rainbow (>= 2.2.2, < 4.0)
      terminal-table (>= 1.5.1)
    ice_cube (0.16.4)
    ice_nine (0.11.2)
    image_processing (1.10.1)
      mini_magick (>= 4.9.5, < 5)
      ruby-vips (>= 2.0.17, < 3)
    iniparse (1.4.4)
    interception (0.5)
    jaro_winkler (1.5.4)
    jmespath (1.4.0)
    json (2.3.0)
    json-schema (2.8.1)
      addressable (>= 2.4)
    jwt (2.2.1)
    kwalify (0.7.2)
    launchy (2.4.3)
      addressable (~> 2.3)
    letter_opener (1.7.0)
      launchy (~> 2.2)
    libv8 (7.3.492.27.1)
    listen (3.1.5)
      rb-fsevent (~> 0.9, >= 0.9.4)
      rb-inotify (~> 0.9, >= 0.9.7)
      ruby_dep (~> 1.2)
    loofah (2.4.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.5.9)
    lumberjack (1.1.0)
    mail (2.7.1)
      mini_mime (>= 0.1.1)
    marcel (0.3.3)
      mimemagic (~> 0.3.2)
    memory_profiler (0.9.14)
    meta_request (0.7.2)
      rack-contrib (>= 1.1, < 3)
      railties (>= 3.0.0, < 7)
    method_source (0.9.2)
    mimemagic (0.3.10)
      nokogiri (~> 1)
      rake
    mini_magick (4.10.1)
    mini_mime (1.0.2)
    mini_portile2 (2.5.0)
    mini_racer (0.2.8)
      libv8 (>= 6.9.411)
    minitest (5.13.0)
    msgpack (1.3.1)
    multipart-post (2.1.1)
    nenv (0.3.0)
    net-scp (2.0.0)
      net-ssh (>= 2.6.5, < 6.0.0)
    net-sftp (3.0.0)
      net-ssh (>= 5.0.0, < 7.0.0)
    net-ssh (5.2.0)
    nio4r (2.5.4)
    nokogiri (1.11.2)
      mini_portile2 (~> 2.5.0)
      racc (~> 1.4)
    notiffany (0.1.3)
      nenv (~> 0.1)
      shellany (~> 0.0)
    oink (0.10.1)
      activerecord
      hodel_3000_compliant_logger
    orm_adapter (0.5.0)
    overcommit (0.52.1)
      childprocess (>= 0.6.3, < 4)
      iniparse (~> 1.4)
    pagy (3.7.1)
    paper_trail (10.3.1)
      activerecord (>= 4.2)
      request_store (~> 1.1)
    parallel (1.19.1)
    parser (*******)
      ast (~> 2.4.0)
    pdf-core (0.7.0)
    pg (1.2.1)
    pg_query (5.1.0)
      google-protobuf (>= 3.22.3)
    pg_search (2.3.1)
      activerecord (>= 5.2)
      activesupport (>= 5.2)
    pghero (2.8.3)
      activerecord (>= 5)
    plivo (4.8.1)
      faraday (~> 0.9)
      faraday_middleware (~> 0.12.2)
      htmlentities
      jwt
    poltergeist (1.18.1)
      capybara (>= 2.1, < 4)
      cliver (~> 0.3.1)
      websocket-driver (>= 0.2.0)
    prawn (2.2.2)
      pdf-core (~> 0.7.0)
      ttfunk (~> 1.5)
    priscilla (1.0.3)
      colorize (~> 0.7)
      rumoji (~> 0.3)
    pry (0.12.2)
      coderay (~> 1.1.0)
      method_source (~> 0.9.0)
    pry-byebug (3.7.0)
      byebug (~> 11.0)
      pry (~> 0.10)
    pry-doc (1.0.0)
      pry (~> 0.11)
      yard (~> 0.9.11)
    pry-rails (0.3.9)
      pry (>= 0.10.4)
    pry-rescue (1.5.0)
      interception (>= 0.5)
      pry (>= 0.12.0)
    pry-stack_explorer (0.4.9.3)
      binding_of_caller (>= 0.7)
      pry (>= 0.9.11)
    psych (3.1.0)
    public_suffix (4.0.3)
    puma (5.2.0)
      nio4r (~> 2.0)
    raabro (1.4.0)
    racc (1.5.2)
    rack (2.0.8)
    rack-contrib (2.1.0)
      rack (~> 2.0)
    rack-cors (1.1.1)
      rack (>= 2.0.0)
    rack-mini-profiler (1.1.4)
      rack (>= 1.2.0)
    rack-protection (2.0.8.1)
      rack
    rack-test (1.1.0)
      rack (>= 1.0, < 3)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.3.0)
      railties (= *******)
      sprockets-rails (>= 2.0.0)
    rails-controller-testing (1.0.4)
      actionpack (>= 5.0.1.x)
      actionview (>= 5.0.1.x)
      activesupport (>= 5.0.1.x)
    rails-dom-testing (2.0.3)
      activesupport (>= 4.2.0)
      nokogiri (>= 1.6)
    rails-erd (1.6.0)
      activerecord (>= 4.2)
      activesupport (>= 4.2)
      choice (~> 0.2.0)
      ruby-graphviz (~> 1.2)
    rails-html-sanitizer (1.3.0)
      loofah (~> 2.3)
    rails-i18n (6.0.0)
      i18n (>= 0.7, < 2)
      railties (>= 6.0.0, < 7)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      method_source
      rake (>= 0.8.7)
      thor (>= 0.20.3, < 2.0)
    rainbow (3.0.0)
    rake (13.0.3)
    rake-progressbar (0.0.5)
    rb-fsevent (0.10.3)
    rb-inotify (0.10.1)
      ffi (~> 1.0)
    redis (4.1.3)
    reek (5.5.0)
      codeclimate-engine-rb (~> 0.4.0)
      kwalify (~> 0.7.0)
      parser (>= 2.5.0.0, < 2.7, != 2.5.1.1)
      psych (~> 3.1.0)
      rainbow (>= 2.0, < 4.0)
    regexp_parser (1.6.0)
    request_store (1.5.0)
      rack (>= 1.4)
    responders (3.0.0)
      actionpack (>= 5.0)
      railties (>= 5.0)
    rspec (3.9.0)
      rspec-core (~> 3.9.0)
      rspec-expectations (~> 3.9.0)
      rspec-mocks (~> 3.9.0)
    rspec-core (3.9.1)
      rspec-support (~> 3.9.1)
    rspec-expectations (3.9.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.9.0)
    rspec-mocks (3.9.1)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.9.0)
    rspec-rails (3.9.0)
      actionpack (>= 3.0)
      activesupport (>= 3.0)
      railties (>= 3.0)
      rspec-core (~> 3.9.0)
      rspec-expectations (~> 3.9.0)
      rspec-mocks (~> 3.9.0)
      rspec-support (~> 3.9.0)
    rspec-retry (0.6.2)
      rspec-core (> 3.3)
    rspec-support (3.9.2)
    rubocop (0.78.0)
      jaro_winkler (~> 1.5.1)
      parallel (~> 1.10)
      parser (>= 2.6)
      rainbow (>= 2.2.2, < 4.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 1.4.0, < 1.7)
    ruby-graphviz (1.2.4)
    ruby-lint (2.3.1)
      parser (~> 2.2)
      slop (~> 3.4, >= 3.4.7)
    ruby-ole (********)
    ruby-prof (1.1.0)
    ruby-progressbar (1.10.1)
    ruby-vips (2.0.17)
      ffi (~> 1.9)
    ruby_dep (1.5.0)
    ruby_http_client (3.5.5)
    ruby_parser (3.14.1)
      sexp_processor (~> 4.9)
    rubyzip (2.0.0)
    rufus-scheduler (3.7.0)
      fugit (~> 1.1, >= 1.1.6)
    rumoji (0.5.0)
    safe_yaml (1.0.5)
    selenium-webdriver (3.142.7)
      childprocess (>= 0.5, < 4.0)
      rubyzip (>= 1.2.2)
    sendgrid-ruby (6.7.0)
      ruby_http_client (~> 3.4)
    sexp_processor (4.13.0)
    shellany (0.0.1)
    shoulda-kept-respond-with-content-type (1.1.0)
      shoulda-matchers (>= 2.1.0)
    shoulda-matchers (4.1.2)
      activesupport (>= 4.2.0)
    sidekiq (6.0.4)
      connection_pool (>= 2.2.2)
      rack (>= 2.0.0)
      rack-protection (>= 2.0.0)
      redis (>= 4.1.0)
    simplecov (0.16.1)
      docile (~> 1.1)
      json (>= 1.8, < 3)
      simplecov-html (~> 0.10.0)
    simplecov-html (0.10.2)
    slack-notifier (2.3.2)
    slop (3.6.0)
    spreadsheet (1.2.5)
      ruby-ole (>= 1.0)
    spring (2.1.0)
    spring-commands-rspec (1.0.4)
      spring (>= 0.9.1)
    spring-watcher-listen (2.0.1)
      listen (>= 2.7, < 4.0)
      spring (>= 1.2, < 3.0)
    sprockets (4.0.0)
      concurrent-ruby (~> 1.0)
      rack (> 1, < 3)
    sprockets-rails (3.2.1)
      actionpack (>= 4.0)
      activesupport (>= 4.0)
      sprockets (>= 3.0.0)
    sshkit (1.20.0)
      net-scp (>= 1.1.2)
      net-ssh (>= 2.8.0)
    stackprof (0.2.15)
    strong_migrations (0.6.2)
      activerecord (>= 5)
    sync (0.5.0)
    term-ansicolor (1.7.1)
      tins (~> 1.0)
    terminal-table (1.8.0)
      unicode-display_width (~> 1.1, >= 1.1.1)
    thor (1.0.1)
    thread_safe (0.3.6)
    tins (1.23.0)
      sync
    ttfunk (*******)
    tzinfo (1.2.6)
      thread_safe (~> 0.1)
    unicode-display_width (1.6.0)
    uniform_notifier (1.13.0)
    virtus (1.0.5)
      axiom-types (~> 0.1)
      coercible (~> 1.0)
      descendants_tracker (~> 0.0, >= 0.0.3)
      equalizer (~> 0.0, >= 0.0.9)
    warden (1.2.8)
      rack (>= 2.0.6)
    web-console (4.0.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webdrivers (4.2.0)
      nokogiri (~> 1.6)
      rubyzip (>= 1.3.0)
      selenium-webdriver (>= 3.0, < 4.0)
    webmock (3.7.6)
      addressable (>= 2.3.6)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    websocket-driver (0.7.1)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.4)
    wicked_pdf (1.4.0)
      activesupport
    wkhtmltopdf-binary (0.12.5)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    yard (0.9.23)
    zeitwerk (2.2.2)

PLATFORMS
  ruby

DEPENDENCIES
  activerecord-import (~> 1.5)
  activestorage-validator
  apartment!
  appsignal (= 3.3.11)
  awesome_print
  aws-sdk-s3
  aws-sdk-ses
  aws-sdk-sns (~> 1.1)
  better_errors
  bootsnap (>= 1.4.2)
  brakeman
  bullet
  bundle-audit (~> 0.1.0)
  byebug
  cancancan
  capistrano
  capistrano-ext
  capistrano-rails
  capybara
  capybara-screenshot
  coveralls
  database_cleaner
  devise
  discard
  doorkeeper (~> 5.2.1)
  doorkeeper-jwt
  dotenv-rails
  fabrication
  faker
  fast_jsonapi
  fasterer
  flamegraph
  foreman
  friendly_id
  guard-rake
  guard-rspec
  hirb
  hiredis
  i18n-tasks
  ice_cube (~> 0.16.4)
  image_processing
  json-schema
  jwt (~> 2.2, >= 2.2.1)
  letter_opener
  listen (>= 3.0.5, < 3.2)
  memory_profiler
  meta_request
  mini_racer
  net-sftp
  nio4r (= 2.5.4)
  oink
  overcommit
  pagy
  paper_trail
  pg (>= 0.18, < 2.0)
  pg_query (>= 2)
  pg_search
  pghero (~> 2.8, >= 2.8.3)
  plivo (>= 4.3.0)
  poltergeist
  prawn-labels!
  priscilla
  pry
  pry-byebug
  pry-doc
  pry-rails
  pry-rescue
  pry-stack_explorer
  puma (= 5.2.0)
  rack-cors
  rack-mini-profiler
  rails (~> 6.0.2, >= *******)
  rails-controller-testing
  rails-erd
  rainbow
  rake-progressbar
  redis
  reek
  rspec-rails
  rspec-retry
  rubocop
  ruby-lint
  ruby-prof
  rufus-scheduler (~> 3.2)
  selenium-webdriver
  sendgrid-ruby
  shoulda-kept-respond-with-content-type
  shoulda-matchers
  sidekiq
  simplecov
  slack-notifier
  spreadsheet
  spring
  spring-commands-rspec
  spring-watcher-listen (~> 2.0.0)
  stackprof
  strong_migrations
  term-ansicolor
  tzinfo-data
  web-console
  webdrivers
  webmock
  wicked_pdf
  wkhtmltopdf-binary

RUBY VERSION
   ruby 2.6.6p146

BUNDLED WITH
   1.17.3
