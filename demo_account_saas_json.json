{"schema": {"benefits": {"key_name": "Benefits", "name": "Name", "description": "Description", "required_fields": ["name"]}, "notifications": {"key_name": "Notifications", "subject": "Subject", "sms_message": "Sms Message", "email_message": "Email Message", "sms": "false", "email": "false", "filter": "Filter"}, "offices": {"key_name": "Commands", "name": "Name", "address": "Address", "phone": "Phone Number", "fax": "Description", "required_fields": ["name", "address"]}, "firearm_statuses": {"key_name": "Firearm Status", "name": "Name", "firearm_type": "Type", "firearm_test_type": "Test Type", "required_fields": ["name"]}, "ranks": {"key_name": "Ranks", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employment_statuses": {"key_name": "Employment Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "officer_statuses": {"key_name": "Officer Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "marital_statuses": {"key_name": "Marital Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "positions": {"key_name": "Positions", "name": "Name", "description": "Description", "required_fields": ["name"]}, "payment_types": {"key_name": "Payment Types", "name": "Name", "description": "Description", "required_fields": ["name"]}, "discipline_settings": {"key_name": "Disciplines", "name": "Name", "description": "Description", "required_fields": ["name"]}, "grievances": {"key_name": "Grievances", "name": "Name", "description": "Description", "required_fields": ["name"]}, "units": {"key_name": "Units", "name": "Name", "description": "Description", "required_fields": ["name"]}, "meeting_types": {"key_name": "Delegate Meeting Types", "name": "Name", "description": "Description", "required_fields": ["name"]}, "genders": {"key_name": "Genders", "name": "Name", "description": "Description", "required_fields": ["name"]}, "leaves": {"started_at": "From", "ended_at": "To", "hours_used": "No. of hours used", "notes": "Notes", "required_fields": ["started_at", "leave_type", "hours_used"]}, "lodis": {"key_name": "<PERSON><PERSON>", "incident_date": "Date of incident", "return_date": "Date of return", "office_name": "Location", "notes": "Notes", "required_fields": ["office_id", "employee_id", "incident_date"]}, "users": {"key_name": "Users", "username": "Username", "email": "Email Address", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "password": "Password", "password_confirmation": "Password Confirmation", "required_fields": ["username", "email", "password", "password_confirmation", "role_id"]}, "roles": {"key_name": "Roles"}, "employees": {"key_name": "Members", "avatar": "Member Photo", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "do_not_mail": "Do Not Mail", "birthday": "DOB", "social_security_number": "Social Security Number", "veteran_status": "Veteran", "a_number": "Pass Number", "shield_number": "BSC Number", "placard_number": "Placard Number", "start_date": "TA Start Date", "prom_prov": "Prom Prov", "prom_perm": "Prom Perm", "member_since": "Member Since", "notes": "Notes", "janus_card": "<PERSON><PERSON>t Out", "staff_member": "Staff Member", "janus_card_opt_out_date": "<PERSON><PERSON> Opt Out Date", "cellphone": "Cell Phone", "home_phone": "Home Phone", "work_phone": "Work Phone", "email": "Email", "social_security_number_format": "4", "allow_multiple_present_status": "true", "required_fields": ["name", "first_name", "last_name", "a_number", "shield_number", "unit_id"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "a_number", "member_since"]}}, "contacts": {"contact_number": {"key_name": "Contact Number", "work_phone": "Work", "personal_phone": "Personal", "home_phone": "Home", "required_fields": []}, "email_address": {"key_name": "Email Address", "work_email": "Work", "personal_email": "Personal", "required_fields": []}}, "employee_employment_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "employment_status_id", "start_date"]}, "employee_departments": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["department_id", "employee_id"]}, "employee_sections": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["section_id", "department_id", "employee_id"]}, "employee_titles": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["department_id", "section_id", "title_id", "employee_id"]}, "employee_officer_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "officer_status_id", "start_date"]}, "employee_offices": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["office_id", "employee_id", "start_date"]}, "delegate_assignments": {"key_name": "Delegate Assignments", "delegate_employee_id": "Delegate Name", "start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["delegate_employee_id", "office_id", "employee_id", "start_date"]}, "employee_positions": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["position_id", "employee_id", "start_date"]}, "employee_ranks": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["rank_id", "employee_id", "start_date"]}, "employee_firearm_statuses": {"status_date": "Date", "firearm_type": "Type", "notes": "Notes", "required_fields": ["firearm_status_id", "employee_id"]}, "firearm_range_scores": {"key_name": "Firearm Range Scores", "test_type": "Test type", "test_date": "Date", "score": "Score", "notes": "Notes", "required_fields": ["test_date", "test_type", "score", "employee_id"]}, "employee_benefits": {"start_date": "Start Date", "end_date": "End Date", "description": "Description", "required_fields": ["benefit_id", "employee_id", "start_date"]}, "beneficiaries": {"key_name": "Beneficiaries", "name": "Name", "relationship": "Relationship", "address": "Address", "beneficiary_type": "Type", "percentage": "Percentage", "file": "Upload", "required_fields": ["name", "employee_id", "percentage"]}, "benefit_coverages": {"key_name": "Benefit Coverages", "name": "Name", "relationship": "Relationship", "social_security_number": "employees.social_security_number", "address": "Address", "birthday": "DOB", "expires_at": "Expiration", "required_fields": ["employee_id", "address", "employee_benefit_id", "birthday", "name", "social_security_number", "relationship"]}, "benefit_disbursements": {"key_name": "Benefit Disbursements", "year": "Year", "date": "Date", "reference_number": "Reference Number", "amount": "Amount", "notes": "Notes", "required_fields": ["employee_id", "amount", "employee_benefit_id", "payment_type_id", "date", "reference_number", "year"]}, "employee_pacfs": {"date": "Date", "amount": "Amount", "notes": "Notes", "required_fields": ["pacf_id", "employee_id"]}, "awards": {"key_name": "Awards", "name": "Type", "awarded_on": "Date", "description": "Description", "required_fields": ["employee_id", "name"]}, "employee_discipline_settings": {"discipline_setting_id": "Type", "date": "Date", "description": "Description", "files": "Files", "required_fields": ["employee_id", "discipline_setting_id"]}, "employee_grievances": {"date": "Date", "description": "Description", "files": "Files", "required_fields": ["employee_id", "grievance_id"]}, "employee_meeting_types": {"meeting_date": "Date", "attended": "Attended", "notes": "Notes", "required_fields": ["meeting_date", "meeting_type_id", "employee_id"]}, "uploads": {"key_name": "Uploads", "file": "File", "notes": "Notes", "required_fields": ["file", "employee_id"]}, "departments": {"key_name": "Departments", "name": "Name", "address": "Address", "phone": "Phone Number", "notes": "Notes", "required_fields": ["name"]}, "sections": {"key_name": "Sections", "name": "Name", "phone": "Phone Number", "notes": "Notes", "required_fields": ["name", "department_id"]}, "pacfs": {"key_name": "PAF", "name": "Name", "description": "Description", "required_fields": ["name"]}, "titles": {"key_name": "Titles", "name": "Name", "description": "Description", "title_code": "Title Code", "required_fields": ["department_id", "section_id", "name", "title_code"]}, "reports": {"single_employee": "Single Member", "sick_bank": "Sick Bank", "lodi": "<PERSON><PERSON>", "union_meetings": "Union Meetings", "employee_delegate_assignment": "Member Delegate Assignment", "janus": "<PERSON><PERSON>", "columns": "Report Columns", "started_at": "From Date", "ended_at": "To Date", "employee_summary": "Member Summary", "total_summary": "Total Summary", "active_date": "Active Date", "inactive_date": "Inactive Date", "disbursement_date": "Disbursement Date", "disbursement_year": "Disbursement Year", "disbursements_not_found": "Benefit disbursements not found.", "name": "Name", "date_in": "Date In", "date_out": "Date Out", "used_hours": "Used Hours", "lodi_return_to_work_status": "Return to Work Status", "date_to": "Date To", "date_from": "Date From", "excel_report": "Generate Excel Report", "pdf_report": "Generate PDF Report", "single_mailing_label": "Generate Single Mailing Labels", "multiple_mailing_label": "Generate Multiple Mailing Labels", "payment_date_from": "Payment Date From", "payment_date_to": "Payment Date To", "show_disbursements": "Show Disbursements", "delegates": "Delegates", "meetings": "Meetings"}, "common_terms": {"employee_analytics": "Analytics", "sick_bank": "Sick Banks", "personal": "Personal", "vacation": "Vacations", "over_time": "Overtime", "comp_time": "Comp Time"}}, "ui": {"employees": {"key_name": "Member List", "is_search": ["employees"], "table_headers": ["name", "officer_status_name", "shield_number", "placard_number", "birthday", "start_date", "address"], "tabs": ["profile", "pacfs", "firearm_statuses", "employee_analytics", "benefits", "awards", "discipline_settings", "grievances", "meeting_types", "uploads"], "profile": {"key_name": "Profile", "additional_details": ["first_name", "middle_name", "last_name"], "employees": ["avatar", "name", "address", "do_not_mail", "birthday", "social_security_number", "genders", "marital_statuses", "veteran_status", "a_number", "units", "shield_number", "placard_number", "start_date", "prom_prov", "prom_perm", "member_since", "notes"], "contacts": [{"contact_number": ["work_phone", "personal_phone", "home_phone"]}, {"email_address": ["work_email", "personal_email"]}], "others": ["employee_employment_statuses", "employee_departments", "employee_sections", "employee_titles", "employees.title_code", "employees.janus_card", "employees.janus_card_opt_out_date", "employee_officer_statuses", "employee_offices", "delegate_assignments", "employee_positions", "employee_ranks", "employees.staff_member"]}}, "settings": {"key_name": "Settings", "tabs": ["benefits", "offices", "departments", "sections", "pacfs", "titles", "firearm_statuses", "ranks", "employment_statuses", "officer_statuses", "marital_statuses", "positions", "payment_types", "discipline_settings", "grievances", "meeting_types", "genders", "units"]}, "users": {"is_search": ["users"], "table_headers": ["username", "name", "email", "roles"]}, "reports": {"key_name": "Reports", "tabs": ["single_employee", "benefits", "sick_bank", "lodi", "union_meetings", "disciplines", "grievances", "employee_delegate_assignment", "janus"], "single_employee": {"primary_filters": [["reports.columns"], ["employees"]], "secondary_filters": [["departments", "sections"], ["titles", "offices"], ["ranks", "employment_statuses"], ["officer_statuses", "employees.email"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.email", "employees.a_number"], ["employees.placard_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.member_since_from", "employees.member_since_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["units", ""]], "columns": ["employees", "offices", "ranks", "titles", "employment_statuses", "officer_statuses", "employees.email", "firearm_statuses", "marital_statuses", "positions", "employees.social_security_number", "employees.placard_number", "employees.shield_number", "employees.birthday", "employees.home_phone", "employees.cellphone", "employees.work_phone", "employees.a_number", "employees.city", "employees.state", "employees.zipcode", "employees.start_date", "employees.apartment", "employees.street", "departments", "sections", "pacfs", "employees.member_since", "units", "employees.prom_prov", "employees.prom_perm"], "default_columns": ["employees", "employees.shield_number", "ranks", "employment_statuses", "offices", "firearm_statuses", "marital_statuses"], "actions": ["single_mailing_label", "multiple_mailing_label", "mailing_label", "excel_report", "pdf_report"]}, "benefits": {"primary_filters": [["benefits"], ["reports.active_date", "reports.inactive_date"], ["payment_types", "reports.disbursement_year"], ["reports.payment_date_from", "reports.payment_date_to"], ["reports.show_disbursements", ""]], "secondary_filters": [["employees"], ["departments", "sections"], ["titles", "offices"], ["ranks", "employment_statuses"], ["officer_statuses", "employees.email"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.placard_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.member_since_from", "employees.member_since_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.a_number"]], "actions": ["pdf_report"]}, "sick_bank": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["departments", "sections"], ["titles", "offices"], ["ranks", "employment_statuses"], ["officer_statuses", "employees.email"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.placard_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.member_since_from", "employees.member_since_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.a_number"]], "actions": ["excel_report", "pdf_report"]}, "lodi": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["departments", "sections"], ["titles", "offices"], ["ranks", "employment_statuses"], ["officer_statuses", "employees.email"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.placard_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.member_since_from", "employees.member_since_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.a_number"]], "actions": ["excel_report", "pdf_report"]}, "union_meetings": {"primary_filters": [["meeting_types"], ["reports.started_at", "reports.ended_at"], ["reports.delegates"], ["reports.meetings"]], "secondary_filters": [["employees"], ["departments", "sections"], ["titles", "offices"], ["ranks", "employment_statuses"], ["officer_statuses", "employees.email"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.placard_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.member_since_from", "employees.member_since_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.a_number"]], "actions": ["excel_report", "pdf_report"]}, "disciplines": {"primary_filters": [["discipline_settings"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["departments", "sections"], ["titles", "offices"], ["ranks", "employment_statuses"], ["officer_statuses", "employees.email"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.placard_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.member_since_from", "employees.member_since_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["units", "employees.a_number"]], "actions": ["excel_report", "pdf_report"]}, "grievances": {"primary_filters": [["grievances"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["departments", "sections"], ["titles", "offices"], ["ranks", "employment_statuses"], ["officer_statuses", "employees.email"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.placard_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.member_since_from", "employees.member_since_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["units", "employees.a_number"]], "actions": ["excel_report", "pdf_report"]}, "employee_delegate_assignment": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["departments", "sections"], ["titles", "offices"], ["ranks", "employment_statuses"], ["officer_statuses", "employees.email"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.placard_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.member_since_from", "employees.member_since_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.a_number"]], "actions": ["pdf_report"]}, "janus": {"primary_filters": [["employees.janus_card"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["departments", "sections"], ["titles", "offices"], ["ranks", "employment_statuses"], ["officer_statuses", "employees.email"], ["firearm_statuses", "marital_statuses"], ["positions", "employees.social_security_number"], ["employees.placard_number", "employees.shield_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.member_since_from", "employees.member_since_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["units", "employees.a_number"]], "actions": ["excel_report", "pdf_report"]}}, "notification": {"key_name": "Notifications", "analytics": "Analytics", "is_search": true, "allow_sms_attachments": true, "filters": [["employees"], ["departments", "sections"], ["titles", "employment_statuses"], ["offices", "ranks"], ["officer_statuses", "positions"], ["firearm_statuses", "marital_statuses"], ["employees.a_number", "units"], ["employees.birthday_from", "employees.birthday_to"], ["employees.member_since_from", "employees.member_since_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.shield_number", "employees.placard_number"], ["employees.city", "employees.state"], ["employees.zipcode", ""]]}}}